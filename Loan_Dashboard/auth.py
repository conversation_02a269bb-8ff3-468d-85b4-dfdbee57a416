import streamlit as st
st.set_page_config(page_title="Seeds by Liberty", page_icon="🌱", layout="wide")
import pandas as pd
import psycopg2
import os
import time
import hashlib
from hashlib import sha256
from dotenv import load_dotenv
from queries import loans_df_, sup_df_, merchants_df_, wallet_trans_df_

############################################################################### Gets queries #######################################################################################

loans_df = loans_df_.copy()
sup_df = sup_df_.copy()
merchants_df = merchants_df_.copy()
wallet_trans_df = wallet_trans_df_.copy()

################################################################################ Lotto Data Cleaning #####################################################################################

@st.cache_data(show_spinner=False)
def link_lotto(wallet_trans_df):
    # Title-case specific string columns
    for col in [
        "vertical_full_name",
        "supervisor_name",
        "agent_name"
    ]:
        if col in wallet_trans_df.columns:
            wallet_trans_df[col] = wallet_trans_df[col].astype(str).str.title()
    return wallet_trans_df

lotto_df = link_lotto(wallet_trans_df)

################################################################################ Loan Data Cleaning #####################################################################################

@st.cache_data(show_spinner=False)
def merge_loans(loans_df):
    # Convert 'date_disbursed' to datetime
    loans_df["date_disbursed"] = pd.to_datetime(loans_df["date_disbursed"])
    loans_df["start_date"] = pd.to_datetime(loans_df["start_date"])
    loans_df["end_date"] = pd.to_datetime(loans_df["end_date"])

    # Convert 'date_disbursed' to short date format
    loans_df["date_disbursed"] = loans_df["date_disbursed"].dt.strftime("%Y-%m-%d")

    # Past maturity (0 if COMPLETED, else difference in days)
    loans_df['past_maturity'] = loans_df.apply(
        lambda row: 0 if row['status'] == 'COMPLETED' else max((pd.Timestamp.today().normalize() - row['end_date']).days, 0),
        axis=1
    )

    # Sort by 'date_disbursed'
    loans_df = loans_df.sort_values(by="date_disbursed")

    # Merge supervisor and loans dataset
    loans_df = sup_df.merge(
        loans_df, how="right", right_on="customer_user_id", left_on="team_user_id"
    )

    # Merge merchanats and loans
    loans_df = merchants_df.merge(
        loans_df, how="right", right_on="customer_user_id", left_on="merchant_user_id"
    )

    # Title-case specific string columns
    for col in [
        "merchant_agents",
        "merchant_users",
        "sup_name",
        "sub_location",
        "vertical_lead_name",
        "verticals",
        "users_full_name",
        "agent_full_name",
    ]:
        if col in loans_df.columns:
            loans_df[col] = loans_df[col].astype(str).str.title()

    return loans_df

# Loads data
df = merge_loans(loans_df)

################################################################################## SET LOGIN PAGE ##################################################################################

st.markdown(
    """
    <style>
    /* Hide the top toolbar (includes hamburger, deploy, settings) */
    div[data-testid="stToolbar"] {
        visibility: hidden;
        height: 0px;
    }

    header[data-testid="stHeader"] {
        visibility: hidden;
        height: 0px;
    }s

    /* Just in case: hide the settings icon explicitly */
    [data-testid="stSettingsIcon"] {
        display: none !important;
    }

    /* Hide deploy status if it's showing */
    div[data-testid="stDeploymentStatus"] {
        display: none !important;
    }

    /* Hide all icon buttons in header */
    header button[title] {
        display: none !important;
    }
    </style>
    """,
    unsafe_allow_html=True,
)

############################################################################ db connection #######################################################################################

load_dotenv()

def get_db_connection():
    try:
        return psycopg2.connect(os.getenv("NEON_DB_URL"))
    except psycopg2.Error as e:
        print(f"Error connecting to the database: {e}")
        return None

def init_db():
    try:
        conn = get_db_connection()
        if conn is None:
            raise Exception("Failed to connect to the database.")

        cur = conn.cursor()

        # Create the table if it doesn't exist
        cur.execute(
            """
            CREATE TABLE IF NOT EXISTS users (
                username TEXT PRIMARY KEY,
                password TEXT NOT NULL,
                role TEXT NOT NULL
            )
        """
        )

        # Ensure the new columns exist (add them if they don't)
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS merchant_agents TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS merchant_agent_email TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS sup_name TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS sup_email TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS sub_location TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS vertical_lead_name TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS vertical_lead_email TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS verticals TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS supervisor_name TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS supervisor_email TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS vertical_full_name TEXT")
        cur.execute("ALTER TABLE users ADD COLUMN IF NOT EXISTS vertical_email TEXT")

        conn.commit()
        cur.close()
        conn.close()
    except psycopg2.Error as e:
        print(f"Database error occurred during initialization: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

def hash_password(password):
    try:
        return sha256(password.encode()).hexdigest()
    except Exception as e:
        print(f"Error hashing password: {e}")
        return None

def is_username_taken(username):
    try:
        conn = get_db_connection()
        if conn is None:
            raise Exception("Failed to connect to the database.")

        cur = conn.cursor()
        cur.execute("SELECT 1 FROM users WHERE username = %s", (username,))
        exists = cur.fetchone() is not None
        cur.close()
        conn.close()
        return exists
    except psycopg2.Error as e:
        print(f"Database error occurred while checking username: {e}")
        return False
    except Exception as e:
        print(f"An error occurred: {e}")
        return False

def create_user(
    username,
    password,
    role,
    merchant_agents=None,
    merchant_agent_email=None,
    sup_name=None,
    sup_email=None,
    sub_location=None,
    vertical_lead_name=None,
    vertical_lead_email=None,
    verticals=None,
    supervisor_name=None,
    supervisor_email=None,
    vertical_full_name=None,
    vertical_email=None,    
):
    try:
        conn = get_db_connection()
        if conn is None:
            raise Exception("Failed to connect to the database.")

        cur = conn.cursor()
        hashed_pw = hash_password(password)
        if not hashed_pw:
            raise Exception("Password hashing failed.")

        cur.execute(
            """
            INSERT INTO users (username, password, role, merchant_agents, merchant_agent_email, sup_name, sup_email, sub_location, vertical_lead_name, vertical_lead_email, verticals, supervisor_name, supervisor_email, vertical_full_name, vertical_email)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """,
            (
                username,
                hashed_pw,
                role,
                merchant_agents,
                merchant_agent_email,
                sup_name,
                sup_email,
                sub_location,
                vertical_lead_name,
                vertical_lead_email,
                verticals,
                supervisor_name,
                supervisor_email,
                vertical_full_name,
                vertical_email
            ),
        )
        conn.commit()
        cur.close()
        conn.close()
    except psycopg2.Error as e:
        print(f"Database error occurred while creating user: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

def verify_user(username, password):
    try:
        conn = get_db_connection()
        if conn is None:
            raise Exception("Failed to connect to the database.")

        cur = conn.cursor()
        cur.execute(
            "SELECT password, role, merchant_agents, merchant_agent_email, sup_name, sup_email, sub_location, vertical_lead_name, vertical_lead_email, verticals, supervisor_name, supervisor_email, vertical_full_name, vertical_email FROM users WHERE username = %s",
            (username,),
        )
        result = cur.fetchone()
        cur.close()
        conn.close()

        if result and hash_password(password) == result[0]:
            return {
                "success": True,
                "role": result[1],
                "merchant_agents": result[2],
                "merchant_agent_email": result[3],
                "sup_name": result[4],
                "sup_email": result[5],
                "sub_location": result[6],
                "vertical_lead_name": result[7],
                "vertical_lead_email": result[8],
                "verticals": result[9],
                "supervisor_name": result[10],
                "supervisor_email": result[11],
                "vertical_full_name": result[12],
                "vertical_email": result[13],
            }
        return {"success": False}
    except psycopg2.Error as e:
        print(f"Database error occurred while verifying user: {e}")
        return {"success": False}
    except Exception as e:
        print(f"An error occurred: {e}")
        return {"success": False}

def get_user_details(username):
    try:
        conn = get_db_connection()
        if conn is None:
            raise Exception("Failed to connect to the database.")

        cur = conn.cursor()
        cur.execute(
            "SELECT role, merchant_agents, merchant_agent_email, sup_name, sup_email, sub_location, vertical_lead_name, vertical_lead_email, verticals, supervisor_name, supervisor_email, vertical_full_name, vertical_email FROM users WHERE username = %s",
            (username,),
        )
        result = cur.fetchone()
        cur.close()
        conn.close()

        return (
            {
                "role": result[0],
                "merchant_agents": result[1],
                "merchant_agent_email": result[2],
                "sup_name": result[3],
                "sup_email": result[4],
                "sub_location": result[5],
                "vertical_lead_name": result[6],
                "vertical_lead_email": result[7],
                "verticals": result[8],
                "supervisor_name": result[9],
                "supervisor_email": result[10],
                "vertical_full_name": result[11],
                "vertical_email": result[12],
            }
            if result
            else None
        )
    except psycopg2.Error as e:
        print(f"Database error occurred while fetching user details: {e}")
        return None
    except Exception as e:
        print(f"An error occurred: {e}")
        return None

def reset_password(email, new_password):
    try:
        conn = get_db_connection()
        if conn is None:
            raise Exception("Failed to connect to the database.")

        cur = conn.cursor()

        # Check if the email exists
        cur.execute("SELECT * FROM users WHERE username = %s", (email,))
        user = cur.fetchone()

        if user:
            # Hash the new password and update
            hashed_password = hashlib.sha256(new_password.encode()).hexdigest()
            cur.execute(
                "UPDATE users SET password = %s WHERE username = %s",
                (hashed_password, email),
            )
            conn.commit()
            cur.close()
            conn.close()
            return {"success": True}
        else:
            return {"success": False, "message": "No account found with that email."}
    except psycopg2.Error as e:
        print(f"Database error occurred while resetting password: {e}")
        return {"success": False, "message": str(e)}
    except Exception as e:
        print(f"An error occurred: {e}")
        return {"success": False, "message": str(e)}

def forgot_password_form():
    st.markdown(
        """
        <div style='text-align:center;'>
            <h2>Reset Your Password</h2>
        </div>
        """,
        unsafe_allow_html=True,
    )
    st.write("")
    c1, c2, c3 = st.columns(3)
    with c2:
        with st.form(key="reset_form"):
            email = st.text_input(
                "📧 Enter your registered Email", placeholder="<EMAIL>"
            )
            new_password = st.text_input("🔒 Enter New Password", type="password")
            confirm_password = st.text_input("🔒 Confirm New Password", type="password")
            submit_button = st.form_submit_button("Reset Password")

            if submit_button:
                if new_password != confirm_password:
                    st.error("Passwords do not match. Please try again.")
                elif not email or not new_password:
                    st.error("Please fill in all fields.")
                else:
                    result = reset_password(email, new_password)
                    if result["success"]:
                        st.success(
                            "Password reset successful! Please login with your new password."
                        )
                        time.sleep(2)
                        st.session_state["page"] = "login"
                        st.rerun()
                    else:
                        st.error(result["message"])
        if st.button("Go Back"):
            st.session_state["page"] = "login"
            st.rerun()

def account_creation_form():
    c1, c2, c3 = st.columns(3)
    with c2:
        st.markdown(
            """
            <div style='text-align: center; padding: 35px;'>
                <h1>Create Your Account</h1>
            </div>
        """,
            unsafe_allow_html=True,
        )

        # Seeds and Pennies admin emails
        admin_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]

        # Winwise Retail admin emails
        lotto_admin_email = [
            "<EMAIL>"
        ]

        # role = st.selectbox(
        #     "Select Your Role",
        #     ["Admin", "Lotto Admin", "Vertical Lead", "Lotto Vertical Lead", "Supervisor", "Lotto Supervisor", "Merchant"],
        #     index=None,
        #     placeholder="Select role",
        # )

        # Select category (Loans or Lotto)
        category = st.selectbox(
            "Select Liberty Brand",
            ["Seeds and Pennies", "WinWise"],
            index=None,
            placeholder="Select department"
        )

        # Roles based on selected category
        if category == "Seeds and Pennies":
            role = st.selectbox(
                "Select Your Role",
                ["Admin", "Vertical Lead", "Supervisor", "Merchant"],
                index=None,
                placeholder="Select Loan Role"
            )
        elif category == "WinWise":
            role = st.selectbox(
                "Select Your Role",
                ["Lotto Admin", "Lotto Vertical Lead", "Lotto Supervisor"],
                index=None,
                placeholder="Select Lotto Role"
            )

        username = st.text_input("E-mail", placeholder="Enter your e-mail")
        password = st.text_input(
            "Password", type="password", placeholder="Enter password"
        )

        # Enforces users to select a brand, fill their required mails and set a password to be able to create an account
        if category:
            if role and username:
                if role == "Admin":
                    if username not in admin_emails:
                        st.error("Not Recognised")
                    elif is_username_taken(username):
                        st.error("Admin already exists.")
                    else:
                        st.success("Admin verified!")
                elif role == "Lotto Admin":
                    if username not in lotto_admin_email:
                        st.error("Not Recognised")
                    elif is_username_taken(username):
                        st.error("Retail Admin already exists.")
                    else:
                        st.success("Retail Admin verified!")
                elif role == "Supervisor":
                    if username not in df["sup_email"].values:
                        st.error("Not Recognised")
                    elif is_username_taken(username):
                        st.error("Supervisor already exists!")
                    else:
                        supervisor_info = df[df["sup_email"] == username].iloc[0]
                        sup_name = st.selectbox(
                            "Supervisor Name",
                            [supervisor_info["sup_name"]],
                            index=0,
                            disabled=True,
                        )
                        sub_location = st.selectbox(
                            "Branch",
                            [supervisor_info["sub_location"]],
                            index=0,
                            disabled=True,
                        )
                        st.success("Supervisor Verified!")
                elif role == "Lotto Supervisor":
                    if username not in lotto_df['supervisor_email'].values:
                        st.error("Not Recognised")
                    elif is_username_taken(username):
                        st.error("Lotto Supervisor already exists!")
                    else:
                        lotto_supervisor_info = lotto_df[lotto_df['supervisor_email'] == username].iloc[0]
                        supervisor_name = st.selectbox(
                            "Lotto Supervisor Name",
                            [lotto_supervisor_info["supervisor_name"]],
                            index=0,
                            disabled=True,
                        )
                        st.success("Lotto Supervisor Verified!")
                elif role == "Lotto Vertical Lead":
                    if username not in lotto_df["vertical_email"].values:
                        st.error("Not Recognised")
                    elif is_username_taken(username):
                        st.error("Lotto Vertical Lead already exists!")
                    else:
                        lotto_vertical_lead_info = lotto_df[lotto_df["vertical_email"] == username].iloc[0]
                        lotto_vertical_lead_name = st.selectbox("Vertical Lead Name",[lotto_vertical_lead_info["vertical_full_name"]],index=0,disabled=True,)
                        st.success("Lotto Vertical Lead Verified!")
                elif role == "Merchant":
                    if username not in df["merchant_agent_email"].values:
                        st.error("Not Recognised")
                    elif is_username_taken(username):
                        st.error("Merchant already exists!")
                    else:
                        merchant_info = df[df["merchant_agent_email"] == username].iloc[0]
                        merchant_agents = st.selectbox(
                            "Merchant Name",
                            [merchant_info["merchant_agents"]],
                            index=0,
                            disabled=True,
                        )
                        st.success("Merchant Verified!")
                elif role == "Vertical Lead":
                    if username not in df["vertical_lead_email"].values:
                        st.error("Not Recognised")
                    elif is_username_taken(username):
                        st.error("Vertical Lead already exists!")
                    else:
                        vertical_lead_info = df[df["vertical_lead_email"] == username].iloc[0]
                        vertical_lead_name = st.selectbox(
                            "Vertical Lead Name",
                            [vertical_lead_info["vertical_lead_name"]],
                            index=0,
                            disabled=True,
                        )
                        verticals = st.selectbox(
                            "Vertical Lead Vertical",
                            [vertical_lead_info["verticals"]],
                            index=0,
                            disabled=True,
                        )
                        st.success("Vertical Lead Verified!")

        # AMkes button for users to create accoun and saves their details to the DB
        if st.button("Create Account"):
            if category:
                if username and password and role:
                    if is_username_taken(username):
                        st.error("Email already registered!")
                    else:
                        merchant_agents = (merchant_info["merchant_agents"] if role == "Merchant" else None )
                        merchant_agent_email = (merchant_info["merchant_agent_email"] if role == "Merchant" else None)
                        sup_name = (supervisor_info["sup_name"] if role == "Supervisor" else None)
                        sup_email = (supervisor_info["sup_email"] if role == "Supervisor" else None)
                        sub_location = (supervisor_info["sub_location"] if role == "Supervisor" else None)
                        supervisor_name = (lotto_supervisor_info["supervisor_name"] if role == "Lotto Supervisor" else None)
                        supervisor_email = (lotto_supervisor_info["supervisor_email"] if role == "Lotto Supervisor" else None)
                        vertical_lead_name = (vertical_lead_info["vertical_lead_name"] if role == "Vertical Lead" else None)
                        vertical_lead_email = (vertical_lead_info["vertical_lead_email"] if role == "Vertical Lead" else None)
                        verticals = (vertical_lead_info["verticals"] if role == "Vertical Lead" else None)
                        lotto_vertical_lead_name = (lotto_vertical_lead_info["vertical_full_name"] if role == "Lotto Vertical Lead" else None)
                        lotto_vertical_lead_email = (lotto_vertical_lead_info["vertical_email"] if role == "Lotto Vertical Lead" else None)
                        create_user(
                            username,
                            password,
                            role,
                            merchant_agents,
                            merchant_agent_email,
                            sup_name,
                            sup_email,
                            sub_location,
                            vertical_lead_name,
                            vertical_lead_email,
                            verticals,
                            supervisor_name,
                            supervisor_email,
                            lotto_vertical_lead_name,
                            lotto_vertical_lead_email
                        )
                        st.success("Account created! You can now log in.")
                        st.session_state["page"] = "login"
                        st.rerun()
                else:
                    st.error("Please fill all fields.")
            else:
                st.error("Please fill all fields.")

        # Takes users back to login page
        if st.button("Go Back"):
            st.session_state["page"] = "login"
            st.rerun()

def login_form():
    st.markdown(
        """
        <div style='text-align:center;'>
            <h1 style='text-shadow: 2px 2px 5px rgba(50, 50, 50, 0.8);'><strong>Liberty Assured</strong></h1>
            <p style='text-shadow: 2px 2px 5px rgba(50, 50, 50, 0.8);'>...be liberated</p>
        </div>
        """,
        unsafe_allow_html=True,
    )
    st.write("")

    # This allows us to centralise the login form page using columns
    c1, c2, c3 = st.columns((1, 2, 1))
    with c2:
        with st.form(key="login_form"):
            st.markdown(
                """<div style='text-align: center; padding: 25px;'>
                            <h3>Login</h3>
                        </div>""",
                        unsafe_allow_html=True,
                        )
            st.markdown("<hr style='width: 80%; margin: auto; border: 1px solid #ddd; margin-bottom: 7%;'>",unsafe_allow_html=True,)

            username = st.text_input("📧 E-mail/Username", placeholder="Enter your e-mail")
            st.write("")
            password = st.text_input("🔒 Password", type="password", placeholder="Password")
            st.write("")
 
            if st.form_submit_button("Login"):
                result = verify_user(username, password)
                if result["success"]:
                    # Map admin emails to their names
                    admin_names = {
                        "<EMAIL>": "Veck_thor ✨",
                        "<EMAIL>": "Dr. Oti",
                        "<EMAIL>": "Dami Afolayan",
                        "<EMAIL>": "Liberty",
                        "<EMAIL>": "Nosiru Sonoiki",
                        "<EMAIL>": "Timileyin"
                    }

                    # Map lotto admin emails to their names
                    lotto_admin_names = {
                        "<EMAIL>": "Liberty Admin",
                    }

                    # Check role and fetch the appropriate name and details
                    if result["role"] == "Admin":
                        user_name = admin_names.get(username, "Admin")
                        vertical_lead_name = None
                        verticals = None
                        merchant_name = None
                        sup_name = None
                        sub_location = None
                        supervisor_name = None
                        lotto_vertical_lead_name = None
                    elif result["role"] == "Lotto Admin":
                        user_name = lotto_admin_names.get(username, "Lotto Admin")
                        vertical_lead_name = None
                        verticals = None
                        merchant_name = None
                        sup_name = None
                        sub_location = None
                        supervisor_name = None
                        lotto_vertical_lead_name = None
                    elif result["role"] == "Supervisor":
                        supervisor_info = df[df["sup_email"] == username].iloc[0]
                        user_name = supervisor_info["sup_name"]
                        vertical_lead_name = None
                        verticals = None
                        merchant_name = None
                        sup_name = user_name
                        sub_location = supervisor_info["sub_location"]
                        supervisor_name = None
                        lotto_vertical_lead_name = None
                    elif result["role"] == "Lotto Supervisor":
                        lotto_supervisor_info = lotto_df[lotto_df["supervisor_email"] == username].iloc[0]
                        user_name = lotto_supervisor_info["supervisor_name"]
                        vertical_lead_name = None
                        verticals = None
                        merchant_name = None
                        sup_name = None
                        sub_location = None
                        supervisor_name = user_name
                        lotto_vertical_lead_name = None
                    elif result["role"] == "Lotto Vertical Lead":
                        lotto_vertical_lead_info = lotto_df[lotto_df["vertical_email"] == username].iloc[0]
                        user_name = lotto_vertical_lead_info["vertical_full_name"]
                        vertical_lead_name = None
                        verticals = None
                        merchant_name = None
                        sup_name = None
                        sub_location = None
                        supervisor_name = None
                        lotto_vertical_lead_name = user_name
                    elif result["role"] == "Merchant":
                        merchant_info = df[df["merchant_agent_email"] == username].iloc[0]
                        user_name = merchant_info["merchant_agents"]
                        vertical_lead_name = None
                        verticals = None
                        merchant_name = user_name
                        sup_name = None
                        sub_location = None
                        supervisor_name = None
                        lotto_vertical_lead_name = None
                    elif result["role"] == "Vertical Lead":
                        vertical_lead_info = df[df["vertical_lead_email"] == username].iloc[0]
                        user_name = vertical_lead_info["vertical_lead_name"]
                        vertical_lead_name = user_name
                        verticals = vertical_lead_info["verticals"]
                        merchant_name = None
                        sup_name = None
                        sub_location = None
                        supervisor_name = None
                        lotto_vertical_lead_name = None

                    # Update session state
                    st.session_state.update(
                        {
                            "logged_in": True,
                            "role": result["role"],
                            "username": username,
                            "sup_name": sup_name,
                            "merchant_agents": merchant_name,
                            "sub_location": sub_location,
                            "vertical_lead_name": vertical_lead_name,
                            "verticals": verticals,
                            "supervisor_name": supervisor_name,
                            "vertical_full_name": lotto_vertical_lead_name
                        }
                    )
                    st.success(f"Access Granted! Welcome, {user_name} 💫")
                    time.sleep(2)
                    st.rerun()
                else:
                    st.error("Invalid credentials.")
            st.write("")

        # Two centered buttons in columns
        b1, b2 = st.columns(2)

        with b1:
            st.markdown(
                """
                <style>
                div.stButton > button {
                    width: 100%;
                }
                </style>
                """,
                unsafe_allow_html=True,
            )
            if st.button("🔑 Forgot Password"):
                st.session_state["page"] = "forgot_password"
                st.rerun()

        with b2:
            st.markdown(
                """
                <style>
                div.stButton > button {
                    width: 100%;
                }
                </style>
                """,
                unsafe_allow_html=True,
            )
            if st.button("➕ Create Account"):
                st.session_state["page"] = "create_account"
                st.rerun()

    st.markdown("---")

    st.markdown(
        """
        <style>
            .footer {
                text-align: center;
                background-color: rgba(0, 151, 167, 0.2);  /* Adjust the opacity here (0.0 - 1.0) */
                color: grey;
                padding:5px;
                position: center;
                bottom: 0;
                width: 100%;
            }

            .footer h3 {
                font-size: 20px;
                font-family: 'Arial', sans-serif;
                letter-spacing: 3px;
                margin: 0;
            }

            .footer h6 {
                font-size: 14px;
                font-family: 'Arial', sans-serif;
                margin-top: 5px;
                letter-spacing: 6px;
                font-weight: lighter;
            }
        </style>
        <div class="footer">
            <h3>P-E-R-F-E-C-T-(ion)</h3>
            <h6>Passion, Excellence, Resilience, Focus, Empowerment, Commitment, and Tenacity</h6>
        </div>
        """,
        unsafe_allow_html=True,
    )

    st.markdown("---")

def show_dashboard():

    role = st.session_state.get("role")
    username = st.session_state.get("username")
    user_details = get_user_details(username)

    # Apply role-based filtering
    if role == "Supervisor":
        st.sidebar.markdown(
            f"""<div style='text-align:center;'><h6><strong>Dashboard for {user_details['sup_name']} in {user_details['sub_location']} branch</strong></h6></div>""",
            unsafe_allow_html=True,
        )
        filtered_data = df[df["sup_name"] == user_details["sup_name"]]
        from main import show_user_dashboard

        show_user_dashboard(filtered_data)

    elif role == "Lotto Supervisor":
        st.sidebar.markdown(
            f"""<div style='text-align:center;'><h6><strong>Dashboard for {user_details['supervisor_name']}</strong></h6></div>""",
            unsafe_allow_html=True,
        )
        filtered_lotto_df = lotto_df[lotto_df["supervisor_name"] == user_details["supervisor_name"]]
        from retail_main import show_lotto_user_dashboard

        show_lotto_user_dashboard(filtered_lotto_df)

    elif role == "Lotto Vertical Lead":
        st.sidebar.markdown(
            f"""<div style='text-align:center;'><h6><strong>Dashboard for {user_details['vertical_full_name']}</strong></h6></div>""",
            unsafe_allow_html=True,
        )
        filtered_lotto_df = lotto_df[lotto_df["vertical_full_name"] == user_details["vertical_full_name"]]
        from retail_main import show_lotto_user_dashboard

        show_lotto_user_dashboard(filtered_lotto_df)

    elif role == "Merchant":
        st.sidebar.markdown(
            f"""<div style='text-align:center;'><h6><strong>Dashboard for Merchant {user_details['merchant_agents']}</strong></h6></div>""",
            unsafe_allow_html=True,
        )
        filtered_data = df[df["merchant_agents"] == user_details["merchant_agents"]]
        from user import show_merchant_dashboard

        show_merchant_dashboard(filtered_data)

    elif role == "Vertical Lead":
        st.sidebar.markdown(
            f"""<div style='text-align:center;'><h6><strong>Dashboard for {user_details['vertical_lead_name']} in {user_details['verticals']} vertical</strong></h6></div>""",
            unsafe_allow_html=True,
        )
        filtered_data = df[
            df["vertical_lead_name"] == user_details["vertical_lead_name"]
        ]
        from main import show_user_dashboard

        show_user_dashboard(filtered_data)

    elif role == "Admin":
        st.sidebar.markdown(
            """<div style='text-align:center';><h6><strong>Admin Dashboard</strong></h6></div>""",
            unsafe_allow_html=True,
        )
        from admin import show_admin_dashboard

        show_admin_dashboard(df)

    elif role == "Lotto Admin":
        st.sidebar.markdown(
            """<div style='text-align:center';><h6><strong>Retail Admin Dashboard</strong></h6></div>""",
            unsafe_allow_html=True,
        )
        from retail import show_retail_dashboard

        show_retail_dashboard(lotto_df)

def main():
    init_db()  # Initialize database on startup
    if "logged_in" not in st.session_state:
        st.session_state["logged_in"] = False
    if "page" not in st.session_state:
        st.session_state["page"] = "login"
    if st.session_state["logged_in"]:
        show_dashboard()
    else:
        if st.session_state["page"] == "create_account":
            account_creation_form()
        elif st.session_state["page"] == "forsgot_password":
            forgot_password_form()
        else:
            login_form()

if __name__ == "__main__":
    main()
