import pandas as pd
import numpy as np
from datetime import timedelta, date, datetime
import warnings
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
import time
import importlib
import sys
import types
import base64
import os
import queries
from queries import holiday_, repayment_df_, sav_transaction_df_, loan_saving_df_, escrow_df_, staff_savings_trans_, monthly_repayments_, disbursement_4_last_month_, earnings_, loginactivity_, sup_df_, merchants_df_, loans_df_
from cache_manager import initialize_auto_cache_clearing, display_cache_status, manual_cache_clear
warnings.filterwarnings('ignore')

def show_admin_dashboard(df):
    # Initialize automatic cache clearing and check if cache was just cleared
    cache_just_cleared = initialize_auto_cache_clearing()

    # Gets dataframe
    filtered_data = df
    holiday = holiday_
    repayment_df = repayment_df_
    sav_transaction_df = sav_transaction_df_
    loan_saving_df = loan_saving_df_
    escrow_df = escrow_df_
    staff_savings_trans = staff_savings_trans_
    monthly_repayments = monthly_repayments_
    disbursement_4_last_month = disbursement_4_last_month_
    earnings = earnings_
    loginactivity = loginactivity_
    sup_df = sup_df_
    merchants_df = merchants_df_
    loans_df = loans_df_

    # sets logo
    st.logo("Seeds logo2.png", size="large")

    # Sets background image and color
    @st.cache_data(show_spinner=False)
    def set_background(image_path):
        if not os.path.exists(image_path):
            st.error(
                f"Image '{image_path}' not found. Ensure the file is in the correct directory."
            )
            return

        with open(image_path, "rb") as f:
            encoded_image = base64.b64encode(f.read()).decode()

        background_style = f"""
        <style>
        /* Darker background with solid dark blue */
        .stApp {{
            background: linear-gradient(to bottom, #00001, #000015); /* Darker blue */
        }}

        /* Sidebar styling - dark grey */
        [data-testid="stSidebar"] {{
            background-color: #33333 !important; /* dark grey */
        }}

        /* Centered transparent text image */
        .background-container {{
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40%; /* Adjust width if needed */
            text-align: center;
            z-index: 0; /* Ensure image is behind the dashboard and infront of the background color */
        }}

        .background-container img {{
            width: 100%;
            height: auto;
            opacity: 0.1; /* Set to minmal visibility */
        }}
        </style>

        <div class="background-container">
            <img src="data:image/png;base64,{encoded_image}">
        </div>
        """

        st.markdown(background_style, unsafe_allow_html=True)

    # Call the function with your image
    set_background("Seeds logo2.png")

    st.sidebar.markdown('---')

    ########################################################################### Create date range #######################################################################################

    # all_time button
    all_time = st.sidebar.checkbox("All Time", value=False)
    # start date
    start_date = st.sidebar.date_input("View Dashboard From ...", filtered_data['date_disbursed'].dropna().min())
    # end date
    end_date = st.sidebar.date_input("View Dasboard Till ...", date.today())

    # Checks for selected date range and if date range is valid
    if all_time:
        st.sidebar.success(f"All time from {start_date} to {end_date}")
    elif (start_date > end_date) and not all_time:
        st.sidebar.error("Error: Start Date must be before End Date.")
    else:
        st.sidebar.success(f"Date range: {start_date} to {end_date}")

    ############################################################################ Sets conditions for date filtering #######################################################################################

    # Override date filters if "All Time" is selected
    filtered_data['date_disbursed'] = pd.to_datetime(filtered_data['date_disbursed'])
    if all_time:
        start_date, end_date = pd.to_datetime(filtered_data["date_disbursed"].dropna().min()), pd.to_datetime(date.today())
    else:
        filtered_data = filtered_data[
            (filtered_data["date_disbursed"] >= pd.to_datetime(start_date)) &
            (filtered_data["date_disbursed"] <= pd.to_datetime(end_date))
        ]

    st.sidebar.markdown('---')

    ######################################################################### Set toggle for past maturity ####################################################################################

    # Past maturity toggle
    show_all_statuses = st.sidebar.toggle("Add Lost Loans (If any)", value=True)

    # Apply filter based on toggle
    if show_all_statuses:
        filtered_data = filtered_data[filtered_data["status"].isin(["OPEN", "COMPLETED", "PAST_MATURITY"])]
    else:
        filtered_data = filtered_data[filtered_data["status"].isin(["OPEN", "COMPLETED"])]

    ######################################################################### Set toggle for loss ####################################################################################

    # Categorization function
    def categorize_loss(days):
        if days == 0:
            return 'No loss'
        elif 1 <= days <= 15:
            return '1-15 days'
        elif 16 <= days <= 30:
            return '16-30 days'
        elif 31 <= days <= 45:
            return '31-45 days'
        elif 46 <= days <= 60:
            return '46-60 days'
        elif 61 <= days <= 90:
            return '61-90 days'
        else:
            return '90+ days'

    # Apply the categorization
    filtered_data['Loss_Category'] = filtered_data['past_maturity'].apply(categorize_loss)

    # Define custom sort order
    category_order = [
        'No loss', '1-15 days', '16-30 days', '31-45 days',
        '46-60 days', '61-90 days', '90+ days'
    ]

    # Define minimum day thresholds
    loss_min_days = {
        'No loss': 0,
        '1-15 days': 1,
        '16-30 days': 16,
        '31-45 days': 31,
        '46-60 days': 46,
        '61-90 days': 61,
        '90+ days': 91,
    }

    # Toggle
    show_loss = st.sidebar.toggle("Remove Loan Lost")

    if show_loss:
        # Filter only available categories and sort them by custom order
        available_categories = sorted(
            filtered_data['Loss_Category'].dropna().unique(),
            key=lambda x: category_order.index(x)
        )

        # Set default to '90+ days'
        default_index = category_order.index('90+ days') if '90+ days' in available_categories else 0

        selected_category = st.sidebar.selectbox(
            "Select a loss period",
            options=available_categories,
            index=default_index
        )

        min_day = loss_min_days[selected_category]

        # Filter based on selection
        if selected_category == 'No loss':
            filtered_data = filtered_data[filtered_data['past_maturity'] == 0]
        else:
            filtered_data = filtered_data[filtered_data['past_maturity'] < min_day]

            # Filter repay_data based on selected loan IDs
            repayment_df = repayment_df[repayment_df['ajo_loan_id'].isin(filtered_data['id'])]

    st.sidebar.markdown('---')

    ############################################################################ Filters multiselect #######################################################################################

    st.sidebar.write('Select or Filter Loans')

    with st.sidebar.popover('Seeds and Pennies Loans'):
        # Select loan_type
        selected_loan_type = st.multiselect('Select Loan Type', filtered_data['loan_type'].unique(), default=None)

        # Select status
        filtered_status = filtered_data[filtered_data['loan_type'].isin(selected_loan_type)]['status'].unique() if selected_loan_type else filtered_data['status'].unique()
        selected_status = st.multiselect('Select status', filtered_status, default=None)

        # Filter data based on selected loan_type and status
        if selected_loan_type:
            filtered_data = filtered_data[filtered_data['loan_type'].isin(selected_loan_type)]

        if selected_status:
            filtered_data = filtered_data[filtered_data['status'].isin(selected_status)]

        # Select verticals (based on selected loan_type and status)
        filtered_verticals = pd.Series(filtered_data[filtered_data['status'].isin(selected_status)]['verticals'].unique()).sort_values() if selected_status else pd.Series(filtered_data['verticals'].unique()).sort_values()
        selected_verticals = st.multiselect('Select Vertical', filtered_verticals, default=None)

        # Filter data based on selected verticals
        if selected_verticals:
            filtered_data = filtered_data[filtered_data['verticals'].isin(selected_verticals)]

        # Select sup_name (based on selected loan_type, status and verticals)
        # The supervisors are not filtered by merchants casue as at creation, no supervisor was assigned to any agent, but they are filtered by verticals, loantype and status not merchants
        filtered_sup_name = pd.Series(filtered_data[filtered_data['verticals'].isin(selected_verticals)]['sup_name'].unique()).sort_values() if selected_verticals else pd.Series(filtered_data['sup_name'].unique()).sort_values()
        selected_sup_name = st.multiselect('Select Supervisor', filtered_sup_name, default=None)

        # Filter data based on selected sup_name
        if selected_sup_name:
            filtered_data = filtered_data[filtered_data['sup_name'].isin(selected_sup_name)]

        # Select agent_full_name (based on selected sup_name, loan_type, status, merchants and vertical lead)
        filtered_agent_full_name = pd.Series(filtered_data[filtered_data['sup_name'].isin(selected_sup_name)]['agent_full_name'].unique()).sort_values() if selected_sup_name else pd.Series(filtered_data['agent_full_name'].unique()).sort_values()
        selected_agent_full_name = st.multiselect('Select Agent', filtered_agent_full_name, default=None)

        # Filter data based on selected agent_full_name
        if selected_agent_full_name:
            filtered_data = filtered_data[filtered_data['agent_full_name'].isin(selected_agent_full_name)]

    ############################################################################ Make Paybox sidebar #####################################################################################

    with st.sidebar.popover('Paybox/Merchant Loans'):
        # Select paybox_agents
        selected_paybox_agents = st.multiselect('Select Paybox Supervisor', filtered_data[(filtered_data['loan_type'] == 'MERCHANT_OVERDRAFT') | (filtered_data['type_of_user'] == 'MERCHANT')]['sup_name'].unique(), default=None)

        # Filter data based on selected loan_type and status
        if selected_paybox_agents:
            filtered_data = filtered_data[filtered_data['sup_name'].isin(selected_paybox_agents)]

        # Select agent_full_name (based on selected sup_name)
        filtered_paybox_agent_full_name = pd.Series(filtered_data[filtered_data['sup_name'].isin(selected_paybox_agents)]['agent_full_name'].unique()).sort_values() if selected_paybox_agents else pd.Series(filtered_data[(filtered_data['loan_type'] == 'MERCHANT_OVERDRAFT') | (filtered_data['type_of_user'] == 'MERCHANT')]['agent_full_name'].unique()).sort_values()
        selected_paybox_agent_full_name = st.multiselect('Select Paybox Agent', filtered_paybox_agent_full_name, default=None)

        # Filter data based on selected agent_full_name
        if selected_paybox_agent_full_name:
            filtered_data = filtered_data[filtered_data['agent_full_name'].isin(selected_paybox_agent_full_name)]

        # Select merchant (based on selected loan_type, status)
        filtered_merchant_agents = pd.Series(filtered_data[filtered_data['sup_name'].isin(selected_paybox_agents)]['merchant_agents'].unique()).sort_values() if selected_paybox_agents else pd.Series(filtered_data['merchant_agents'].unique()).sort_values()
        selected_merchant_agents = st.multiselect('Select Merchant Agent', filtered_merchant_agents, default=None)

        # Filter data based on selected merchant_agents
        if selected_merchant_agents:
            filtered_data = filtered_data[filtered_data['loan_type']=='MERCHANT_OVERDRAFT'] # This is for all merchant loans
            filtered_data = filtered_data[filtered_data['merchant_agents'].isin(selected_merchant_agents)]

    # Filter repayment_df based on the filtered_data
    if not filtered_data.empty:  # Check if filtered_data is not empty
        loan_ids = filtered_data['id'].unique()
        filtered_repayment_df = repayment_df[repayment_df['ajo_loan_id'].isin(loan_ids)]
    else:
        filtered_repayment_df = pd.DataFrame() # If filtered_data is empty, then repayment_df is empty.

    st.sidebar.markdown("---")

    # Set sidebar for laons and customer activity
    st.sidebar.write('Click to download files')
    with st.sidebar.popover('Download Loan/Customer Record'):
        ## to downlaod the file for each and store in a sidebar using .popover() keeping it readable...
        ## ... and simple for users to access dataframes instead of viewing them and consuming space in the dashboard
        # File 1
        file1, mid = st.columns([1, 3])
        with mid:
            st.write("### All Loan records")

        # File 2
        file2, mid = st.columns([1, 3])
        with mid:
            st.write("### Customer activities")

        # File 3
        file3, mid = st.columns([1, 3])
        with mid:
            st.write("### Monthly cohort summary")

        # File 4
        file4, mid = st.columns([1, 3])
        with mid:
            st.write("### Monthly cohort/cummulative summary")

        # File 5
        file5, mid = st.columns([1, 3])
        with mid:
            st.write("### Daily repayment by agents (no holiday)")

        # File 6
        file6, mid = st.columns([1, 3])
        with mid:
            st.write("### Expected collection by agents (w.r.t. holidays)")

        # File 7
        file7, mid = st.columns([1, 3])
        with mid:
            st.write("### Open loans (no past maturity/completed loans)")

        # File 8
        file8, mid = st.columns([1, 3])
        with mid:
            st.write("### Loans past maturity")

        # File 9
        file9, mid = st.columns([1, 3])
        with mid:
            st.write("### Monthly past maturity summary")

    st.sidebar.markdown("---")

    ########################################################################### set sidebar logout and reset buttons #####################################################################################

    # Define reload function to re-import from queries.py
    def reload_queries_functions():
        global holiday_, repayment_df_, sav_transaction_df_, loan_saving_df_, escrow_df_, staff_savings_trans_
        global monthly_repayments_, disbursement_4_last_month_, earnings_, loginactivity_, sup_df_, merchants_df_, loans_df_

        from queries import (
            holiday_, repayment_df_, sav_transaction_df_, loan_saving_df_, escrow_df_,
            staff_savings_trans_, monthly_repayments_, disbursement_4_last_month_, earnings_,
            loginactivity_, sup_df_, merchants_df_, loans_df_)

    # Clear all caches including queries.py
    def clear_all_caches():
        st.cache_data.clear()
        st.cache_resource.clear()

        try:
            queries.clear_cache() # Call function in queries.py
            importlib.reload(queries) # Reload the module
            reload_queries_functions() # Re-import all functions
        except Exception as e:
            st.warning(f"Failed to clear queries cache: {e}")

    # Sidebar Buttons
    c1, c2 = st.sidebar.columns([1, 1])

    with c1:
        st.markdown(
            """
            <style>
            div.stButton > button {
                width: 100%;
            }
            </style>
            """,
            unsafe_allow_html=True,
        )

        # Show auto-clear status
        display_cache_status(cache_just_cleared)

        # Manual clear button (now optional)
        if st.button("Clear Memory Now"):
            if manual_cache_clear():
                st.success("Memory cleared manually!")
                time.sleep(1)
                st.rerun()
            else:
                st.error("Failed to clear cache manually")

    with c2:
        st.markdown(
            """
            <style>div.stButton > button {width: 100%;}</style>
            """,
            unsafe_allow_html=True,
        )
        if st.button("Logout"):
            st.session_state["logged_in"] = False
            st.session_state["role"] = None
            st.session_state["username"] = None
            st.session_state["page"] = "login"
            st.success("You have been logged out.")
            time.sleep(3)
            st.rerun()

    ########################################################################### Data Cleaning #####################################################################################

    # Further data cleaning
    @st.cache_data(show_spinner=False)
    def process_filtered_data(filtered_data):
        """
        Processes the loans DataFrame by performing various operations:

        - Adjusts 'due_today_amount' to ensure it does not exceed 'expected_repayment'.
        - Adjusts 'exp_repay_today' to ensure it does not exceed 'expected_repayment'.

        Args:
            filtered_data (pd.DataFrame): The input DataFrame containing loan data.

        Returns:
            pd.DataFrame: The processed DataFrame.
        """
        # Adjust 'due_today_amount' and 'exp_repay_today' column directly
        filtered_data['due_today_amount'] = np.minimum(filtered_data['due_today_amount'], filtered_data['expected_repayment'])

        # Gets total amount saved
        filtered_data['total_amount_saved'] = np.where(
            (filtered_data['amount_disbursed'].isna()) | (filtered_data['amount_saved'].isna()) |
            (filtered_data['amount_disbursed'] == 0) | (filtered_data['amount_saved'] == 0),
            0,
            filtered_data['amount'] - filtered_data['amount_disbursed'] + filtered_data['amount_saved'] - 1500
        )

        # Actual disbursement
        filtered_data['actual_loan_disbursed'] = filtered_data.apply(
            lambda row: max(0, row['amount'] - row['total_amount_saved'] if pd.notna(row['total_amount_saved']) else 0),
            axis=1) # This gets only the actual disbursement capped at 0 instead of a negative value

        # Backend open portfolio
        filtered_data['backend_repayment'] = filtered_data.apply(
            lambda row: max(0, row['expected_repayment'] - row['total_amount_saved'] if pd.notna(row['total_amount_saved']) else 0),
            axis=1) # This gets only the actual disbursement capped at 0 instead of a negative value

        # Backend_open_port
        filtered_data['backend_open_port'] = filtered_data.apply(
            lambda row: max(0, row['backend_repayment'] - row['repayment'] if pd.notna(row['repayment']) else row['backend_repayment']),
            axis=1) # This gets only the total defaults and it gives all the overpayment a cap at 0 instead of a negative value

        # Daily repayment amount for backend_repayment
        filtered_data['backend_daily_repayment'] = (filtered_data['backend_repayment']/filtered_data['tenor_in_days']).round(2)

        return filtered_data

    # Loads data
    filtered_data = process_filtered_data(filtered_data)

    # Setup Dates
    @st.cache_data(show_spinner=False)
    def get_key_dates(end_date):
        today = pd.to_datetime(end_date).normalize()
        return {
            "today": today,
            "yesterday": (today - pd.DateOffset(days=1)).normalize(),
            "start_of_week": (today - pd.DateOffset(days=today.weekday())).normalize(),
            "start_of_month": today.replace(day=1).normalize(),
            "start_of_year": today.replace(month=1, day=1)
        }

    # Prepare Holidays
    @st.cache_data(show_spinner=False)
    def process_holidays(holiday_df):
        holiday_df['date'] = pd.to_datetime(holiday_df['date']).dt.normalize()
        general = set(holiday_df[holiday_df['agent_id'].isna()]['date'])
        agent_map = {
            agent: set(sub['date']) for agent, sub in holiday_df[holiday_df['agent_id'].notna()].groupby('agent_id')
        }
        return general, agent_map

    # Build Schedule
    @st.cache_data(show_spinner=False)
    def build_schedule(loans_df, holidays_df, today, amount_col, cap_col, output_col, cum_col):
        general_hols, agent_hols_map = process_holidays(holidays_df)
        records = []

        for _, loan in loans_df.dropna(subset=['start_date', 'end_date']).iterrows():
            loan_id = loan['id']
            start = loan['start_date']
            end = min(loan['end_date'], today)
            daily_amt = loan[amount_col]
            cap = loan[cap_col]
            agent = loan.get('agent_id', None)

            dates = pd.date_range(start, end, freq='B')
            dates = [d for d in dates if d not in general_hols and d not in agent_hols_map.get(agent, set())]

            cum = 0.0
            for d in dates:
                to_add = min(daily_amt, cap - cum)
                if to_add <= 0:
                    break
                cum += to_add
                records.append({
                    'loan_id': loan_id,
                    'date': d,
                    output_col: to_add,
                    cum_col: cum
                })

        return pd.DataFrame(records)

    # Merge and Aggregate
    @st.cache_data(show_spinner=False)
    def process_and_group(filtered_df, schedule_df, today, key_dates, date_col, group_col):
        merged = filtered_df.merge(schedule_df, left_on='id', right_on='loan_id', how='right')
        merged[date_col] = pd.to_datetime(merged[date_col]).dt.normalize()

        # Identify the repayment column dynamically
        label = [col for col in schedule_df.columns if col not in ['loan_id', 'date']][0]

        def filter_group(df, group_col, filter_date):
            return df[df[date_col] >= filter_date].groupby([group_col, 'date_disbursed'])[[label]].sum() \
                    .sort_values(by='date_disbursed').reset_index()

        return {
            "today": filter_group(merged[merged[date_col] == key_dates['today']], group_col, key_dates['today']),
            "yesterday": filter_group(merged[merged[date_col] == key_dates['yesterday']], group_col, key_dates['yesterday']),
            "this_week": filter_group(merged, group_col, key_dates['start_of_week']),
            "this_month": filter_group(merged, group_col, key_dates['start_of_month']),
            "this_year": filter_group(merged, group_col, key_dates['start_of_year']),
            "all_time": merged.groupby([group_col, 'date_disbursed'])[[label]].sum().sort_values(by='date_disbursed').reset_index()
        }

    # Main Processing Function
    def calculate_expected_repayments(filtered_data, holiday, end_date):
        try:
            df = filtered_data.copy()
            key_dates = get_key_dates(end_date)

            for col in ['date_disbursed', 'start_date', 'end_date']:
                df[col] = pd.to_datetime(df[col]).dt.normalize()

            df = df.dropna(subset=['start_date', 'end_date'])

            # --- First: Standard Expected ---
            schedule_df = build_schedule(df, holiday, key_dates['today'],
                                        amount_col='daily_repayment_amount',
                                        cap_col='expected_repayment',
                                        output_col='exp_repay_today',
                                        cum_col='cumulative_expected')

            expected_results = process_and_group(
                df, schedule_df, key_dates['today'], key_dates, 'date', 'id'
            )

            # --- Second: Backend Expected ---
            backend_schedule_df = build_schedule(df, holiday, key_dates['today'],
                                                amount_col='backend_daily_repayment',
                                                cap_col='backend_repayment',
                                                output_col='backend_exp_repay_today',
                                                cum_col='cumulative_backend_expected')

            backend_results = process_and_group(
                df, backend_schedule_df, key_dates['today'], key_dates, 'date', 'id'
            )

            # returns raw schedule DataFrames and grouped datasets
            return expected_results, backend_results, schedule_df, backend_schedule_df

        except Exception as e:
            st.warning(f"An error occurred while processing expected repayments: {e}")
            return None, None, None, None

    # Assign Output Variables
    expected, backend, schedule_df, backend_schedule_df = calculate_expected_repayments(filtered_data, holiday, end_date)

    if expected and backend:
        # Expected Collection
        exp_till_today_ = expected["today"]
        exp_till_yesterday_ = expected["yesterday"]
        exp_till_ThisWeek_ = expected["this_week"]
        exp_till_ThisMonth_ = expected["this_month"]
        exp_till_ThisYear_ = expected["this_year"]
        exp_all_time = expected["all_time"]

        # Backend Expected Collection
        backend_exp_till_today_ = backend["today"]
        backend_exp_till_yesterday_ = backend["yesterday"]
        backend_exp_till_ThisWeek_ = backend["this_week"]
        backend_exp_till_ThisMonth_ = backend["this_month"]
        backend_exp_till_ThisYear_ = backend["this_year"]
        backend_exp_all_time = backend["all_time"]

    # Further data cleaning
    @st.cache_data(show_spinner=False)
    def process_filtered_data(filtered_data):
        """
        Processes the loans DataFrame by computing derived metrics for repayments, portfolio status,
        repayment performance, loan maturity, loan structure, and interest/principal breakdowns.
            - Merges in 'exp_repay_today' from `expected['all_time']`
            - Merges in 'backend_exp_repay_today' from `backend['all_time']`
            - Caps 'due_today_amount' and 'exp_repay_today' to not exceed 'expected_repayment'
            - Caps 'backend_exp_repay_today' to not exceed 'backend_repayment'
            - Fills missing repayment values with 0
            - `exp_repay_count`: Number of days paid (based on repayment amount / daily rate)
            - `total_amount_saved`: Computes saved amount using amount, disbursed, saved, and a fixed deduction
            - `actual_loan_disbursed`: Amount disbursed after accounting for savings
            - `backend_repayment`: Remaining amount to be paid (expected - saved)
            - `backend_open_port`: Amount still unpaid after backend repayment vs. actual repayment
            - `backend_daily_repayment`: Daily expected backend repayment
            - `missed_repayment`: Expected today vs actual repayment (capped at 0)
            - `missed_days`: Missed days derived from missed amount / daily rate (ceil)
            - `open_portfolio`: Remaining unpaid balance from expected vs. actual repayment
            - `repayment_rate`: Actual repayment as a percentage of expected for today
            - `backend_rate`: Same as above but for backend expected
            - `completion_rate`: Overall repayment as a percentage of expected repayment
            - All rates are clipped at 100%
            - `days_left_to_maturity`: Days from today to `end_date`, clipped at 0
            - `month`, `Year`: Extracts month and year from `date_disbursed`
            - `loan_age(days)`: Duration of loan capped at today
            - `loan_age(months)`: Same as above but as timedelta
            - `num_of_month`: Number of months extracted from `tenor` column
            - `monthly_repayment`: Expected monthly repayment = total expected / months
            - `int_monthly_repayment`, `prin_monthly_repayment`: Breakdown of interest and principal per month
            - `interest_element_pct`, `principal_element_pct`: Percentages of monthly payment that are interest or principal
            - `interest_element`, `principal_element`: Actual amounts collected in interest and principal from repayment
            - Sorts the DataFrame by 'date_disbursed'

        Args:
            filtered_data (pd.DataFrame): The DataFrame containing loan details and base metrics.

        Returns:
            pd.DataFrame: The enriched and processed DataFrame with all derived fields.
        """
        # merge to get exp_repay_today and backend_exp_repay_today
        filtered_data = filtered_data.merge(expected['all_time'][['id', 'exp_repay_today']], on='id', how='left')
        filtered_data = filtered_data.merge(backend['all_time'][['id', 'backend_exp_repay_today']], on='id', how='left')

        # Adjust 'due_today_amount' and 'exp_repay_today' column directly
        filtered_data['due_today_amount'] = np.minimum(filtered_data['due_today_amount'], filtered_data['expected_repayment'])
        filtered_data['exp_repay_today'] = np.minimum(filtered_data['exp_repay_today'], filtered_data['expected_repayment'])
        filtered_data['exp_repay_today'] = filtered_data['exp_repay_today'].fillna(0)
        # Gets max of backend_exp_repayment
        filtered_data['backend_exp_repay_today'] = np.minimum(filtered_data['backend_exp_repay_today'], filtered_data['backend_repayment'])
        filtered_data['backend_exp_repay_today'] = filtered_data['backend_exp_repay_today'].fillna(0)

        # Expected repayment count
        filtered_data['exp_repay_count'] = (filtered_data['exp_repay_today'] / filtered_data['daily_repayment_amount']).round(2)

        # Gets total amount saved
        filtered_data['total_amount_saved'] = np.where(
            (filtered_data['amount_disbursed'].isna()) | (filtered_data['amount_saved'].isna()) |
            (filtered_data['amount_disbursed'] == 0) | (filtered_data['amount_saved'] == 0),
            0,
            filtered_data['amount'] - filtered_data['amount_disbursed'] + filtered_data['amount_saved'] - 1500
        )

        # Actual disbursement
        filtered_data['actual_loan_disbursed'] = filtered_data.apply(
            lambda row: max(0, row['amount'] - row['total_amount_saved'] if pd.notna(row['total_amount_saved']) else 0),
            axis=1) # This gets only the actual disbursement capped at 0 instead of a negative value

        # Backend open portfolio
        filtered_data['backend_repayment'] = filtered_data.apply(
            lambda row: max(0, row['expected_repayment'] - row['total_amount_saved'] if pd.notna(row['total_amount_saved']) else 0),
            axis=1) # This gets only the actual disbursement capped at 0 instead of a negative value

        # Backend_open_port
        filtered_data['backend_open_port'] = filtered_data.apply(
            lambda row: max(0, row['backend_repayment'] - row['repayment'] if pd.notna(row['repayment']) else row['backend_repayment']),
            axis=1) # This gets only the total defaults and it gives all the overpayment a cap at 0 instead of a negative value

        # Daily repayment amount for backend_repayment
        filtered_data['backend_daily_repayment'] = (filtered_data['backend_repayment']/filtered_data['tenor_in_days']).round(2)

        # Missed amount using due_today
        filtered_data['missed_repayment'] = filtered_data.apply(
            lambda row: max(0, row['exp_repay_today'] - row['repayment'] if pd.notna(row['repayment']) else 0),
            axis=1) # This gets only the missed repayments and it gives all the overpayment a cap at 0 instead of a negative value

        # Missed days
        filtered_data['missed_days'] = np.ceil(filtered_data['missed_repayment'] / filtered_data['daily_repayment_amount'])

        # Apply condition: If missed_days is 1 and missed_repayment is less than 200, set it to 0
        filtered_data['missed_days'] = np.where((filtered_data['missed_days'] == 1) & (filtered_data['missed_repayment'] < 200), 0, filtered_data['missed_days'])

        # Open portfolio
        filtered_data['open_portfolio'] = filtered_data.apply(
            lambda row: max(0, row['expected_repayment'] - row['repayment'] if pd.notna(row['repayment']) else row['expected_repayment']),
            axis=1) # This gets only the total defaults and it gives all the overpayment a cap at 0 instead of a negative value

        # Calculate Repayment Rate (%)
        filtered_data['repayment_rate'] = np.where(
            filtered_data['exp_repay_today'] == 0,
            0,
            (filtered_data['repayment'] / filtered_data['exp_repay_today'] * 100).round()
        )
        filtered_data['repayment_rate'] = filtered_data['repayment_rate'].clip(upper=100)

        # Calculate Repayment Rate (%) for backend_exp_repayment
        filtered_data['backend_rate'] = np.where(
            filtered_data['backend_exp_repay_today'] == 0,
            0,
            (filtered_data['repayment'] / filtered_data['backend_exp_repay_today'] * 100).round()
        )
        filtered_data['backend_rate'] = filtered_data['backend_rate'].clip(upper=100)

        # Calculate Completion Rate (%)
        filtered_data['completion_rate'] = np.where(
            (filtered_data['expected_repayment'] == 0) | (filtered_data['status'] == 'OPEN'),
            0,
            (filtered_data['repayment'] / filtered_data['expected_repayment'] * 100).round()
        )
        filtered_data['completion_rate'] = filtered_data['completion_rate'].clip(upper=100)

        # Get current date and ensure it's a Timestamp
        TodayDate = pd.Timestamp.today().normalize()

        # Calculate days left only for loans that haven't reached their end date
        filtered_data['days_left_to_maturity'] = (filtered_data['end_date'] - TodayDate).dt.days

        # If 'end_date' is already passed, set 'days_left_to_maturity' to 0
        filtered_data.loc[filtered_data['days_left_to_maturity'] < 0, 'days_left_to_maturity'] = 0

        # Extract month names
        filtered_data['month'] = filtered_data['date_disbursed'].dt.strftime('%B')  # Use '%b' for abbreviated month names

        # Extract Year
        filtered_data['Year'] = filtered_data['date_disbursed'].dt.strftime('%Y')

        # Calculate Loan Duration
        filtered_data['loan_age(days)'] = (filtered_data['end_date'].clip(upper=TodayDate) - (filtered_data['start_date'] - pd.Timedelta(days=4))).dt.days

        # Gets loan duration in months
        filtered_data['loan_age(months)'] = (filtered_data['end_date'].clip(upper=TodayDate) - (filtered_data['start_date'] - pd.Timedelta(days=4)))

        # Extracts number of month from 'tenor'
        filtered_data['num_of_month'] = filtered_data['tenor'].str.extract(r'(\d+)').astype(int)

        # Sort by 'date_disbursed'
        filtered_data = filtered_data.sort_values(by='date_disbursed')

        # Gets monthly repayment for loan
        filtered_data['monthly_repayment'] = filtered_data['expected_repayment'] / filtered_data['num_of_month']

        # Gets interest monthly repayment
        filtered_data['int_monthly_repayment'] = filtered_data['interest_amount'] / filtered_data['num_of_month']

        # Gets principal monthly repayment
        filtered_data['prin_monthly_repayment'] = filtered_data['amount'] / filtered_data['num_of_month']

        # Gets interest element percentage
        filtered_data['interest_element_pct'] = (filtered_data['int_monthly_repayment'] / filtered_data['monthly_repayment']).round(4)

        # Gets principal element percentage
        filtered_data['principal_element_pct'] = (filtered_data['prin_monthly_repayment'] / filtered_data['monthly_repayment']).round(4)

        # Interest element
        filtered_data['interest_element'] = (filtered_data['interest_element_pct'] * filtered_data['repayment']).round(2)

        # Principal collected
        filtered_data['principal_element'] = (filtered_data['principal_element_pct'] * filtered_data['repayment']).round(2)

        return filtered_data

    # Loads data
    filtered_data = process_filtered_data(filtered_data)

    ######################################################################### Save Files #######################################################################################

    # Gets file with a download button to save space
    with file1:
        if filtered_data.empty:
            st.warning("No data found for the selected filters. Please adjust your selections.")
        else:
            # Format: "15_Jan_2025"
            today_str = datetime.now().strftime("%d_%b_%Y")
            file_name = f"Seeds_and_pennies_loan_till_{today_str}.csv"

            csv = filtered_data.to_csv(index=False).encode("utf-8")
            st.download_button(
                label="⬇️ Download",
                data=csv,
                file_name=file_name,
                mime="text/csv"
            )

    ######################################################################### Loans past due date #######################################################################################

    # Convert end_date to datetime
    filtered_data["end_date"] = pd.to_datetime(filtered_data["end_date"])

    # Filter loans with status 'OPEN' and end_date greater than today
    cur_date = pd.to_datetime(end_date)

    # Filter for past_due date when ststua is not completed
    past_due_date = filtered_data[(filtered_data["status"] != "COMPLETED") & (filtered_data["end_date"] < cur_date)]

    # Calculate the count and total amount for today
    past_maturity_total_amount = past_due_date["open_portfolio"].sum().round(2) # Gets total outstanding amount

    # Portfolio At Risk by number of days
    past_maturity_7_days = past_due_date[past_due_date['past_maturity']<=7]
    past_maturity_15_days = past_due_date[(past_due_date['past_maturity']>7) & (past_due_date['past_maturity']<=15)]
    past_maturity_30_days = past_due_date[(past_due_date['past_maturity']>15) & (past_due_date['past_maturity']<=30)]
    past_maturity_45_days = past_due_date[(past_due_date['past_maturity']>30) & (past_due_date['past_maturity']<=45)]
    past_maturity_60_days = past_due_date[(past_due_date['past_maturity']>45) & (past_due_date['past_maturity']<=60)]
    past_maturity_90_days = past_due_date[(past_due_date['past_maturity']>60) & (past_due_date['past_maturity']<=90)]
    past_maturity_90_plus_days = past_due_date[past_due_date['past_maturity']>90]

    # Portfolio At Risk count
    past_maturity_7_days_count = past_maturity_7_days['id'].count().round(2)
    past_maturity_15_days_count = past_maturity_15_days['id'].count().round(2)
    past_maturity_30_days_count = past_maturity_30_days['id'].count().round(2)
    past_maturity_45_days_count = past_maturity_45_days['id'].count().round(2)
    past_maturity_60_days_count = past_maturity_60_days['id'].count().round(2)
    past_maturity_90_days_count = past_maturity_90_days['id'].count().round(2)
    past_maturity_90_plus_days_count = past_maturity_90_plus_days['id'].count().round(2)

    # Portfolio At Risk sum
    past_maturity_7_days_sum = past_maturity_7_days['open_portfolio'].sum().round(2)
    past_maturity_15_days_sum = past_maturity_15_days['open_portfolio'].sum().round(2)
    past_maturity_30_days_sum = past_maturity_30_days['open_portfolio'].sum().round(2)
    past_maturity_45_days_sum = past_maturity_45_days['open_portfolio'].sum().round(2)
    past_maturity_60_days_sum = past_maturity_60_days['open_portfolio'].sum().round(2)
    past_maturity_90_days_sum = past_maturity_90_days['open_portfolio'].sum().round(2)
    past_maturity_90_plus_days_sum = past_maturity_90_plus_days['open_portfolio'].sum().round(2)

    total_bad_loans = (past_maturity_45_days_sum + past_maturity_60_days_sum + past_maturity_90_days_sum + past_maturity_90_plus_days_sum).round(2)
    bad_loans_count = (past_maturity_45_days_count + past_maturity_60_days_count + past_maturity_90_days_count + past_maturity_90_plus_days_count).round(2)
    total_late_repayment = (past_maturity_7_days_sum + past_maturity_15_days_sum + past_maturity_30_days_sum)
    late_repayment_count = (past_maturity_7_days_count + past_maturity_15_days_count + past_maturity_30_days_count)

    ######################################################################### Active Portfolio ####################################################################################

    # Filter for past_due date
    active_portfolio = filtered_data[(filtered_data["status"] == "OPEN") & (filtered_data["end_date"] >= cur_date)]

    ######################################################################### Create data for yesterday ####################################################################################

    # Convert 'date_disbursed' to datetime format for filtering
    filtered_data['date_disbursed'] = pd.to_datetime(filtered_data['date_disbursed'])

    # previous date
    yest_date = cur_date - timedelta(days=1)

    # Filter rows where 'date_disbursed' is up until yesterday
    previous_data = filtered_data[filtered_data['date_disbursed'] <= yest_date]
    # Reset the index if needed
    previous_data = previous_data.reset_index(drop=True)

    ######################################################################### Today's expected payments for all 'OPEN' loans ####################################################################################

    filtered_data['start_date'] = pd.to_datetime(filtered_data['start_date'])
    filtered_data['end_date'] = pd.to_datetime(filtered_data['end_date'])

    # Filter loans with status 'OPEN' for today
    repayment_today = filtered_data[
        (filtered_data["status"] == "OPEN") &
        (filtered_data["start_date"] <= cur_date) &
        (filtered_data["end_date"] >= cur_date)
    ]

    ############################################################################ Getting all users that come back to take a loan #######################################################################################

    # Convert start_date to datetime and extract Month and Year
    loans_df['start_date'] = pd.to_datetime(loans_df['start_date'])
    loans_df['Month_and_Year'] = loans_df['start_date'].dt.strftime("%b %Y")

    # Copy df
    grouped_df = loans_df.copy()

    # Mark the first loan occurrence for each user
    grouped_df['is_first_loan'] = ~grouped_df.duplicated(subset=['users_full_name'], keep='first')

    # Returning Users (Excluding First Occurrence)
    repeat_loans_df = grouped_df[~grouped_df['is_first_loan']]  # Remove first occurrence

    # Count unique returning users per month
    monthly_returning_users = repeat_loans_df.groupby('Month_and_Year')['users_full_name'].nunique().reset_index()
    monthly_returning_users = monthly_returning_users.rename(columns={'users_full_name': 'returning_users'})

    # Total Repeat Users (Including First Occurrence)
    all_repeat_users = grouped_df[grouped_df['users_full_name'].duplicated(keep=False)]  # Keep all repeat borrowers

    # Count total repeat users per month (counting first occurrence)
    monthly_total_repeat_users = all_repeat_users.groupby('Month_and_Year')['users_full_name'].nunique().reset_index()
    monthly_total_repeat_users = monthly_total_repeat_users.rename(columns={'users_full_name': 'total_repeat_users'})

    # Get Total Users Per Month
    monthly_total_users = grouped_df.groupby('Month_and_Year')['users_full_name'].nunique().reset_index()
    monthly_total_users = monthly_total_users.rename(columns={'users_full_name': 'total_users'})

    # Merge all DataFrames
    users_retake_df = monthly_total_users.merge(monthly_returning_users, on='Month_and_Year', how='left').fillna(0)
    users_retake_df = users_retake_df.merge(monthly_total_repeat_users, on='Month_and_Year', how='left').fillna(0)

    # Calculate percentage of returning users (excluding first occurrence)
    users_retake_df['returning_pct'] = round((users_retake_df['returning_users'] / users_retake_df['total_users']) * 100, 2)

    # Calculate percentage of total repeat users (including first occurrence)
    users_retake_df['total_repeat_pct'] = round((users_retake_df['total_repeat_users'] / users_retake_df['total_users']) * 100, 2)

    ############################################################################ Repayment By Paid date #######################################################################################

    # Convert datetime and make 'cur_date' able to read on repayment dataset by...
    # ...altering the date and creating a new date column so it doesn't affect the line chart
    repayment_df['paid_date'] = pd.to_datetime(repayment_df['paid_date']).dt.normalize().dt.tz_localize(None)
    filtered_repayment_df['paid_date'] = pd.to_datetime(filtered_repayment_df['paid_date']).dt.normalize().dt.tz_localize(None)
    top_up_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'].isin(['ESCROW_REPAY', 'DEDUCTABLE_REPAY'])] # all top ups in repayemnt table
    escrow_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'ESCROW_DEBIT'] # all escrow debits in repayment table
    total_paid_only = filtered_repayment_df[filtered_repayment_df['repayment_type'].isin(['AGENT_DEBIT', 'TRANSFER', 'LOAN_RECOVERY'])] # all normal loan repayments alone
    agent_debit_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'AGENT_DEBIT']
    transfer_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'TRANSFER']
    first_week_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'FIRST_WEEK']
    libertypay_directdebit_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'LIBERTYPAY_DIRECTDEBIT']
    lite_debit_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'LITE_DEBIT']
    loan_recovery_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'LOAN_RECOVERY']
    merchant_debit_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'MERCHANT_DEBIT']
    savings_debit_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'SAVINGS_DEBIT']
    spend_debit_repayment = filtered_repayment_df[filtered_repayment_df['repayment_type'] == 'SPEND_DEBIT']

    ############################################################################ Dashboard graphs and plots #######################################################################################

    # splits dashboard into tabs
    tab1, tab2, tab3, tab4, tab5, tab6, tab7, tab8, tab9 = st.tabs(["Portfolio Overview", "Repayment Analysis", "Time-Series Analysis", "Supervisor Portfolio", "Merchant Portfolio", "Pharmarcy Analysis", "Financial Trajectory", "Product Analysis", "Past Maturity Analysis"])

    ############################################################################ Tab 1 #######################################################################################

    with tab1:

    ######################################################################### Set Date Filters for metrics ####################################################################################

        # Get today's date
        today = pd.to_datetime(cur_date)
        # Calculate yesterday's date
        yesterday = today - pd.DateOffset(days=1)
        # Calculate the day before yesterday's date
        day_before_yesterday = today - pd.DateOffset(days=2)
        # Calculate the start of this week
        start_of_week = today - pd.DateOffset(days=today.weekday())  # Monday as start of week
        # Calculate the start of last week (Monday)
        start_of_last_week = start_of_week - pd.DateOffset(weeks=1)
        # Calculate the start of two weeks ago (Monday)
        start_of_last_2_weeks = start_of_week - pd.DateOffset(weeks=2)
        # Calculate the start of this month
        start_of_month = today.replace(day=1)
        # Calculate the start of last month
        start_of_last_month = (start_of_month - pd.DateOffset(days=1)).replace(day=1)
        # Calculate the start of two months ago
        start_of_last_2_months = (start_of_last_month - pd.DateOffset(days=1)).replace(day=1)
        # Calculate the start of this year
        start_of_year = today.replace(month=1, day=1)
        # Calculate the start of last year
        start_of_last_year = start_of_year - pd.DateOffset(years=1)
        # Calculate the start of two years ago
        start_of_last_2_years = start_of_last_year - pd.DateOffset(years=1)

        # Filter datasets for repayment
        ## Note that we are using the filtered_repayment_df cause we want it to be filtered based...
        ## ...on the filters that are already defined by the sidebar
        ### 'filtered_repayment_df' = filtered repayments || 'repayment_df' = repayment dataset

        repay_df_today = filtered_repayment_df[filtered_repayment_df['paid_date'] == today]
        repay_df_yesterday = filtered_repayment_df[filtered_repayment_df['paid_date'] == yesterday]
        repay_df_day_before_yesterday = filtered_repayment_df[filtered_repayment_df['paid_date'] == day_before_yesterday]
        repay_df_week = filtered_repayment_df[filtered_repayment_df['paid_date'] >= start_of_week]
        repay_df_last_week = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_week) &
            (filtered_repayment_df['paid_date'] < start_of_week)
        ]
        repay_df_last_2_weeks = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_2_weeks) &
            (filtered_repayment_df['paid_date'] < start_of_last_week)
        ]
        repay_df_month = filtered_repayment_df[filtered_repayment_df['paid_date'] >= start_of_month]
        repay_df_last_month = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_month) &
            (filtered_repayment_df['paid_date'] < start_of_month)
        ]
        repay_df_last_2_months = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_2_months) &
            (filtered_repayment_df['paid_date'] < start_of_last_month)
        ]
        repay_df_year = filtered_repayment_df[filtered_repayment_df['paid_date'] >= start_of_year]
        repay_df_last_year = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_year) &
            (filtered_repayment_df['paid_date'] < start_of_year)
        ]
        repay_df_last_2_years = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_2_years) &
            (filtered_repayment_df['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        repay_today_total = repay_df_today['repayment_by_date'].sum()
        repay_yesterday_total = repay_df_yesterday['repayment_by_date'].sum()
        repay_day_before_yesterday_total = repay_df_day_before_yesterday['repayment_by_date'].sum()
        repay_week_total = repay_df_week['repayment_by_date'].sum()
        repay_last_week_total = repay_df_last_week['repayment_by_date'].sum()
        repay_last_2_week_total = repay_df_last_2_weeks['repayment_by_date'].sum()
        repay_month_total = repay_df_month['repayment_by_date'].sum()
        repay_last_month_total = repay_df_last_month['repayment_by_date'].sum()
        repay_last_2_month_total = repay_df_last_2_months['repayment_by_date'].sum()
        repay_year_total = repay_df_year['repayment_by_date'].sum()
        repay_last_year_total = repay_df_last_year['repayment_by_date'].sum()
        repay_last_2_year_total = repay_df_last_2_years['repayment_by_date'].sum()
        repay_all_time_total = filtered_repayment_df['repayment_by_date'].sum()

        # Gets all total paid for the loan disbursed
        total_paid_only_df_today = total_paid_only[total_paid_only['paid_date'] == today]
        total_paid_only_df_yesterday = total_paid_only[total_paid_only['paid_date'] == yesterday]
        total_paid_only_df_day_before_yesterday = total_paid_only[total_paid_only['paid_date'] == day_before_yesterday]
        total_paid_only_df_week = total_paid_only[total_paid_only['paid_date'] >= start_of_week]
        total_paid_only_df_last_week = total_paid_only[
            (total_paid_only['paid_date'] >= start_of_last_week) &
            (total_paid_only['paid_date'] < start_of_week)
        ]
        total_paid_only_df_last_2_weeks = total_paid_only[
            (total_paid_only['paid_date'] >= start_of_last_2_weeks) &
            (total_paid_only['paid_date'] < start_of_last_week)
        ]
        total_paid_only_df_month = total_paid_only[total_paid_only['paid_date'] >= start_of_month]
        total_paid_only_df_last_month = total_paid_only[
            (total_paid_only['paid_date'] >= start_of_last_month) &
            (total_paid_only['paid_date'] < start_of_month)
        ]
        total_paid_only_df_last_2_months = total_paid_only[
            (total_paid_only['paid_date'] >= start_of_last_2_months) &
            (total_paid_only['paid_date'] < start_of_last_month)
        ]
        total_paid_only_df_year = total_paid_only[total_paid_only['paid_date'] >= start_of_year]
        total_paid_only_df_last_year = total_paid_only[
            (total_paid_only['paid_date'] >= start_of_last_year) &
            (total_paid_only['paid_date'] < start_of_year)
        ]
        total_paid_only_df_last_2_years = total_paid_only[
            (total_paid_only['paid_date'] >= start_of_last_2_years) &
            (total_paid_only['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for total_paid_onlyment
        total_paid_only_today_total = total_paid_only_df_today['repayment_by_date'].sum()
        total_paid_only_yesterday_total = total_paid_only_df_yesterday['repayment_by_date'].sum()
        total_paid_only_day_before_yesterday_total = total_paid_only_df_day_before_yesterday['repayment_by_date'].sum()
        total_paid_only_week_total = total_paid_only_df_week['repayment_by_date'].sum()
        total_paid_only_last_week_total = total_paid_only_df_last_week['repayment_by_date'].sum()
        total_paid_only_last_2_week_total = total_paid_only_df_last_2_weeks['repayment_by_date'].sum()
        total_paid_only_month_total = total_paid_only_df_month['repayment_by_date'].sum()
        total_paid_only_last_month_total = total_paid_only_df_last_month['repayment_by_date'].sum()
        total_paid_only_last_2_month_total = total_paid_only_df_last_2_months['repayment_by_date'].sum()
        total_paid_only_year_total = total_paid_only_df_year['repayment_by_date'].sum()
        total_paid_only_last_year_total = total_paid_only_df_last_year['repayment_by_date'].sum()
        total_paid_only_last_2_year_total = total_paid_only_df_last_2_years['repayment_by_date'].sum()
        total_paid_only_all_time_total = total_paid_only['repayment_by_date'].sum()

        # Filter datasets for loans by date_disbursed
        df_today = filtered_data[filtered_data['date_disbursed'] == today]
        df_yesterday = filtered_data[filtered_data['date_disbursed'] == yesterday]
        df_day_before_yesterday = filtered_data[filtered_data['date_disbursed'] == day_before_yesterday]
        df_week = filtered_data[filtered_data['date_disbursed'] >= start_of_week]
        df_last_week = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_week) &
            (filtered_data['date_disbursed'] < start_of_week)
        ]
        df_last_2_weeks = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_2_weeks) &
            (filtered_data['date_disbursed'] < start_of_last_week)
        ]
        df_month = filtered_data[filtered_data['date_disbursed'] >= start_of_month]
        df_last_month = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_month) &
            (filtered_data['date_disbursed'] < start_of_month)
        ]
        df_last_2_months = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_2_months) &
            (filtered_data['date_disbursed'] < start_of_last_month)
        ]
        df_year = filtered_data[filtered_data['date_disbursed'] >= start_of_year]
        df_last_year = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_year) &
            (filtered_data['date_disbursed'] < start_of_year)
        ]
        df_last_2_years = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_2_years) &
            (filtered_data['date_disbursed'] < start_of_last_year)
        ]

        # Aggregate data for IDs
        user_today_total = df_today['id'].count()
        user_yesterday_total = df_yesterday['id'].count()
        user_day_before_yesterday_total = df_day_before_yesterday['id'].count()
        user_week_total = df_week['id'].count()
        user_last_week_total = df_last_week['id'].count()
        user_last_2_weeks_total = df_last_2_weeks['id'].count()
        user_month_total = df_month['id'].count()
        user_last_month_total = df_last_month['id'].count()
        user_last_2_months_total = df_last_2_months['id'].count()
        user_year_total = df_year['id'].count()
        user_last_year_total = df_last_year['id'].count()
        user_last_2_years_total = df_last_2_years['id'].count()
        user_all_time_total = filtered_data['id'].count()

        # Aggregate data for disbursement
        disbursed_today_total = df_today['amount'].sum()
        disbursed_yesterday_total = df_yesterday['amount'].sum()
        disbursed_day_before_yesterday_total = df_day_before_yesterday['amount'].sum()
        disbursed_week_total = df_week['amount'].sum()
        disbursed_last_week_total = df_last_week['amount'].sum()
        disbursed_last_2_weeks_total = df_last_2_weeks['amount'].sum()
        disbursed_month_total = df_month['amount'].sum()
        disbursed_last_month_total = df_last_month['amount'].sum()
        disbursed_last_2_months_total = df_last_2_months['amount'].sum()
        disbursed_year_total = df_year['amount'].sum()
        disbursed_last_year_total = df_last_year['amount'].sum()
        disbursed_last_2_years_total = df_last_2_years['amount'].sum()
        disbursed_all_time_total = filtered_data['amount'].sum()

        # Aggregate data for Actual Disbursed
        actual_disbursed_today_total = df_today['actual_loan_disbursed'].sum()
        actual_disbursed_yesterday_total = df_yesterday['actual_loan_disbursed'].sum()
        actual_disbursed_day_before_yesterday_total = df_day_before_yesterday['actual_loan_disbursed'].sum()
        actual_disbursed_week_total = df_week['actual_loan_disbursed'].sum()
        actual_disbursed_last_week_total = df_last_week['actual_loan_disbursed'].sum()
        actual_disbursed_last_2_weeks_total = df_last_2_weeks['actual_loan_disbursed'].sum()
        actual_disbursed_month_total = df_month['actual_loan_disbursed'].sum()
        actual_disbursed_last_month_total = df_last_month['actual_loan_disbursed'].sum()
        actual_disbursed_last_2_months_total = df_last_2_months['actual_loan_disbursed'].sum()
        actual_disbursed_year_total = df_year['actual_loan_disbursed'].sum()
        actual_disbursed_last_year_total = df_last_year['actual_loan_disbursed'].sum()
        actual_disbursed_last_2_years_total = df_last_2_years['actual_loan_disbursed'].sum()
        actual_disbursed_all_time_total = filtered_data['actual_loan_disbursed'].sum()

        # Aggregate data for amount saved and fees
        amount_saved_today_total = df_today['total_amount_saved'].sum()
        amount_saved_yesterday_total = df_yesterday['total_amount_saved'].sum()
        amount_saved_day_before_yesterday_total = df_day_before_yesterday['total_amount_saved'].sum()
        amount_saved_week_total = df_week['total_amount_saved'].sum()
        amount_saved_last_week_total = df_last_week['total_amount_saved'].sum()
        amount_saved_last_2_weeks_total = df_last_2_weeks['total_amount_saved'].sum()
        amount_saved_month_total = df_month['total_amount_saved'].sum()
        amount_saved_last_month_total = df_last_month['total_amount_saved'].sum()
        amount_saved_last_2_months_total = df_last_2_months['total_amount_saved'].sum()
        amount_saved_year_total = df_year['total_amount_saved'].sum()
        amount_saved_last_year_total = df_last_year['total_amount_saved'].sum()
        amount_saved_last_2_years_total = df_last_2_years['total_amount_saved'].sum()
        amount_saved_all_time_total = filtered_data['total_amount_saved'].sum()

        # Aggregate data for portfolio outstanding
        open_port_today_total = df_today['open_portfolio'].sum()
        open_port_yesterday_total = df_yesterday['open_portfolio'].sum()
        open_port_day_before_yesterday_total = df_day_before_yesterday['open_portfolio'].sum()
        open_port_week_total = df_week['open_portfolio'].sum()
        open_port_last_week_total = df_last_week['open_portfolio'].sum()
        open_port_last_2_weeks_total = df_last_2_weeks['open_portfolio'].sum()
        open_port_month_total = df_month['open_portfolio'].sum()
        open_port_last_month_total = df_last_month['open_portfolio'].sum()
        open_port_last_2_months_total = df_last_2_months['open_portfolio'].sum()
        open_port_year_total = df_year['open_portfolio'].sum()
        open_port_last_year_total = df_last_year['open_portfolio'].sum()
        open_port_last_2_years_total = df_last_2_years['open_portfolio'].sum()
        open_port_all_time_total = filtered_data['open_portfolio'].sum()

        # Backend Open Portfolio each period
        backend_open_port_today_total = df_today['backend_open_port'].sum()
        backend_open_port_yesterday_total = df_yesterday['backend_open_port'].sum()
        backend_open_port_day_before_yesterday_total = df_day_before_yesterday['backend_open_port'].sum()
        backend_open_port_week_total = df_week['backend_open_port'].sum()
        backend_open_port_last_week_total = df_last_week['backend_open_port'].sum()
        backend_open_port_last_2_weeks_total = df_last_2_weeks['backend_open_port'].sum()
        backend_open_port_month_total = df_month['backend_open_port'].sum()
        backend_open_port_last_month_total = df_last_month['backend_open_port'].sum()
        backend_open_port_last_2_months_total = df_last_2_months['backend_open_port'].sum()
        backend_open_port_year_total = df_year['backend_open_port'].sum()
        backend_open_port_last_year_total = df_last_year['backend_open_port'].sum()
        backend_open_port_last_2_years_total = df_last_2_years['backend_open_port'].sum()
        backend_open_port_all_time_total = filtered_data['backend_open_port'].sum()

        # 1–30 days
        past_due_1_30 = past_due_date[(past_due_date['past_maturity'] >= 1) & (past_due_date['past_maturity'] <= 30)]
        sum_1_30 = past_due_1_30['open_portfolio'].sum()
        # 31–45 days
        past_due_31_45 = past_due_date[(past_due_date['past_maturity'] >= 31) & (past_due_date['past_maturity'] <= 45)]
        sum_31_45 = past_due_31_45['open_portfolio'].sum()
        # 46–60 days
        past_due_46_60 = past_due_date[(past_due_date['past_maturity'] >= 46) & (past_due_date['past_maturity'] <= 60)]
        sum_46_60 = past_due_46_60['open_portfolio'].sum()
        # 61–90 days
        past_due_61_90 = past_due_date[(past_due_date['past_maturity'] >= 61) & (past_due_date['past_maturity'] <= 90)]
        sum_61_90 = past_due_61_90['open_portfolio'].sum()
        # 90+ days
        past_due_90_plus = past_due_date[past_due_date['past_maturity'] > 90]
        sum_90_plus = past_due_90_plus['open_portfolio'].sum()

        # Amount Due each period
        exp_till_today_total = exp_till_today_['exp_repay_today'].sum()
        exp_till_yesterday_total = exp_till_yesterday_['exp_repay_today'].sum()
        exp_till_ThisWeek_total = exp_till_ThisWeek_['exp_repay_today'].sum()
        exp_till_ThisMonth_total = exp_till_ThisMonth_['exp_repay_today'].sum()
        exp_till_ThisYear_total = exp_till_ThisYear_['exp_repay_today'].sum()
        exp_all_time_total = exp_all_time['exp_repay_today'].sum()

        # Expected Collection each period
        backend_exp_till_today_total = backend_exp_till_today_['backend_exp_repay_today'].sum()
        backend_exp_till_yesterday_total = backend_exp_till_yesterday_['backend_exp_repay_today'].sum()
        backend_exp_till_ThisWeek_total = backend_exp_till_ThisWeek_['backend_exp_repay_today'].sum()
        backend_exp_till_ThisMonth_total = backend_exp_till_ThisMonth_['backend_exp_repay_today'].sum()
        backend_exp_till_ThisYear_total = backend_exp_till_ThisYear_['backend_exp_repay_today'].sum()
        backend_exp_all_time_total = backend_exp_all_time['backend_exp_repay_today'].sum()

        # Missed Repayment each period
        missed_repayment_today = max(0, exp_till_today_total-repay_today_total)
        missed_repayment_yesterday = max(0, exp_till_yesterday_total-repay_yesterday_total)
        missed_repayment_this_week = max(0, exp_till_ThisWeek_total-repay_week_total)
        missed_repayment_this_month = max(0, exp_till_ThisMonth_total-repay_month_total)
        missed_repayment_this_year = max(0, exp_till_ThisYear_total-repay_year_total)
        missed_repayment_all_time = max(0, exp_all_time_total-repay_all_time_total)

        # Backend Missed Repayment each period
        backend_missed_repayment_today = max(0, backend_exp_till_today_total-repay_today_total)
        backend_missed_repayment_yesterday = max(0, backend_exp_till_yesterday_total-repay_yesterday_total)
        backend_missed_repayment_this_week = max(0, backend_exp_till_ThisWeek_total-repay_week_total)
        backend_missed_repayment_this_month = max(0, backend_exp_till_ThisMonth_total-repay_month_total)
        backend_missed_repayment_this_year = max(0, backend_exp_till_ThisYear_total-repay_year_total)
        backend_missed_repayment_all_time = max(0, backend_exp_all_time_total-repay_all_time_total)

    ######################################################################### Create function to give growth rate ####################################################################################

        # Function to calculate growth rate
        def calculate_growth_rate(current, previous):
            if previous == 0:  # Avoid division by zero
                return 0
            return ((current - previous) / previous) * 100

        # Compute growth rates for Users
        user_growth_today = calculate_growth_rate(user_today_total, user_yesterday_total)
        user_growth_yesterday = calculate_growth_rate(user_yesterday_total, user_day_before_yesterday_total)
        user_growth_week = calculate_growth_rate(user_week_total, user_last_week_total)
        user_growth_last_week = calculate_growth_rate(user_last_week_total, user_last_2_weeks_total)
        user_growth_month = calculate_growth_rate(user_month_total, user_last_month_total)
        user_growth_last_month = calculate_growth_rate(user_last_month_total, user_last_2_months_total)
        user_growth_year = calculate_growth_rate(user_year_total, user_last_year_total)
        user_growth_last_year = calculate_growth_rate(user_last_year_total, user_last_2_years_total)

        # Compute growth rates for Disbursement
        disbursed_growth_today = calculate_growth_rate(disbursed_today_total, disbursed_yesterday_total)
        disbursed_growth_yesterday = calculate_growth_rate(disbursed_yesterday_total, disbursed_day_before_yesterday_total)
        disbursed_growth_week = calculate_growth_rate(disbursed_week_total, disbursed_last_week_total)
        disbursed_growth_last_week = calculate_growth_rate(disbursed_last_week_total, disbursed_last_2_weeks_total)
        disbursed_growth_month = calculate_growth_rate(disbursed_month_total, disbursed_last_month_total)
        disbursed_growth_last_month = calculate_growth_rate(disbursed_last_month_total, disbursed_last_2_months_total)
        disbursed_growth_year = calculate_growth_rate(disbursed_year_total, disbursed_last_year_total)
        disbursed_growth_last_year = calculate_growth_rate(disbursed_last_year_total, disbursed_last_2_years_total)

        # Compute growth rates for Actual Disbursed
        actual_disbursed_growth_today = calculate_growth_rate(actual_disbursed_today_total, actual_disbursed_yesterday_total)
        actual_disbursed_growth_yesterday = calculate_growth_rate(actual_disbursed_yesterday_total, actual_disbursed_day_before_yesterday_total)
        actual_disbursed_growth_week = calculate_growth_rate(actual_disbursed_week_total, actual_disbursed_last_week_total)
        actual_disbursed_growth_last_week = calculate_growth_rate(actual_disbursed_last_week_total, actual_disbursed_last_2_weeks_total)
        actual_disbursed_growth_month = calculate_growth_rate(actual_disbursed_month_total, actual_disbursed_last_month_total)
        actual_disbursed_growth_last_month = calculate_growth_rate(actual_disbursed_last_month_total, actual_disbursed_last_2_months_total)
        actual_disbursed_growth_year = calculate_growth_rate(actual_disbursed_year_total, actual_disbursed_last_year_total)
        actual_disbursed_growth_last_year = calculate_growth_rate(actual_disbursed_last_year_total, actual_disbursed_last_2_years_total)

        # Compute growth rates for amount saved and fees
        amount_saved_growth_today = calculate_growth_rate(amount_saved_today_total, amount_saved_yesterday_total)
        amount_saved_growth_yesterday = calculate_growth_rate(amount_saved_yesterday_total, amount_saved_day_before_yesterday_total)
        amount_saved_growth_week = calculate_growth_rate(amount_saved_week_total, amount_saved_last_week_total)
        amount_saved_growth_last_week = calculate_growth_rate(amount_saved_last_week_total, amount_saved_last_2_weeks_total)
        amount_saved_growth_month = calculate_growth_rate(amount_saved_month_total, amount_saved_last_month_total)
        amount_saved_growth_last_month = calculate_growth_rate(amount_saved_last_month_total, amount_saved_last_2_months_total)
        amount_saved_growth_year = calculate_growth_rate(amount_saved_year_total, amount_saved_last_year_total)
        amount_saved_growth_last_year = calculate_growth_rate(amount_saved_last_year_total, amount_saved_last_2_years_total)

        # Compute growth rates for Open Portfolio
        open_port_growth_today = calculate_growth_rate(open_port_today_total, open_port_yesterday_total)
        open_port_growth_yesterday = calculate_growth_rate(open_port_yesterday_total, open_port_day_before_yesterday_total)
        open_port_growth_week = calculate_growth_rate(open_port_week_total, open_port_last_week_total)
        open_port_growth_last_week = calculate_growth_rate(open_port_last_week_total, open_port_last_2_weeks_total)
        open_port_growth_month = calculate_growth_rate(open_port_month_total, open_port_last_month_total)
        open_port_growth_last_month = calculate_growth_rate(open_port_last_month_total, open_port_last_2_months_total)
        open_port_growth_year = calculate_growth_rate(open_port_year_total, open_port_last_year_total)
        open_port_growth_last_year = calculate_growth_rate(open_port_last_year_total, open_port_last_2_years_total)

        # Compute growth rates for Backend Open Portfolio
        backend_open_port_growth_today = calculate_growth_rate(backend_open_port_today_total, backend_open_port_yesterday_total)
        backend_open_port_growth_yesterday = calculate_growth_rate(backend_open_port_yesterday_total, backend_open_port_day_before_yesterday_total)
        backend_open_port_growth_week = calculate_growth_rate(backend_open_port_week_total, backend_open_port_last_week_total)
        backend_open_port_growth_last_week = calculate_growth_rate(backend_open_port_last_week_total, backend_open_port_last_2_weeks_total)
        backend_open_port_growth_month = calculate_growth_rate(backend_open_port_month_total, backend_open_port_last_month_total)
        backend_open_port_growth_last_month = calculate_growth_rate(backend_open_port_last_month_total, backend_open_port_last_2_months_total)
        backend_open_port_growth_year = calculate_growth_rate(backend_open_port_year_total, backend_open_port_last_year_total)
        backend_open_port_growth_last_year = calculate_growth_rate(backend_open_port_last_year_total, backend_open_port_last_2_years_total)

    ######################################################################### More customization using div class ####################################################################################

        # Custom CSS for bordered metrics
        st.markdown(
            """
            <style>
            .metric-container {
                border: 4px solid #4b9ca5; /* Sea green border */
                border-radius: 20%; /* Rounded corners */
                padding: 10px;
                text-align: center;
                margin: 15px;
                width: 100%;
                box-shadow: 3px 3px 5px rgba(0, 151, 167, 0.5); /* shadow */
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
            .metric-value {
                font-size: 14px;
                font-weight: bold;
                white-space: normal;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .metric-delta {
                font-size: 12px;
                font-weight: bold;
                margin-top: 5px;
                margin-bottom: 5px;
            }
            .metric-delta-positive {
                color: #4CAF50; /* Green for positive deltas */
            }
            .metric-delta-negative {
                color: #FF5252; /* Red for negative deltas */
            }
            .metric-title {
                font-size: 17px;
                color: #777;
                font-weight: bold;
                margin-bottom: 10px;
                white-space: normal;
                text-align: center;
            }
            .metric-label {
                font-size: 15px;
                color: #555;
                font-weight: lighter;
                white-space: normal;
                text-align: center;
            }
            </style>
            """, unsafe_allow_html=True
        )

        # Custom metric display function for entire category
        def bordered_category(title, metrics):
            st.markdown(f'<div class="metric-container"><div class="metric-title">{title}</div>', unsafe_allow_html=True)
            for label1, value1, delta1, label2, value2, delta2 in metrics:
                st.markdown(
                    f"""
                    <div style="display: flex; justify-content: space-between; width: 100%;">
                        <div style="text-align: center; flex: 1; margin-left:7%; padding: 10px;">
                            <div class="metric-label">{label1}</div>
                            <div class="metric-value">{value1}</div>
                            <div class='metric-delta {"metric-delta-positive" if delta1 >= 0 else "metric-delta-negative"}'>{delta1:+.2f}%</div>
                        </div>
                        <div style="text-align: center; flex: 1; padding: 10px;">
                            <div class="metric-label">{label2}</div>
                            <div class="metric-value">{value2}</div>
                            <div class='metric-delta {"metric-delta-positive" if delta2 >= 0 else "metric-delta-negative"}'>{delta2:+.2f}%</div>
                        </div>
                    </div>
                    <hr style="margin:0; width:90%; display:block; margin-left:10%; margin-right:0%;">
                    """, unsafe_allow_html=True
                )
            st.markdown("</div>", unsafe_allow_html=True)  # Close container

        # Create columns for each metric category
        col1, col2, col3, col4 = st.columns(4)
        col5, col6, col7, col8 = st.columns(4)
        col9, col10, col11, col12 = st.columns(4)

        # USERS
        with col1:
            bordered_category("Users", [
                ("Today", f"{user_today_total:,} users", user_growth_today, "Yesterday", f"{user_yesterday_total:,} users", user_growth_yesterday),
                ("This Week", f"{user_week_total:,} users", user_growth_last_week, "This Month", f"{user_month_total:,} users", user_growth_last_month),
                ("This Year", f"{user_year_total:,} users", user_growth_last_year, "All-Time", f"{user_all_time_total:,} users", 0),
            ])

        # DISBURSEMENT
        with col2:
            bordered_category("Disbursement", [
                ("Today", f"N{disbursed_today_total:,.2f}", disbursed_growth_today, "Yesterday", f"N{disbursed_yesterday_total:,.2f}", disbursed_growth_yesterday),
                ("This Week", f"N{disbursed_week_total:,.2f}", disbursed_growth_last_week, "This Month", f"N{disbursed_month_total:,.2f}", disbursed_growth_last_month),
                ("This Year", f"N{disbursed_year_total:,.2f}", disbursed_growth_last_year, "All-Time", f"N{disbursed_all_time_total:,.2f}", 0),
            ])

        # ACTUAL DISBURSEMENT
        with col3:
            bordered_category("Actual Disbursement", [
                ("Today", f"N{actual_disbursed_today_total:,.2f}", actual_disbursed_growth_today, "Yesterday", f"N{actual_disbursed_yesterday_total:,.2f}", actual_disbursed_growth_yesterday),
                ("This Week", f"N{actual_disbursed_week_total:,.2f}", actual_disbursed_growth_last_week, "This Month", f"N{actual_disbursed_month_total:,.2f}", actual_disbursed_growth_last_month),
                ("This Year", f"N{actual_disbursed_year_total:,.2f}", actual_disbursed_growth_last_year, "All-Time", f"N{actual_disbursed_all_time_total:,.2f}", 0),
            ])

        # BACKEND PORTFOLIO OUTSTANDING
        with col4:
            bordered_category("Backend Portfolio Outstanding", [
                ("Today", f"N{backend_open_port_today_total:,.2f}", backend_open_port_growth_today, "Yesterday", f"N{backend_open_port_yesterday_total:,.2f}", backend_open_port_growth_yesterday),
                ("This Week", f"N{backend_open_port_week_total:,.2f}", backend_open_port_growth_last_week, "This Month", f"N{backend_open_port_month_total:,.2f}", backend_open_port_growth_last_month),
                ("This Year", f"N{backend_open_port_year_total:,.2f}", backend_open_port_growth_last_year, "All-Time", f"N{backend_open_port_all_time_total:,.2f}", 0),
            ])

        # REPAYMENT
        with col5:
            bordered_category("Repayment", [
                ("Today", f"N{repay_today_total:,.2f}", ((repay_today_total/backend_exp_till_today_total)*100).round(2), "Yesterday", f"N{repay_yesterday_total:,.2f}", ((repay_yesterday_total/backend_exp_till_yesterday_total)*100).round(2)),
                ("This Week", f"N{repay_week_total:,.2f}", ((repay_week_total/backend_exp_till_ThisWeek_total)*100).round(2), "This Month", f"N{repay_month_total:,.2f}", ((repay_month_total/backend_exp_till_ThisMonth_total)*100).round(2)),
                ("This Year", f"N{repay_year_total:,.2f}", ((repay_year_total/backend_exp_till_ThisYear_total)*100).round(2), "All-Time", f"N{repay_all_time_total:,.2f}", ((repay_all_time_total/backend_exp_all_time_total)*100).round(2)),
            ])

        # EXPECTED COLLECTION
        with col6:
            bordered_category("Expected Collection", [
                ("Today", f"N{exp_till_today_total:,.2f}", ((repay_today_total/exp_till_today_total)*100).round(2), "Yesterday", f"N{exp_till_yesterday_total:,.2f}", ((repay_yesterday_total/exp_till_yesterday_total)*100).round(2)),
                ("This Week", f"N{exp_till_ThisWeek_total:,.2f}", ((repay_week_total/exp_till_ThisWeek_total)*100).round(2), "This Month", f"N{exp_till_ThisMonth_total:,.2f}", ((repay_month_total/exp_till_ThisMonth_total)*100).round(2)),
                ("This Year", f"N{exp_till_ThisYear_total:,.2f}", ((repay_year_total/exp_till_ThisYear_total)*100).round(2), "All-Time", f"N{exp_all_time_total:,.2f}", ((repay_all_time_total/exp_all_time_total)*100).round(2)),
            ])

        # PAST MATURITY
        with col7:
            bordered_category("Past Maturity", [
                ("Late Repayment", f"N{sum_1_30:,.2f}", 0, "31 - 45 Days", f"N{sum_31_45:,.2f}", 0),
                ("46 - 60 Days", f"N{sum_46_60:,.2f}", 0, "61 - 90 Days", f"N{sum_61_90:,.2f}", 0),
                ("91 Days & Above", f"N{sum_90_plus:,.2f}", 0, "All-Time", f"N{(past_due_date["open_portfolio"].sum().round(2)):,.2f}", 0),
            ])

        # PORTFOLIO OUTSTANDING
        with col8:
            bordered_category("Portfolio Outstanding", [
                ("Today", f"N{open_port_today_total:,.2f}", open_port_growth_today, "Yesterday", f"N{open_port_yesterday_total:,.2f}", open_port_growth_yesterday),
                ("This Week", f"N{open_port_week_total:,.2f}", open_port_growth_last_week, "This Month", f"N{open_port_month_total:,.2f}", open_port_growth_last_month),
                ("This Year", f"N{open_port_year_total:,.2f}", open_port_growth_last_year, "All-Time", f"N{open_port_all_time_total:,.2f}", 0),
            ])

        # LOAN REPAYMENTS
        with col9:
            bordered_category("Total Paid", [
                ("Today", f"N{total_paid_only_today_total:,.2f}", 0, "Yesterday", f"N{total_paid_only_yesterday_total:,.2f}", 0),
                ("This Week", f"N{total_paid_only_week_total:,.2f}", 0, "This Month", f"N{total_paid_only_month_total:,.2f}", 0),
                ("This Year", f"N{total_paid_only_year_total:,.2f}", 0, "All-Time", f"N{total_paid_only_all_time_total:,.2f}", 0),
            ])

        # SAVINGS AND FEES
        with col10:
            bordered_category("Savings", [
                ("Today", f"N{amount_saved_today_total:,.2f}", amount_saved_growth_today, "Yesterday", f"N{amount_saved_yesterday_total:,.2f}", amount_saved_growth_yesterday),
                ("This Week", f"N{amount_saved_week_total:,.2f}", amount_saved_growth_last_week, "This Month", f"N{amount_saved_month_total:,.2f}", amount_saved_growth_last_month),
                ("This Year", f"N{amount_saved_year_total:,.2f}", amount_saved_growth_last_year, "All-Time", f"N{amount_saved_all_time_total:,.2f}", 0),
            ])

        # MISSED REPAYMENT
        with col11:
            bordered_category("Missed Repayment", [
                ("Today", f"N{missed_repayment_today:,.2f}", ((missed_repayment_today/exp_till_today_total)*100).round(2), "Yesterday", f"N{missed_repayment_yesterday:,.2f}", ((missed_repayment_yesterday/exp_till_yesterday_total)*100).round(2)),
                ("This Week", f"N{missed_repayment_this_week:,.2f}", ((missed_repayment_this_week/exp_till_ThisWeek_total)*100).round(2), "This Month", f"N{missed_repayment_this_month:,.2f}", ((missed_repayment_this_month/exp_till_ThisMonth_total)*100).round(2)),
                ("This Year", f"N{missed_repayment_this_year:,.2f}", ((missed_repayment_this_year/exp_till_ThisYear_total)*100).round(2), "All-Time", f"N{missed_repayment_all_time:,.2f}", ((missed_repayment_all_time/exp_all_time_total)*100).round(2)),
            ])

        # BACKEND MISSED REPAYMENT
        with col12:
            bordered_category("Backend Missed Repayment", [
                ("Today", f"N{backend_missed_repayment_today:,.2f}", ((backend_missed_repayment_today/backend_exp_till_today_total)*100).round(2), "Yesterday", f"N{backend_missed_repayment_yesterday:,.2f}", ((backend_missed_repayment_yesterday/backend_exp_till_yesterday_total)*100).round(2)),
                ("This Week", f"N{backend_missed_repayment_this_week:,.2f}", ((backend_missed_repayment_this_week/backend_exp_till_ThisWeek_total)*100).round(2), "This Month", f"N{backend_missed_repayment_this_month:,.2f}", ((backend_missed_repayment_this_month/backend_exp_till_ThisMonth_total)*100).round(2)),
                ("This Year", f"N{backend_missed_repayment_this_year:,.2f}", ((backend_missed_repayment_this_year/backend_exp_till_ThisYear_total)*100).round(2), "All-Time", f"N{backend_missed_repayment_all_time:,.2f}", ((backend_missed_repayment_all_time/backend_exp_all_time_total)*100).round(2)),
            ])

        st.markdown("---")

    ################################################################################ Repayment Progress ##########################################################################################

        # Filter today's repayment data
        filtered_repayment_df_today = filtered_repayment_df[filtered_repayment_df['paid_date'] == today]

        # Group by date and get sum of repayments
        repayment_made = filtered_repayment_df_today.groupby('paid_date', as_index=False)['repayment_by_date'].sum()

        # Get the total expected repayment (sum over all days in exp_till_today_)
        total_expected = exp_till_today_['exp_repay_today'].sum()

        # Add the expected repayment column
        repayment_made['exp_repay_today'] = total_expected

        # Progress in %
        repayment_made['progress_%'] = (repayment_made['repayment_by_date'] / repayment_made['exp_repay_today']) * 100

        # Display section
        st.markdown(f"### 💰 Repayment Progress for {today.strftime('%Y-%m-%d')}")

        # Create a nice progress display
        for _, row in repayment_made.iterrows():
            actual = row['repayment_by_date']
            expected = row['exp_repay_today']
            progress = row['progress_%']
            unpaid = 100 - progress

            # Display with better layout
            st.markdown(f"**Expected Repayment:** ₦{expected:,.2f} | **Actual Repayment:** ₦{actual:,.2f}")

            # Stylish progress bar with both paid & unpaid
            paid_color = "#28a745" if progress >= 100 else "#ffc107" if progress >= 70 else "#dc3545"
            unpaid_color = "#e0e0e0"

            st.markdown(
                f"""
                <div style="background-color:#f0f0f0; border-radius:20px; height:30px; overflow:hidden; border: 1px solid #ddd; display:flex;">
                    <div style="width:{min(progress, 100):.2f}%; background-color:{paid_color}; height:100%;
                                display:flex; align-items:center; justify-content:center; color:white; font-weight:bold;">
                        {progress:.2f}% Collected
                    </div>
                    <div style="width:{max(unpaid, 0):.2f}%; background-color:{unpaid_color}; height:100%;
                                display:flex; align-items:center; justify-content:center; color:black; font-weight:bold;">
                        {unpaid:.2f}% Not Collected
                    </div>
                </div>
                """,
                unsafe_allow_html=True
            )

        st.markdown("---")

    ################################################################################ Charts and Plots ##########################################################################################

        # Cached Chart Generators
        @st.cache_data(show_spinner=False)
        def generate_loan_status_chart(filtered_df):
            status_counts = filtered_df['status'].value_counts()
            fig = px.pie(
                names=status_counts.index,
                values=status_counts.values,
                title="Loan Status Distribution",
                color_discrete_sequence=px.colors.sequential.Viridis
            )
            fig.update_traces(hoverinfo="label+percent+value")
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",
                plot_bgcolor="rgba(0, 0, 0, 0)",
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}
            )
            return fig

        @st.cache_data(show_spinner=False)
        def generate_loan_type_chart(filtered_df):
            loan_type_counts = filtered_df['loan_type'].value_counts()
            fig = px.pie(
                names=loan_type_counts.index,
                values=loan_type_counts.values,
                title="Loan Type Distribution",
                color_discrete_sequence=px.colors.sequential.Cividis,
                hole=0.5
            )
            fig.update_traces(hoverinfo="label+percent+value")
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",
                plot_bgcolor="rgba(0, 0, 0, 0)",
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}
            )
            return fig

        # Loan Status and Performance Distribution
        col1, col2 = st.columns(2)

        with col1:
            st.plotly_chart(generate_loan_status_chart(filtered_data), use_container_width=True)

        with col2:
            st.plotly_chart(generate_loan_type_chart(filtered_data), use_container_width=True)

    ############################################################################ Tab 2 #######################################################################################

    with tab2:

    ############################################################## Gets repayment analysis #################################################################################

        # Escrow repayments
        escrow_df_today = escrow_repayment[escrow_repayment['paid_date'] == today]
        escrow_df_yesterday = escrow_repayment[escrow_repayment['paid_date'] == yesterday]
        escrow_df_day_before_yesterday = escrow_repayment[escrow_repayment['paid_date'] == day_before_yesterday]
        escrow_df_week = escrow_repayment[escrow_repayment['paid_date'] >= start_of_week]
        escrow_df_last_week = escrow_repayment[
            (escrow_repayment['paid_date'] >= start_of_last_week) &
            (escrow_repayment['paid_date'] < start_of_week)
        ]
        escrow_df_last_2_weeks = escrow_repayment[
            (escrow_repayment['paid_date'] >= start_of_last_2_weeks) &
            (escrow_repayment['paid_date'] < start_of_last_week)
        ]
        escrow_df_month = escrow_repayment[escrow_repayment['paid_date'] >= start_of_month]
        escrow_df_last_month = escrow_repayment[
            (escrow_repayment['paid_date'] >= start_of_last_month) &
            (escrow_repayment['paid_date'] < start_of_month)
        ]
        escrow_df_last_2_months = escrow_repayment[
            (escrow_repayment['paid_date'] >= start_of_last_2_months) &
            (escrow_repayment['paid_date'] < start_of_last_month)
        ]
        escrow_df_year = escrow_repayment[escrow_repayment['paid_date'] >= start_of_year]
        escrow_df_last_year = escrow_repayment[
            (escrow_repayment['paid_date'] >= start_of_last_year) &
            (escrow_repayment['paid_date'] < start_of_year)
        ]
        escrow_df_last_2_years = escrow_repayment[
            (escrow_repayment['paid_date'] >= start_of_last_2_years) &
            (escrow_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        escrow_today_total = escrow_df_today['repayment_by_date'].sum()
        escrow_yesterday_total = escrow_df_yesterday['repayment_by_date'].sum()
        escrow_day_before_yesterday_total = escrow_df_day_before_yesterday['repayment_by_date'].sum()
        escrow_week_total = escrow_df_week['repayment_by_date'].sum()
        escrow_last_week_total = escrow_df_last_week['repayment_by_date'].sum()
        escrow_last_2_week_total = escrow_df_last_2_weeks['repayment_by_date'].sum()
        escrow_month_total = escrow_df_month['repayment_by_date'].sum()
        escrow_last_month_total = escrow_df_last_month['repayment_by_date'].sum()
        escrow_last_2_month_total = escrow_df_last_2_months['repayment_by_date'].sum()
        escrow_year_total = escrow_df_year['repayment_by_date'].sum()
        escrow_last_year_total = escrow_df_last_year['repayment_by_date'].sum()
        escrow_last_2_year_total = escrow_df_last_2_years['repayment_by_date'].sum()
        escrow_all_time_total = escrow_repayment['repayment_by_date'].sum()

        # Top up repayments
        top_up_df_today = top_up_repayment[top_up_repayment['paid_date'] == today]
        top_up_df_yesterday = top_up_repayment[top_up_repayment['paid_date'] == yesterday]
        top_up_df_day_before_yesterday = top_up_repayment[top_up_repayment['paid_date'] == day_before_yesterday]
        top_up_df_week = top_up_repayment[top_up_repayment['paid_date'] >= start_of_week]
        top_up_df_last_week = top_up_repayment[
            (top_up_repayment['paid_date'] >= start_of_last_week) &
            (top_up_repayment['paid_date'] < start_of_week)
        ]
        top_up_df_last_2_weeks = top_up_repayment[
            (top_up_repayment['paid_date'] >= start_of_last_2_weeks) &
            (top_up_repayment['paid_date'] < start_of_last_week)
        ]
        top_up_df_month = top_up_repayment[top_up_repayment['paid_date'] >= start_of_month]
        top_up_df_last_month = top_up_repayment[
            (top_up_repayment['paid_date'] >= start_of_last_month) &
            (top_up_repayment['paid_date'] < start_of_month)
        ]
        top_up_df_last_2_months = top_up_repayment[
            (top_up_repayment['paid_date'] >= start_of_last_2_months) &
            (top_up_repayment['paid_date'] < start_of_last_month)
        ]
        top_up_df_year = top_up_repayment[top_up_repayment['paid_date'] >= start_of_year]
        top_up_df_last_year = top_up_repayment[
            (top_up_repayment['paid_date'] >= start_of_last_year) &
            (top_up_repayment['paid_date'] < start_of_year)
        ]
        top_up_df_last_2_years = top_up_repayment[
            (top_up_repayment['paid_date'] >= start_of_last_2_years) &
            (top_up_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        top_up_today_total = top_up_df_today['repayment_by_date'].sum()
        top_up_yesterday_total = top_up_df_yesterday['repayment_by_date'].sum()
        top_up_day_before_yesterday_total = top_up_df_day_before_yesterday['repayment_by_date'].sum()
        top_up_week_total = top_up_df_week['repayment_by_date'].sum()
        top_up_last_week_total = top_up_df_last_week['repayment_by_date'].sum()
        top_up_last_2_week_total = top_up_df_last_2_weeks['repayment_by_date'].sum()
        top_up_month_total = top_up_df_month['repayment_by_date'].sum()
        top_up_last_month_total = top_up_df_last_month['repayment_by_date'].sum()
        top_up_last_2_month_total = top_up_df_last_2_months['repayment_by_date'].sum()
        top_up_year_total = top_up_df_year['repayment_by_date'].sum()
        top_up_last_year_total = top_up_df_last_year['repayment_by_date'].sum()
        top_up_last_2_year_total = top_up_df_last_2_years['repayment_by_date'].sum()
        top_up_all_time_total = top_up_repayment['repayment_by_date'].sum()

        # Agent Debit repayments
        agent_debit_df_today = agent_debit_repayment[agent_debit_repayment['paid_date'] == today]
        agent_debit_df_yesterday = agent_debit_repayment[agent_debit_repayment['paid_date'] == yesterday]
        agent_debit_df_day_before_yesterday = agent_debit_repayment[agent_debit_repayment['paid_date'] == day_before_yesterday]
        agent_debit_df_week = agent_debit_repayment[agent_debit_repayment['paid_date'] >= start_of_week]
        agent_debit_df_last_week = agent_debit_repayment[
            (agent_debit_repayment['paid_date'] >= start_of_last_week) &
            (agent_debit_repayment['paid_date'] < start_of_week)
        ]
        agent_debit_df_last_2_weeks = agent_debit_repayment[
            (agent_debit_repayment['paid_date'] >= start_of_last_2_weeks) &
            (agent_debit_repayment['paid_date'] < start_of_last_week)
        ]
        agent_debit_df_month = agent_debit_repayment[agent_debit_repayment['paid_date'] >= start_of_month]
        agent_debit_df_last_month = agent_debit_repayment[
            (agent_debit_repayment['paid_date'] >= start_of_last_month) &
            (agent_debit_repayment['paid_date'] < start_of_month)
        ]
        agent_debit_df_last_2_months = agent_debit_repayment[
            (agent_debit_repayment['paid_date'] >= start_of_last_2_months) &
            (agent_debit_repayment['paid_date'] < start_of_last_month)
        ]
        agent_debit_df_year = agent_debit_repayment[agent_debit_repayment['paid_date'] >= start_of_year]
        agent_debit_df_last_year = agent_debit_repayment[
            (agent_debit_repayment['paid_date'] >= start_of_last_year) &
            (agent_debit_repayment['paid_date'] < start_of_year)
        ]
        agent_debit_df_last_2_years = agent_debit_repayment[
            (agent_debit_repayment['paid_date'] >= start_of_last_2_years) &
            (agent_debit_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        agent_debit_today_total = agent_debit_df_today['repayment_by_date'].sum()
        agent_debit_yesterday_total = agent_debit_df_yesterday['repayment_by_date'].sum()
        agent_debit_day_before_yesterday_total = agent_debit_df_day_before_yesterday['repayment_by_date'].sum()
        agent_debit_week_total = agent_debit_df_week['repayment_by_date'].sum()
        agent_debit_last_week_total = agent_debit_df_last_week['repayment_by_date'].sum()
        agent_debit_last_2_week_total = agent_debit_df_last_2_weeks['repayment_by_date'].sum()
        agent_debit_month_total = agent_debit_df_month['repayment_by_date'].sum()
        agent_debit_last_month_total = agent_debit_df_last_month['repayment_by_date'].sum()
        agent_debit_last_2_month_total = agent_debit_df_last_2_months['repayment_by_date'].sum()
        agent_debit_year_total = agent_debit_df_year['repayment_by_date'].sum()
        agent_debit_last_year_total = agent_debit_df_last_year['repayment_by_date'].sum()
        agent_debit_last_2_year_total = agent_debit_df_last_2_years['repayment_by_date'].sum()
        agent_debit_all_time_total = agent_debit_repayment['repayment_by_date'].sum()

        # Transfer repayments
        transfer_df_today = transfer_repayment[transfer_repayment['paid_date'] == today]
        transfer_df_yesterday = transfer_repayment[transfer_repayment['paid_date'] == yesterday]
        transfer_df_day_before_yesterday = transfer_repayment[transfer_repayment['paid_date'] == day_before_yesterday]
        transfer_df_week = transfer_repayment[transfer_repayment['paid_date'] >= start_of_week]
        transfer_df_last_week = transfer_repayment[
            (transfer_repayment['paid_date'] >= start_of_last_week) &
            (transfer_repayment['paid_date'] < start_of_week)
        ]
        transfer_df_last_2_weeks = transfer_repayment[
            (transfer_repayment['paid_date'] >= start_of_last_2_weeks) &
            (transfer_repayment['paid_date'] < start_of_last_week)
        ]
        transfer_df_month = transfer_repayment[transfer_repayment['paid_date'] >= start_of_month]
        transfer_df_last_month = transfer_repayment[
            (transfer_repayment['paid_date'] >= start_of_last_month) &
            (transfer_repayment['paid_date'] < start_of_month)
        ]
        transfer_df_last_2_months = transfer_repayment[
            (transfer_repayment['paid_date'] >= start_of_last_2_months) &
            (transfer_repayment['paid_date'] < start_of_last_month)
        ]
        transfer_df_year = transfer_repayment[transfer_repayment['paid_date'] >= start_of_year]
        transfer_df_last_year = transfer_repayment[
            (transfer_repayment['paid_date'] >= start_of_last_year) &
            (transfer_repayment['paid_date'] < start_of_year)
        ]
        transfer_df_last_2_years = transfer_repayment[
            (transfer_repayment['paid_date'] >= start_of_last_2_years) &
            (transfer_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        transfer_today_total = transfer_df_today['repayment_by_date'].sum()
        transfer_yesterday_total = transfer_df_yesterday['repayment_by_date'].sum()
        transfer_day_before_yesterday_total = transfer_df_day_before_yesterday['repayment_by_date'].sum()
        transfer_week_total = transfer_df_week['repayment_by_date'].sum()
        transfer_last_week_total = transfer_df_last_week['repayment_by_date'].sum()
        transfer_last_2_week_total = transfer_df_last_2_weeks['repayment_by_date'].sum()
        transfer_month_total = transfer_df_month['repayment_by_date'].sum()
        transfer_last_month_total = transfer_df_last_month['repayment_by_date'].sum()
        transfer_last_2_month_total = transfer_df_last_2_months['repayment_by_date'].sum()
        transfer_year_total = transfer_df_year['repayment_by_date'].sum()
        transfer_last_year_total = transfer_df_last_year['repayment_by_date'].sum()
        transfer_last_2_year_total = transfer_df_last_2_years['repayment_by_date'].sum()
        transfer_all_time_total = transfer_repayment['repayment_by_date'].sum()

        # First week repayments
        first_week_df_today = first_week_repayment[first_week_repayment['paid_date'] == today]
        first_week_df_yesterday = first_week_repayment[first_week_repayment['paid_date'] == yesterday]
        first_week_df_day_before_yesterday = first_week_repayment[first_week_repayment['paid_date'] == day_before_yesterday]
        first_week_df_week = first_week_repayment[first_week_repayment['paid_date'] >= start_of_week]
        first_week_df_last_week = first_week_repayment[
            (first_week_repayment['paid_date'] >= start_of_last_week) &
            (first_week_repayment['paid_date'] < start_of_week)
        ]
        first_week_df_last_2_weeks = first_week_repayment[
            (first_week_repayment['paid_date'] >= start_of_last_2_weeks) &
            (first_week_repayment['paid_date'] < start_of_last_week)
        ]
        first_week_df_month = first_week_repayment[first_week_repayment['paid_date'] >= start_of_month]
        first_week_df_last_month = first_week_repayment[
            (first_week_repayment['paid_date'] >= start_of_last_month) &
            (first_week_repayment['paid_date'] < start_of_month)
        ]
        first_week_df_last_2_months = first_week_repayment[
            (first_week_repayment['paid_date'] >= start_of_last_2_months) &
            (first_week_repayment['paid_date'] < start_of_last_month)
        ]
        first_week_df_year = first_week_repayment[first_week_repayment['paid_date'] >= start_of_year]
        first_week_df_last_year = first_week_repayment[
            (first_week_repayment['paid_date'] >= start_of_last_year) &
            (first_week_repayment['paid_date'] < start_of_year)
        ]
        first_week_df_last_2_years = first_week_repayment[
            (first_week_repayment['paid_date'] >= start_of_last_2_years) &
            (first_week_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        first_week_today_total = first_week_df_today['repayment_by_date'].sum()
        first_week_yesterday_total = first_week_df_yesterday['repayment_by_date'].sum()
        first_week_day_before_yesterday_total = first_week_df_day_before_yesterday['repayment_by_date'].sum()
        first_week_week_total = first_week_df_week['repayment_by_date'].sum()
        first_week_last_week_total = first_week_df_last_week['repayment_by_date'].sum()
        first_week_last_2_week_total = first_week_df_last_2_weeks['repayment_by_date'].sum()
        first_week_month_total = first_week_df_month['repayment_by_date'].sum()
        first_week_last_month_total = first_week_df_last_month['repayment_by_date'].sum()
        first_week_last_2_month_total = first_week_df_last_2_months['repayment_by_date'].sum()
        first_week_year_total = first_week_df_year['repayment_by_date'].sum()
        first_week_last_year_total = first_week_df_last_year['repayment_by_date'].sum()
        first_week_last_2_year_total = first_week_df_last_2_years['repayment_by_date'].sum()
        first_week_all_time_total = first_week_repayment['repayment_by_date'].sum()

        # Libertypay repayments
        libertypay_directdebit_df_today = libertypay_directdebit_repayment[libertypay_directdebit_repayment['paid_date'] == today]
        libertypay_directdebit_df_yesterday = libertypay_directdebit_repayment[libertypay_directdebit_repayment['paid_date'] == yesterday]
        libertypay_directdebit_df_day_before_yesterday = libertypay_directdebit_repayment[libertypay_directdebit_repayment['paid_date'] == day_before_yesterday]
        libertypay_directdebit_df_week = libertypay_directdebit_repayment[libertypay_directdebit_repayment['paid_date'] >= start_of_week]
        libertypay_directdebit_df_last_week = libertypay_directdebit_repayment[
            (libertypay_directdebit_repayment['paid_date'] >= start_of_last_week) &
            (libertypay_directdebit_repayment['paid_date'] < start_of_week)
        ]
        libertypay_directdebit_df_last_2_weeks = libertypay_directdebit_repayment[
            (libertypay_directdebit_repayment['paid_date'] >= start_of_last_2_weeks) &
            (libertypay_directdebit_repayment['paid_date'] < start_of_last_week)
        ]
        libertypay_directdebit_df_month = libertypay_directdebit_repayment[libertypay_directdebit_repayment['paid_date'] >= start_of_month]
        libertypay_directdebit_df_last_month = libertypay_directdebit_repayment[
            (libertypay_directdebit_repayment['paid_date'] >= start_of_last_month) &
            (libertypay_directdebit_repayment['paid_date'] < start_of_month)
        ]
        libertypay_directdebit_df_last_2_months = libertypay_directdebit_repayment[
            (libertypay_directdebit_repayment['paid_date'] >= start_of_last_2_months) &
            (libertypay_directdebit_repayment['paid_date'] < start_of_last_month)
        ]
        libertypay_directdebit_df_year = libertypay_directdebit_repayment[libertypay_directdebit_repayment['paid_date'] >= start_of_year]
        libertypay_directdebit_df_last_year = libertypay_directdebit_repayment[
            (libertypay_directdebit_repayment['paid_date'] >= start_of_last_year) &
            (libertypay_directdebit_repayment['paid_date'] < start_of_year)
        ]
        libertypay_directdebit_df_last_2_years = libertypay_directdebit_repayment[
            (libertypay_directdebit_repayment['paid_date'] >= start_of_last_2_years) &
            (libertypay_directdebit_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        libertypay_directdebit_today_total = libertypay_directdebit_df_today['repayment_by_date'].sum()
        libertypay_directdebit_yesterday_total = libertypay_directdebit_df_yesterday['repayment_by_date'].sum()
        libertypay_directdebit_day_before_yesterday_total = libertypay_directdebit_df_day_before_yesterday['repayment_by_date'].sum()
        libertypay_directdebit_week_total = libertypay_directdebit_df_week['repayment_by_date'].sum()
        libertypay_directdebit_last_week_total = libertypay_directdebit_df_last_week['repayment_by_date'].sum()
        libertypay_directdebit_last_2_week_total = libertypay_directdebit_df_last_2_weeks['repayment_by_date'].sum()
        libertypay_directdebit_month_total = libertypay_directdebit_df_month['repayment_by_date'].sum()
        libertypay_directdebit_last_month_total = libertypay_directdebit_df_last_month['repayment_by_date'].sum()
        libertypay_directdebit_last_2_month_total = libertypay_directdebit_df_last_2_months['repayment_by_date'].sum()
        libertypay_directdebit_year_total = libertypay_directdebit_df_year['repayment_by_date'].sum()
        libertypay_directdebit_last_year_total = libertypay_directdebit_df_last_year['repayment_by_date'].sum()
        libertypay_directdebit_last_2_year_total = libertypay_directdebit_df_last_2_years['repayment_by_date'].sum()
        libertypay_directdebit_all_time_total = libertypay_directdebit_repayment['repayment_by_date'].sum()

        # Lite Debit repayments
        lite_debit_df_today = lite_debit_repayment[lite_debit_repayment['paid_date'] == today]
        lite_debit_df_yesterday = lite_debit_repayment[lite_debit_repayment['paid_date'] == yesterday]
        lite_debit_df_day_before_yesterday = lite_debit_repayment[lite_debit_repayment['paid_date'] == day_before_yesterday]
        lite_debit_df_week = lite_debit_repayment[lite_debit_repayment['paid_date'] >= start_of_week]
        lite_debit_df_last_week = lite_debit_repayment[
            (lite_debit_repayment['paid_date'] >= start_of_last_week) &
            (lite_debit_repayment['paid_date'] < start_of_week)
        ]
        lite_debit_df_last_2_weeks = lite_debit_repayment[
            (lite_debit_repayment['paid_date'] >= start_of_last_2_weeks) &
            (lite_debit_repayment['paid_date'] < start_of_last_week)
        ]
        lite_debit_df_month = lite_debit_repayment[lite_debit_repayment['paid_date'] >= start_of_month]
        lite_debit_df_last_month = lite_debit_repayment[
            (lite_debit_repayment['paid_date'] >= start_of_last_month) &
            (lite_debit_repayment['paid_date'] < start_of_month)
        ]
        lite_debit_df_last_2_months = lite_debit_repayment[
            (lite_debit_repayment['paid_date'] >= start_of_last_2_months) &
            (lite_debit_repayment['paid_date'] < start_of_last_month)
        ]
        lite_debit_df_year = lite_debit_repayment[lite_debit_repayment['paid_date'] >= start_of_year]
        lite_debit_df_last_year = lite_debit_repayment[
            (lite_debit_repayment['paid_date'] >= start_of_last_year) &
            (lite_debit_repayment['paid_date'] < start_of_year)
        ]
        lite_debit_df_last_2_years = lite_debit_repayment[
            (lite_debit_repayment['paid_date'] >= start_of_last_2_years) &
            (lite_debit_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        lite_debit_today_total = lite_debit_df_today['repayment_by_date'].sum()
        lite_debit_yesterday_total = lite_debit_df_yesterday['repayment_by_date'].sum()
        lite_debit_day_before_yesterday_total = lite_debit_df_day_before_yesterday['repayment_by_date'].sum()
        lite_debit_week_total = lite_debit_df_week['repayment_by_date'].sum()
        lite_debit_last_week_total = lite_debit_df_last_week['repayment_by_date'].sum()
        lite_debit_last_2_week_total = lite_debit_df_last_2_weeks['repayment_by_date'].sum()
        lite_debit_month_total = lite_debit_df_month['repayment_by_date'].sum()
        lite_debit_last_month_total = lite_debit_df_last_month['repayment_by_date'].sum()
        lite_debit_last_2_month_total = lite_debit_df_last_2_months['repayment_by_date'].sum()
        lite_debit_year_total = lite_debit_df_year['repayment_by_date'].sum()
        lite_debit_last_year_total = lite_debit_df_last_year['repayment_by_date'].sum()
        lite_debit_last_2_year_total = lite_debit_df_last_2_years['repayment_by_date'].sum()
        lite_debit_all_time_total = lite_debit_repayment['repayment_by_date'].sum()

        # Loan Recovery repayments
        loan_recovery_df_today = loan_recovery_repayment[loan_recovery_repayment['paid_date'] == today]
        loan_recovery_df_yesterday = loan_recovery_repayment[loan_recovery_repayment['paid_date'] == yesterday]
        loan_recovery_df_day_before_yesterday = loan_recovery_repayment[loan_recovery_repayment['paid_date'] == day_before_yesterday]
        loan_recovery_df_week = loan_recovery_repayment[loan_recovery_repayment['paid_date'] >= start_of_week]
        loan_recovery_df_last_week = loan_recovery_repayment[
            (loan_recovery_repayment['paid_date'] >= start_of_last_week) &
            (loan_recovery_repayment['paid_date'] < start_of_week)
        ]
        loan_recovery_df_last_2_weeks = loan_recovery_repayment[
            (loan_recovery_repayment['paid_date'] >= start_of_last_2_weeks) &
            (loan_recovery_repayment['paid_date'] < start_of_last_week)
        ]
        loan_recovery_df_month = loan_recovery_repayment[loan_recovery_repayment['paid_date'] >= start_of_month]
        loan_recovery_df_last_month = loan_recovery_repayment[
            (loan_recovery_repayment['paid_date'] >= start_of_last_month) &
            (loan_recovery_repayment['paid_date'] < start_of_month)
        ]
        loan_recovery_df_last_2_months = loan_recovery_repayment[
            (loan_recovery_repayment['paid_date'] >= start_of_last_2_months) &
            (loan_recovery_repayment['paid_date'] < start_of_last_month)
        ]
        loan_recovery_df_year = loan_recovery_repayment[loan_recovery_repayment['paid_date'] >= start_of_year]
        loan_recovery_df_last_year = loan_recovery_repayment[
            (loan_recovery_repayment['paid_date'] >= start_of_last_year) &
            (loan_recovery_repayment['paid_date'] < start_of_year)
        ]
        loan_recovery_df_last_2_years = loan_recovery_repayment[
            (loan_recovery_repayment['paid_date'] >= start_of_last_2_years) &
            (loan_recovery_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        loan_recovery_today_total = loan_recovery_df_today['repayment_by_date'].sum()
        loan_recovery_yesterday_total = loan_recovery_df_yesterday['repayment_by_date'].sum()
        loan_recovery_day_before_yesterday_total = loan_recovery_df_day_before_yesterday['repayment_by_date'].sum()
        loan_recovery_week_total = loan_recovery_df_week['repayment_by_date'].sum()
        loan_recovery_last_week_total = loan_recovery_df_last_week['repayment_by_date'].sum()
        loan_recovery_last_2_week_total = loan_recovery_df_last_2_weeks['repayment_by_date'].sum()
        loan_recovery_month_total = loan_recovery_df_month['repayment_by_date'].sum()
        loan_recovery_last_month_total = loan_recovery_df_last_month['repayment_by_date'].sum()
        loan_recovery_last_2_month_total = loan_recovery_df_last_2_months['repayment_by_date'].sum()
        loan_recovery_year_total = loan_recovery_df_year['repayment_by_date'].sum()
        loan_recovery_last_year_total = loan_recovery_df_last_year['repayment_by_date'].sum()
        loan_recovery_last_2_year_total = loan_recovery_df_last_2_years['repayment_by_date'].sum()
        loan_recovery_all_time_total = loan_recovery_repayment['repayment_by_date'].sum()

        # Merchant Debit repayments
        merchant_debit_df_today = merchant_debit_repayment[merchant_debit_repayment['paid_date'] == today]
        merchant_debit_df_yesterday = merchant_debit_repayment[merchant_debit_repayment['paid_date'] == yesterday]
        merchant_debit_df_day_before_yesterday = merchant_debit_repayment[merchant_debit_repayment['paid_date'] == day_before_yesterday]
        merchant_debit_df_week = merchant_debit_repayment[merchant_debit_repayment['paid_date'] >= start_of_week]
        merchant_debit_df_last_week = merchant_debit_repayment[
            (merchant_debit_repayment['paid_date'] >= start_of_last_week) &
            (merchant_debit_repayment['paid_date'] < start_of_week)
        ]
        merchant_debit_df_last_2_weeks = merchant_debit_repayment[
            (merchant_debit_repayment['paid_date'] >= start_of_last_2_weeks) &
            (merchant_debit_repayment['paid_date'] < start_of_last_week)
        ]
        merchant_debit_df_month = merchant_debit_repayment[merchant_debit_repayment['paid_date'] >= start_of_month]
        merchant_debit_df_last_month = merchant_debit_repayment[
            (merchant_debit_repayment['paid_date'] >= start_of_last_month) &
            (merchant_debit_repayment['paid_date'] < start_of_month)
        ]
        merchant_debit_df_last_2_months = merchant_debit_repayment[
            (merchant_debit_repayment['paid_date'] >= start_of_last_2_months) &
            (merchant_debit_repayment['paid_date'] < start_of_last_month)
        ]
        merchant_debit_df_year = merchant_debit_repayment[merchant_debit_repayment['paid_date'] >= start_of_year]
        merchant_debit_df_last_year = merchant_debit_repayment[
            (merchant_debit_repayment['paid_date'] >= start_of_last_year) &
            (merchant_debit_repayment['paid_date'] < start_of_year)
        ]
        merchant_debit_df_last_2_years = merchant_debit_repayment[
            (merchant_debit_repayment['paid_date'] >= start_of_last_2_years) &
            (merchant_debit_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        merchant_debit_today_total = merchant_debit_df_today['repayment_by_date'].sum()
        merchant_debit_yesterday_total = merchant_debit_df_yesterday['repayment_by_date'].sum()
        merchant_debit_day_before_yesterday_total = merchant_debit_df_day_before_yesterday['repayment_by_date'].sum()
        merchant_debit_week_total = merchant_debit_df_week['repayment_by_date'].sum()
        merchant_debit_last_week_total = merchant_debit_df_last_week['repayment_by_date'].sum()
        merchant_debit_last_2_week_total = merchant_debit_df_last_2_weeks['repayment_by_date'].sum()
        merchant_debit_month_total = merchant_debit_df_month['repayment_by_date'].sum()
        merchant_debit_last_month_total = merchant_debit_df_last_month['repayment_by_date'].sum()
        merchant_debit_last_2_month_total = merchant_debit_df_last_2_months['repayment_by_date'].sum()
        merchant_debit_year_total = merchant_debit_df_year['repayment_by_date'].sum()
        merchant_debit_last_year_total = merchant_debit_df_last_year['repayment_by_date'].sum()
        merchant_debit_last_2_year_total = merchant_debit_df_last_2_years['repayment_by_date'].sum()
        merchant_debit_all_time_total = merchant_debit_repayment['repayment_by_date'].sum()

        # Saving Debit repayments
        savings_debit_df_today = savings_debit_repayment[savings_debit_repayment['paid_date'] == today]
        savings_debit_df_yesterday = savings_debit_repayment[savings_debit_repayment['paid_date'] == yesterday]
        savings_debit_df_day_before_yesterday = savings_debit_repayment[savings_debit_repayment['paid_date'] == day_before_yesterday]
        savings_debit_df_week = savings_debit_repayment[savings_debit_repayment['paid_date'] >= start_of_week]
        savings_debit_df_last_week = savings_debit_repayment[
            (savings_debit_repayment['paid_date'] >= start_of_last_week) &
            (savings_debit_repayment['paid_date'] < start_of_week)
        ]
        savings_debit_df_last_2_weeks = savings_debit_repayment[
            (savings_debit_repayment['paid_date'] >= start_of_last_2_weeks) &
            (savings_debit_repayment['paid_date'] < start_of_last_week)
        ]
        savings_debit_df_month = savings_debit_repayment[savings_debit_repayment['paid_date'] >= start_of_month]
        savings_debit_df_last_month = savings_debit_repayment[
            (savings_debit_repayment['paid_date'] >= start_of_last_month) &
            (savings_debit_repayment['paid_date'] < start_of_month)
        ]
        savings_debit_df_last_2_months = savings_debit_repayment[
            (savings_debit_repayment['paid_date'] >= start_of_last_2_months) &
            (savings_debit_repayment['paid_date'] < start_of_last_month)
        ]
        savings_debit_df_year = savings_debit_repayment[savings_debit_repayment['paid_date'] >= start_of_year]
        savings_debit_df_last_year = savings_debit_repayment[
            (savings_debit_repayment['paid_date'] >= start_of_last_year) &
            (savings_debit_repayment['paid_date'] < start_of_year)
        ]
        savings_debit_df_last_2_years = savings_debit_repayment[
            (savings_debit_repayment['paid_date'] >= start_of_last_2_years) &
            (savings_debit_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        savings_debit_today_total = savings_debit_df_today['repayment_by_date'].sum()
        savings_debit_yesterday_total = savings_debit_df_yesterday['repayment_by_date'].sum()
        savings_debit_day_before_yesterday_total = savings_debit_df_day_before_yesterday['repayment_by_date'].sum()
        savings_debit_week_total = savings_debit_df_week['repayment_by_date'].sum()
        savings_debit_last_week_total = savings_debit_df_last_week['repayment_by_date'].sum()
        savings_debit_last_2_week_total = savings_debit_df_last_2_weeks['repayment_by_date'].sum()
        savings_debit_month_total = savings_debit_df_month['repayment_by_date'].sum()
        savings_debit_last_month_total = savings_debit_df_last_month['repayment_by_date'].sum()
        savings_debit_last_2_month_total = savings_debit_df_last_2_months['repayment_by_date'].sum()
        savings_debit_year_total = savings_debit_df_year['repayment_by_date'].sum()
        savings_debit_last_year_total = savings_debit_df_last_year['repayment_by_date'].sum()
        savings_debit_last_2_year_total = savings_debit_df_last_2_years['repayment_by_date'].sum()
        savings_debit_all_time_total = savings_debit_repayment['repayment_by_date'].sum()

        # Spend Debit repayments
        spend_debit_df_today = spend_debit_repayment[spend_debit_repayment['paid_date'] == today]
        spend_debit_df_yesterday = spend_debit_repayment[spend_debit_repayment['paid_date'] == yesterday]
        spend_debit_df_day_before_yesterday = spend_debit_repayment[spend_debit_repayment['paid_date'] == day_before_yesterday]
        spend_debit_df_week = spend_debit_repayment[spend_debit_repayment['paid_date'] >= start_of_week]
        spend_debit_df_last_week = spend_debit_repayment[
            (spend_debit_repayment['paid_date'] >= start_of_last_week) &
            (spend_debit_repayment['paid_date'] < start_of_week)
        ]
        spend_debit_df_last_2_weeks = spend_debit_repayment[
            (spend_debit_repayment['paid_date'] >= start_of_last_2_weeks) &
            (spend_debit_repayment['paid_date'] < start_of_last_week)
        ]
        spend_debit_df_month = spend_debit_repayment[spend_debit_repayment['paid_date'] >= start_of_month]
        spend_debit_df_last_month = spend_debit_repayment[
            (spend_debit_repayment['paid_date'] >= start_of_last_month) &
            (spend_debit_repayment['paid_date'] < start_of_month)
        ]
        spend_debit_df_last_2_months = spend_debit_repayment[
            (spend_debit_repayment['paid_date'] >= start_of_last_2_months) &
            (spend_debit_repayment['paid_date'] < start_of_last_month)
        ]
        spend_debit_df_year = spend_debit_repayment[spend_debit_repayment['paid_date'] >= start_of_year]
        spend_debit_df_last_year = spend_debit_repayment[
            (spend_debit_repayment['paid_date'] >= start_of_last_year) &
            (spend_debit_repayment['paid_date'] < start_of_year)
        ]
        spend_debit_df_last_2_years = spend_debit_repayment[
            (spend_debit_repayment['paid_date'] >= start_of_last_2_years) &
            (spend_debit_repayment['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        spend_debit_today_total = spend_debit_df_today['repayment_by_date'].sum()
        spend_debit_yesterday_total = spend_debit_df_yesterday['repayment_by_date'].sum()
        spend_debit_day_before_yesterday_total = spend_debit_df_day_before_yesterday['repayment_by_date'].sum()
        spend_debit_week_total = spend_debit_df_week['repayment_by_date'].sum()
        spend_debit_last_week_total = spend_debit_df_last_week['repayment_by_date'].sum()
        spend_debit_last_2_week_total = spend_debit_df_last_2_weeks['repayment_by_date'].sum()
        spend_debit_month_total = spend_debit_df_month['repayment_by_date'].sum()
        spend_debit_last_month_total = spend_debit_df_last_month['repayment_by_date'].sum()
        spend_debit_last_2_month_total = spend_debit_df_last_2_months['repayment_by_date'].sum()
        spend_debit_year_total = spend_debit_df_year['repayment_by_date'].sum()
        spend_debit_last_year_total = spend_debit_df_last_year['repayment_by_date'].sum()
        spend_debit_last_2_year_total = spend_debit_df_last_2_years['repayment_by_date'].sum()
        spend_debit_all_time_total = spend_debit_repayment['repayment_by_date'].sum()

        # Create columns for each metric category
        col1, col2, col3, col4 = st.columns(4)
        col5, col6, col7, col8 = st.columns(4)
        col9, col10, col11, col12 = st.columns(4)

        with col1:
            bordered_category("Agent Debit Collections", [
                ("Today", f"N{agent_debit_today_total:,.2f}", 0, "Yesterday", f"N{agent_debit_yesterday_total:,.2f}", 0),
                ("This Week", f"N{agent_debit_week_total:,.2f}", 0, "This Month", f"N{agent_debit_month_total:,.2f}", 0),
                ("This Year", f"N{agent_debit_year_total:,.2f}", 0, "All-Time", f"N{agent_debit_all_time_total:,.2f}", 0),
            ])

        with col2:
            bordered_category("Transfer Collections", [
                ("Today", f"N{transfer_today_total:,.2f}", 0, "Yesterday", f"N{transfer_yesterday_total:,.2f}", 0),
                ("This Week", f"N{transfer_week_total:,.2f}", 0, "This Month", f"N{transfer_month_total:,.2f}", 0),
                ("This Year", f"N{transfer_year_total:,.2f}", 0, "All-Time", f"N{transfer_all_time_total:,.2f}", 0),
            ])

        with col3:
            bordered_category("Loan Recovery Collections", [
                ("Today", f"N{loan_recovery_today_total:,.2f}", 0, "Yesterday", f"N{loan_recovery_yesterday_total:,.2f}", 0),
                ("This Week", f"N{loan_recovery_week_total:,.2f}", 0, "This Month", f"N{loan_recovery_month_total:,.2f}", 0),
                ("This Year", f"N{loan_recovery_year_total:,.2f}", 0, "All-Time", f"N{loan_recovery_all_time_total:,.2f}", 0),
            ])

        with col4:
            bordered_category("Total Paid", [
                ("Today", f"N{total_paid_only_today_total:,.2f}", 0, "Yesterday", f"N{total_paid_only_yesterday_total:,.2f}", 0),
                ("This Week", f"N{total_paid_only_week_total:,.2f}", 0, "This Month", f"N{total_paid_only_month_total:,.2f}", 0),
                ("This Year", f"N{total_paid_only_year_total:,.2f}", 0, "All-Time", f"N{total_paid_only_all_time_total:,.2f}", 0),
            ])

        with col5:
            bordered_category("Escrow Collections", [
                ("Today", f"N{escrow_today_total:,.2f}", 0, "Yesterday", f"N{escrow_yesterday_total:,.2f}", 0),
                ("This Week", f"N{escrow_week_total:,.2f}", 0, "This Month", f"N{escrow_month_total:,.2f}", 0),
                ("This Year", f"N{escrow_year_total:,.2f}", 0, "All-Time", f"N{escrow_all_time_total:,.2f}", 0),
            ])

        with col6:
            bordered_category("Top Up Collections", [
                ("Today", f"N{top_up_today_total:,.2f}", 0, "Yesterday", f"N{top_up_yesterday_total:,.2f}", 0),
                ("This Week", f"N{top_up_week_total:,.2f}", 0, "This Month", f"N{top_up_month_total:,.2f}", 0),
                ("This Year", f"N{top_up_year_total:,.2f}", 0, "All-Time", f"N{top_up_all_time_total:,.2f}", 0),
            ])

        with col7:
            bordered_category("Spend Debit Collections", [
                ("Today", f"N{spend_debit_today_total:,.2f}", 0, "Yesterday", f"N{spend_debit_yesterday_total:,.2f}", 0),
                ("This Week", f"N{spend_debit_week_total:,.2f}", 0, "This Month", f"N{spend_debit_month_total:,.2f}", 0),
                ("This Year", f"N{spend_debit_year_total:,.2f}", 0, "All-Time", f"N{spend_debit_all_time_total:,.2f}", 0),
            ])

        with col8:
            bordered_category("Savings Debit Collections", [
                ("Today", f"N{savings_debit_today_total:,.2f}", 0, "Yesterday", f"N{savings_debit_yesterday_total:,.2f}", 0),
                ("This Week", f"N{savings_debit_week_total:,.2f}", 0, "This Month", f"N{savings_debit_month_total:,.2f}", 0),
                ("This Year", f"N{savings_debit_year_total:,.2f}", 0, "All-Time", f"N{savings_debit_all_time_total:,.2f}", 0),
            ])

        with col9:
            bordered_category("Merchant Debit Collections", [
                ("Today", f"N{merchant_debit_today_total:,.2f}", 0, "Yesterday", f"N{merchant_debit_yesterday_total:,.2f}", 0),
                ("This Week", f"N{merchant_debit_week_total:,.2f}", 0, "This Month", f"N{merchant_debit_month_total:,.2f}", 0),
                ("This Year", f"N{merchant_debit_year_total:,.2f}", 0, "All-Time", f"N{merchant_debit_all_time_total:,.2f}", 0),
            ])

        with col10:
            bordered_category("Lite Debit Collections", [
                ("Today", f"N{lite_debit_today_total:,.2f}", 0, "Yesterday", f"N{lite_debit_yesterday_total:,.2f}", 0),
                ("This Week", f"N{lite_debit_week_total:,.2f}", 0, "This Month", f"N{lite_debit_month_total:,.2f}", 0),
                ("This Year", f"N{lite_debit_year_total:,.2f}", 0, "All-Time", f"N{lite_debit_all_time_total:,.2f}", 0),
            ])

        with col11:
            bordered_category("LibertyPay Direct Debit", [
                ("Today", f"N{libertypay_directdebit_today_total:,.2f}", 0, "Yesterday", f"N{libertypay_directdebit_yesterday_total:,.2f}", 0),
                ("This Week", f"N{libertypay_directdebit_week_total:,.2f}", 0, "This Month", f"N{libertypay_directdebit_month_total:,.2f}", 0),
                ("This Year", f"N{libertypay_directdebit_year_total:,.2f}", 0, "All-Time", f"N{libertypay_directdebit_all_time_total:,.2f}", 0),
            ])

        with col12:
            bordered_category("First Week Collections", [
                ("Today", f"N{first_week_today_total:,.2f}", 0, "Yesterday", f"N{first_week_yesterday_total:,.2f}", 0),
                ("This Week", f"N{first_week_week_total:,.2f}", 0, "This Month", f"N{first_week_month_total:,.2f}", 0),
                ("This Year", f"N{first_week_year_total:,.2f}", 0, "All-Time", f"N{first_week_all_time_total:,.2f}", 0),
            ])

    ############################################################################ Tab 3 #######################################################################################

    with tab3:

    ############################################################## Gets 'exp_repay_column' for progress checker #################################################################################

        try:
            # Drop 'exp_repay_today'
            new_filtered_data = filtered_data.drop(columns=['exp_repay_today', 'backend_exp_repay_today'])

            # Convert date column to datetime if not already
            schedule_df['date'] = pd.to_datetime(schedule_df['date'])
            backend_schedule_df['date'] = pd.to_datetime(backend_schedule_df['date'])

            # Aggregate exp_repay_today by month
            monthly_exp_repay = (schedule_df.groupby(pd.Grouper(key='date', freq='M')).agg({'exp_repay_today' : 'sum'}).round(2).reset_index())
            backend_monthly_exp_repay = (backend_schedule_df.groupby(pd.Grouper(key='date', freq='M')).agg({'backend_exp_repay_today' : 'sum'}).round(2).reset_index())

            monthly_exp_repay['date'] = monthly_exp_repay['date'].dt.strftime('%b %Y')  # Format: "Jan 2025"
            backend_monthly_exp_repay['date'] = backend_monthly_exp_repay['date'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            sales_trend = new_filtered_data.groupby(pd.Grouper(key='date_disbursed', freq='M')).agg({
                'amount': 'sum',
                'interest_amount': 'sum',
                'num_of_month': 'sum',
                'repayment': 'sum',
                'expected_repayment': 'sum',
                'due_today_amount': 'sum',
                'open_portfolio': 'sum',
                'actual_loan_disbursed': 'sum'
            }).round(2).reset_index()

            sales_trend['date_disbursed'] = sales_trend['date_disbursed'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            active_trend = active_portfolio.groupby(pd.Grouper(key='date_disbursed', freq='M')).agg({
                'amount': 'sum',
                'interest_amount': 'sum',
                'repayment': 'sum',
                'expected_repayment': 'sum',
                'due_today_amount': 'sum',
                'open_portfolio': 'sum'
            }).round(2).reset_index()

            active_trend['date_disbursed'] = active_trend['date_disbursed'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            # Repayments (Grouping Repaymenrts by months)
            repayment_trend = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='M'))[['repayment_by_date']].sum().reset_index()
            repayment_trend['paid_date'] = repayment_trend['paid_date'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            all_trend = sales_trend.merge(repayment_trend, left_on = 'date_disbursed', right_on = 'paid_date', how='left')

            # Merge data with exp_repay_today
            all_trend = all_trend.merge(monthly_exp_repay, left_on="date_disbursed", right_on='date', how="left")
            all_trend = all_trend.merge(backend_monthly_exp_repay, left_on="date_disbursed", right_on='date', how="left")

            # Cummulative exp-repayment
            all_trend["cumulative_exp_repay_today"] = all_trend["exp_repay_today"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_backend_exp_repay_today"] = all_trend["backend_exp_repay_today"].cumsum()  # Compute cumulative sum

            # Gets monthly repayment for loan
            all_trend['monthly_repayment'] = all_trend['expected_repayment'] / all_trend['num_of_month']

            # Gets interest monthly repayment
            all_trend['int_monthly_repayment'] = all_trend['interest_amount'] / all_trend['num_of_month']

            # Gets principal monthly repayment
            all_trend['prin_monthly_repayment'] = all_trend['amount'] / all_trend['num_of_month']

            # Gets interest element percentage
            all_trend['interest_element_pct'] = (all_trend['int_monthly_repayment'] / all_trend['monthly_repayment']).round(2)

            # Gets principal element percentage
            all_trend['principal_element_pct'] = (all_trend['prin_monthly_repayment'] / all_trend['monthly_repayment']).round(2)

            # Interest element
            all_trend['interest_element'] = (all_trend['interest_element_pct'] * all_trend['repayment_by_date']).round(2)

            # Principal collected
            all_trend['principal_element'] = (all_trend['principal_element_pct'] * all_trend['repayment_by_date']).round(2)

            # Selecting relevant columns and converting date_disbursed to datetime
            df_selected = past_due_date[['date_disbursed', 'open_portfolio', 'Loss_Category']].copy()
            df_selected['date_disbursed'] = pd.to_datetime(df_selected['date_disbursed'], errors='coerce')

            # Dropping rows with NaT values in date_disbursed
            df_selected = df_selected.dropna(subset=['date_disbursed'])

            # Grouping by month and Loss_Category, then summing open_portfolio
            past_due_date_trend_2 = df_selected.groupby(
                [pd.Grouper(key='date_disbursed', freq='M'), 'Loss_Category']
            )[['open_portfolio']].sum().reset_index()

            # Compute cumulative sum for each Loss_Category
            past_due_date_trend_2['cumulative_open_portfolio'] = (
                past_due_date_trend_2.groupby('Loss_Category')['open_portfolio'].cumsum()
            )

            # Formatting date to show 'Month Year'
            past_due_date_trend_2['date_disbursed'] = past_due_date_trend_2['date_disbursed'].dt.strftime('%b %Y')

            # Past maturity by month (cohort)
            ## Loans past due date
            past_due_date_trend = past_due_date.groupby(
                [pd.Grouper(key='date_disbursed', freq='M'), 'Loss_Category']
            )[['open_portfolio']].sum().reset_index()
            past_due_date_trend['date_disbursed'] = past_due_date_trend['date_disbursed'].dt.strftime('%b %Y')
            loans_groups = new_filtered_data.groupby(pd.Grouper(key='date_disbursed', freq='M'))[['amount']].sum().reset_index()
            loans_groups['date_disbursed'] = loans_groups['date_disbursed'].dt.strftime('%b %Y')  # Format: "Jan 2025"
            past_due_date_trend = past_due_date_trend.merge(loans_groups, on = 'date_disbursed')

            # Setup date range for ordering
            start_date = pd.to_datetime("January 2024")
            end_date_ = pd.to_datetime("December 2030")
            date_range = pd.date_range(start=start_date, end=end_date_, freq='MS')  # Month start
            date_order = [date.strftime('%b %Y') for date in date_range]

            # Select relevant columns and convert date
            df_selected = past_due_date[['date_disbursed', 'open_portfolio', 'Loss_Category']].copy()
            df_selected['date_disbursed'] = pd.to_datetime(df_selected['date_disbursed'], errors='coerce')
            df_selected = df_selected.dropna(subset=['date_disbursed'])

            # Group by month and Loss_Category, sum open_portfolio
            past_due_date_trend_2 = df_selected.groupby(
                [pd.Grouper(key='date_disbursed', freq='M'), 'Loss_Category']
            )[['open_portfolio']].sum().reset_index()

            # Compute cumulative sum for each Loss_Category
            past_due_date_trend_2['cumulative_open_portfolio'] = (
                past_due_date_trend_2.groupby('Loss_Category')['open_portfolio'].cumsum()
            )

            # Format date and enforce monthly order
            past_due_date_trend_2['date_disbursed'] = pd.to_datetime(past_due_date_trend_2['date_disbursed'])
            past_due_date_trend_2['month'] = past_due_date_trend_2['date_disbursed'].dt.strftime('%b %Y')
            past_due_date_trend_2['month'] = pd.Categorical(past_due_date_trend_2['month'], categories=date_order, ordered=True)
            past_due_date_trend_2 = past_due_date_trend_2.sort_values('month')

            # Group past maturity by month (cohort)
            past_due_date_trend = past_due_date.groupby(
                [pd.Grouper(key='date_disbursed', freq='M'), 'Loss_Category']
            )[['open_portfolio']].sum().reset_index()

            past_due_date_trend['date_disbursed'] = pd.to_datetime(past_due_date_trend['date_disbursed'])
            past_due_date_trend['month'] = past_due_date_trend['date_disbursed'].dt.strftime('%b %Y')
            past_due_date_trend['month'] = pd.Categorical(past_due_date_trend['month'], categories=date_order, ordered=True)
            past_due_date_trend = past_due_date_trend.sort_values('month')

            # Apply ordering to loans_groups
            loans_groups = new_filtered_data.groupby(
                pd.Grouper(key='date_disbursed', freq='M')
            )[['amount']].sum().reset_index()

            loans_groups['date_disbursed'] = pd.to_datetime(loans_groups['date_disbursed'])
            loans_groups['month'] = loans_groups['date_disbursed'].dt.strftime('%b %Y')
            loans_groups['month'] = pd.Categorical(loans_groups['month'], categories=date_order, ordered=True)
            loans_groups = loans_groups.sort_values('month')

            # Merge on 'month' (not raw date)
            past_due_date_trend = past_due_date_trend.merge(loans_groups[['month', 'amount']], on='month', how='left')

            all_trend["cumulative_open_portfolio"] = all_trend["open_portfolio"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_amount"] = all_trend["amount"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_due_today_amount"] = all_trend["due_today_amount"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_expected_repayment"] = all_trend["expected_repayment"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_repayment"] = all_trend["repayment_by_date"].cumsum()  # Compute cumulative sum
            all_trend['cum_open_portfolio'] = all_trend['cumulative_expected_repayment'] - all_trend['cumulative_repayment'] # cum open portfolio
            all_trend["cumulative_interest"] = all_trend["interest_amount"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_exp_repay"] = all_trend["expected_repayment"].cumsum()  # Compute cumulative sum
            active_trend["cumulative_open_portfolio"] = active_trend["open_portfolio"].cumsum()  # Compute cumulative sum
            active_trend["cumulative_amount"] = active_trend["amount"].cumsum()  # Compute cumulative sum
            active_trend["cumulative_repayment"] = active_trend["repayment"].cumsum()  # Compute cumulative sum
            past_due_date_trend["cumulative_open_portfolio"] = past_due_date_trend["open_portfolio"].cumsum()  # Compute cumulative sum
            past_due_date_trend["cumulative_amount"] = past_due_date_trend["amount"].cumsum()  # Compute cumulative sum

            new_filtered_data['month'] = new_filtered_data['date_disbursed'].dt.strftime('%b %Y')
            grouped_monthly = new_filtered_data.groupby('month').agg(
                    repayment_amount=('repayment', 'sum'),
                    due_today=('due_today_amount', 'sum')).reset_index()

            grouped_by_month = all_trend.merge(grouped_monthly, how='left', left_on= 'date_disbursed', right_on = 'month')

            escrow_df['month'] = escrow_df['date_created'].dt.strftime('%b %Y')
            escrow_bal_by_month = escrow_df.groupby('month').agg(
                    available_bal=('available_balance', 'sum')).reset_index()
            escrow_bal_by_month['month'] = escrow_bal_by_month['month'].str.strip()

            # Ensure the 'Month' column follows the desired order
            escrow_bal_by_month['month'] = pd.to_datetime(escrow_bal_by_month['month'], format='%b %Y')
            escrow_bal_by_month['month'] = pd.Categorical(escrow_bal_by_month['month'].dt.strftime('%b %Y'), categories=date_order, ordered=True)

            # Ensure the 'Month' column follows the desired order
            grouped_by_month['month'] = pd.to_datetime(grouped_by_month['month'], format='%b %Y')
            grouped_by_month['month'] = pd.Categorical(grouped_by_month['month'].dt.strftime('%b %Y'), categories=date_order, ordered=True)

            df1_sorted = grouped_by_month.sort_values("month")
            df2_sorted = escrow_bal_by_month.sort_values("month")

            escrow_merged_df = pd.merge(df1_sorted, df2_sorted, on = 'month')
            escrow_merged_df['plus_escrow'] = escrow_merged_df['repayment_by_date'] + escrow_merged_df['available_bal']

            # Adds cumulative repayment plus escrow
            escrow_merged_df["cumulative_plus_escrow"] = escrow_merged_df["plus_escrow"].cumsum()  # Compute cumulative sum

            # Sees the portfolio at the end of that current month
            open_port_repay = repayment_df.groupby(
                [pd.Grouper(key='paid_date', freq='M'), 'ajo_loan_id']
            )[['repayment_by_date']].sum().reset_index()
            open_port_repay['paid_date'] = open_port_repay['paid_date'].dt.strftime('%b %Y')

            # Gets open portfolio for that specified month alone
            open_port_filtered_df = new_filtered_data.groupby([pd.Grouper(key='date_disbursed', freq='M'), 'id'])[['amount', 'expected_repayment']].sum().reset_index()
            open_port_filtered_df['date_disbursed'] = open_port_filtered_df['date_disbursed'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            # Merges both loans and repayment to see open portfolio for that poarticular month alonre
            monthly_open_portfolio = open_port_filtered_df.merge(open_port_repay, left_on=['date_disbursed', 'id'], right_on=['paid_date', 'ajo_loan_id'], how='left')

            # Groups by date_disbursed and get open portfolio for that particular month alone
            grp_mth_open_port = monthly_open_portfolio.groupby('date_disbursed')[['amount', 'repayment_by_date','expected_repayment']].sum().reset_index()

            # Calculate open portfolio
            grp_mth_open_port['open_portfolio'] = grp_mth_open_port['expected_repayment'] - grp_mth_open_port['repayment_by_date']

            # Generate a list of all months from Feb 2024 to Dec 2030 (or further as needed)
            start_date = pd.to_datetime("Feb 2024")
            end_date_ = pd.to_datetime("Dec 2030")
            date_range = pd.date_range(start=start_date, end=end_date_, freq='MS')  # 'MS' = month start frequency
            month_year_order = [date.strftime('%b %Y') for date in date_range]

            # Ensure the 'Month' column follows the desired order
            grp_mth_open_port['date_disbursed'] = pd.to_datetime(grp_mth_open_port['date_disbursed'], format='%b %Y')
            grp_mth_open_port['date_disbursed'] = pd.Categorical(grp_mth_open_port['date_disbursed'].dt.strftime('%b %Y'), categories=month_year_order, ordered=True)

            # Sort the DataFrame by the ordered 'Month' column
            grp_mth_open_port = grp_mth_open_port.sort_values('date_disbursed').reset_index(drop=True)

            grp_mth_open_port['cumulative_open_portfolio'] = grp_mth_open_port['open_portfolio'].cumsum()

            # Create two columns for the layout
            col9, col10 = st.columns(2)

            with col9:
                with st.container():
                    # Loans Trends
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y="amount",
                        title="Disbursement vs Outstanding Portfolio (Cohort)",
                    )

                    fig9.update_traces(mode="lines+markers")

                    # Add a separate trace for 'open_portfolio' (not affected by repayment of other months)
                    fig9.add_trace(
                        px.line(
                            grp_mth_open_port,
                            x="date_disbursed",
                            y="open_portfolio"
                        ).data[0]  # Extract the trace from the new figure
                    )

                    # Update the separate 'open_portfolio' trace to make it visually distinct
                    fig9.data[-1].name = "Total open_portfolio"  # Rename in legend
                    fig9.data[-1].line.color = "red"

                    fig9.update_traces(mode="lines+markers")

                    fig9.update_layout(
                        xaxis_title="Date",
                        yaxis_title="Disbursement / Oustanding Portfolio",
                        legend_title="open_portfolio",
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This plot illustrates the relationship between loan disbursements and the outstanding portfolio month on month.<br>
                        - Disbursement: Represents the amount of loans issued at end end of each month.<br>
                        - Outstanding Portfolio: Indicates the monthly total outstanding loan (money not collected yet from loans disbursed that month).
                        </p>
                        """, unsafe_allow_html=True)

            with col10:
                with st.container():
                    # Loans Trends
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["cumulative_amount", "cum_open_portfolio"],
                        title="Disbursement vs Open Portfolio (Cumulative)",
                    )

                    fig9.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig9.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This plot presents the cumulative trend of loan disbursements and the open portfolio over time.<br>
                        - Cumulative Amount: The total amount of loans disbursed up to date.<br>
                        - Cumulative Open Portfolio: The total outstanding loan balance up to date.<br>
                        This helps visualize long-term loan growth and portfolio changes and possible drift from loaned amount as we keep disbursing.
                        </p>
                        """, unsafe_allow_html=True)

            col3, col4 = st.columns(2)

            with col3:
                # Expected Repayment Trends
                with st.container():
                    fig5 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["repayment", "expected_repayment", "due_today_amount"],
                        title="Repayment Trends (Cohort)",
                        labels={"expected_repayment": "Expected Repayment"},
                    )
                    fig5.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig5.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig5)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This cohort plot displays the repayment trends for loans disbursed that month alone <br>
                        It displays the loan repayment amount 'at as the end_date' vs the expected collection 'as at today' vs total amount paid as of today <br>
                        - <b>Total Paid:</b> Amount repaid for each loan disbursed that month over time <br>
                        - <b>Expected Repayment:</b> The expected amount to be repaid by the end date/maturity date <br>
                        - <b>Due Today:</b> The amount due for repayment today for the loans disbursed that month alone <br>
                        This plot show a straight line for previous months that have all/almost all their loans fully paid indicating that those loans expectations are met <br>
                        The comparison helps in tracking repayment progress and identifying any shortfalls.
                        </p>
                        """, unsafe_allow_html=True)

            with col4:
                # Principal and interest element Trends
                with st.container():
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["principal_element","interest_element"],
                        title="Repayment (Principal vs Interest Element) Trends",
                    )

                    fig9.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig9.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This plot highlights the repayment breakdown between principal and interest elements:<br>
                        - <b>Principal Element:</b> The portion of repayment allocated to amount disbursed.<br>
                        - <b>Interest Element:</b> The portion of repayment allocated to interest on those loans.
                        </p>
                        """, unsafe_allow_html=True)

            # Create two columns for the layout
            col7, col8 = st.columns(2)

            with col7:
                with st.container():
                    fig6 = px.line(
                        escrow_merged_df,
                        x="month",
                        y=["repayment_by_date", "plus_escrow", "exp_repay_today", "backend_exp_repay_today"],
                        title="Repayment plus escrow",
                        labels={"returning_users": "Returning_borrowers"},
                    )

                    fig6.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig6.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig6)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This plot tracks repayment trends alongside escrow of loans repayment<br>
                        - <b>Repayment:</b> Amount paid each month.<br>
                        - <b>Plus Escrow:</b> Amount paid each month plus their respective escrow amount.<br>
                        - <b>exp_repay_today:</b> The amount we should collect each month.
                        </p>
                        """, unsafe_allow_html=True)

            with col8:
                with st.container():
                    # Loans Trends
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["amount", "actual_loan_disbursed"],
                        title="Loans (Disbursed vs Actual Disbursed) Relationship",
                    )

                    fig9.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig9.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart compares the total loan amounts vs the actual disbursed amounts over time<br>
                        - Amount (Disbursement): Represents the total loan amount approved for disbursement.<br>
                        - Amount Disbursed (Actual Disbursement From Seeds and Pennis): The actual funds released to borrowers, which differs due to loan_type<br><br>
                        </p>
                        """, unsafe_allow_html=True)

            # Create two columns for the layout
            col9, col10 = st.columns(2)

            with col9:
                with st.container():
                    # Loans Trends
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["amount", "repayment_by_date", "expected_repayment"],
                        title="Loans Trends (Cohort)",
                    )

                    # Update layout to reduce background opacity
                    fig9.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    # Customizing hover text
                    fig9.update_traces(
                        mode="lines+markers",
                        hovertemplate="<b>Date:</b> %{x}<br>" +
                                    "<b>Amount:</b> %{y:,.2f}<br>" +
                                    "<b>Repayment by Date:</b> %{customdata[0]:,.2f}<br>" +
                                    "<b>Expected Repayment:</b> %{customdata[1]:,.2f}<br>" +
                                    "<b>Interest Rate:</b> %{customdata[2]:.2%}<br>" +
                                    "<b>Completion Rate:</b> %{customdata[3]:.2%}<extra></extra>",
                        customdata=np.column_stack([
                            all_trend["repayment_by_date"],
                            all_trend["expected_repayment"],
                            (all_trend["expected_repayment"] / all_trend["amount"]) - 1.0,
                            all_trend["repayment_by_date"] / all_trend["expected_repayment"]
                        ])
                    )

                    # Add a separate trace for 'open_portfolio' (not affected by repayment of other months)
                    fig9.add_trace(
                        px.line(
                            grp_mth_open_port,
                            x="date_disbursed",
                            y="open_portfolio"
                        ).data[0]  # Extract the trace from the new figure
                    )

                    # Update the separate 'open_portfolio' trace to make it visually distinct
                    fig9.data[-1].name = "Total open_portfolio"  # Rename in legend
                    fig9.data[-1].line.color = "red"

                    fig9.update_traces(mode="lines+markers")

                    fig9.update_layout(
                        xaxis_title="Date",
                        yaxis_title="Disbursement / Open Portfolio",
                        legend_title="open_portfolio category",
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart tracks loan disbursement trends along with repayment progress:<br>
                        - <b>Amount:</b> The total loans disbursed for each month.<br>
                        - <b>Repayment:</b> Actual repayments made that month.<br>
                        - <b>Expected Repayment:</b> The scheduled repayments for issued loans.<br>
                        - <b>Open Portfolio:</b> The amount of loans still outstanding.<br>
                        </p>
                        """, unsafe_allow_html=True)

            with col10:
                with st.container():
                    # Loans Trends
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["cumulative_amount", "cumulative_repayment", "cumulative_exp_repay_today", "cumulative_backend_exp_repay_today", "cum_open_portfolio"],
                        title="Loans Trends (Cummulative)",
                    )

                    fig9.update_traces(mode="lines+markers")

                    # Add a separate trace for 'cumulative_plus_escrow' with a different dataset
                    fig9.add_trace(
                        px.line(
                            escrow_merged_df,
                            x="month",
                            y="cumulative_plus_escrow"
                        ).data[0]  # Extract the trace from the new figure
                    )

                    # Update the separate 'plus_escrow' trace to make it visually distinct between repayment made each month plus the escrow amount
                    fig9.data[-1].name = "Repayment plus escrow"  # Rename in legend
                    fig9.data[-1].line.color = "yellow"
                    fig9.data[-1].line.dash = "dot"  # Make it a dotted line to differentiate

                    fig9.update_traces(mode="lines+markers")

                    fig9.update_layout(
                        xaxis_title="Date",
                        yaxis_title="escrow",
                        legend_title="Plus Escrow",
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    # Customizing hover text
                    fig9.update_traces(
                        mode="lines+markers",
                        hovertemplate="<b>Date:</b> %{x}<br>" +
                                    "<b>Cumulative Amount:</b> %{customdata[0]:,.2f}<br>" +
                                    "<b>Cumulative Repayment:</b> %{customdata[1]:,.2f}<br>" +
                                    "<b>Cumulative Due Today:</b> %{customdata[2]:,.2f}<br>" +
                                    "<b>Cumulative Expected Due Today:</b> %{customdata[3]:,.2f}<br>" +
                                    "<b>Cumulative Open Portfolio:</b> %{customdata[4]:,.2f}<br>" +
                                    "<b>Cumulative Plus Escrow:</b> %{customdata[5]:,.2f}<br>" +
                                    "<b>Repayment/Due Rate:</b> %{customdata[6]:,.2%}<br>" +
                                    "<b>Plus_Escrow/Due Rate:</b> %{customdata[7]:.2%}<extra></extra>" +
                                    "<b>Repayment/Exp. Due Rate:</b> %{customdata[8]:,.2%}<br>" +
                                    "<b>Plus_Escrow/Exp.Due Rate:</b> %{customdata[9]:.2%}<extra></extra>",
                        customdata=np.column_stack([
                            all_trend["cumulative_amount"],
                            all_trend["cumulative_repayment"],
                            all_trend["cumulative_exp_repay_today"],
                            all_trend["cumulative_backend_exp_repay_today"],
                            all_trend["cum_open_portfolio"],
                            escrow_merged_df["cumulative_plus_escrow"],
                            all_trend["cumulative_repayment"] / all_trend["cumulative_exp_repay_today"],
                            escrow_merged_df["cumulative_plus_escrow"] / all_trend["cumulative_exp_repay_today"],
                            all_trend["cumulative_repayment"] / all_trend["cumulative_backend_exp_repay_today"],
                            escrow_merged_df["cumulative_plus_escrow"] / all_trend["cumulative_backend_exp_repay_today"]
                        ])
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart tracks cumulative loan trends, including:<br>
                        - <b>Cumulative Amount:</b> Total loans disbursed over time.<br>
                        - <b>Cumulative Repayment:</b> Total repayments made.<br>
                        - <b>Cumulative Due Today:</b> Amount to be collected.<br>
                        - <b>Cumulative Expected Repayment:</b> Scheduled repayment amounts as at end_date.<br>
                        - <b>Open Portfolio:</b> Total amount still outstanding.<br>
                        - <b>Repayment plus Escrow:</b> The combination of repayments and escrow, shown with a <span style='color: yellow;'>dotted yellow line</span>.<br>
                        The chart also shows the rate of repayment to due_today (cumulative) and then it also shows the rate of due_today to repayment plus escrow
                        </p>
                        """, unsafe_allow_html=True)

            # Create two columns for the layout
            col11, col12 = st.columns(2)

            with col11:
                with st.container():
                    fig10 = px.line(
                        past_due_date_trend,
                        x="date_disbursed",
                        y="open_portfolio",
                        color="Loss_Category",  # Only open_portfolio is affected by hue
                        title="Past Maturity Trends (Cohort)",
                        markers=True
                    )

                    # Update layout to reduce background opacity
                    fig10.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig10)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart tracks past maturity trends by showing:<br>
                        - <b>Open Portfolio:</b> Active loans that remain unpaid after the due date.<br>
                        - <b>PAR (Portfolio At Risk) Category:</b> Loans grouped by risk levels (days after end date).<br>
                        <br>
                        Each line represents a different risk category, showing the open portfolio trend over time.
                        </p>
                        """, unsafe_allow_html=True)

            with col12:
                with st.container():
                    # Open Port folio
                    fig10 = px.line(
                        past_due_date_trend_2,
                        x="date_disbursed",
                        y="cumulative_open_portfolio",
                        color="Loss_Category",  # Only open_portfolio is affected by hue
                        title="Past Maturity Trends (Cumulative)",
                        markers=True
                    )

                    # Update layout to reduce background opacity
                    fig10.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig10)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart visualizes the cumulative open portfolio of loans past their maturity date, categorized by risk levels:<br>
                        - <b>Cumulative Open Portfolio:</b> The cumulative outstanding loan balance that has exceeded its due date.<br>
                        - <b>PAR (Portfolio At Risk) Category:</b> Loans grouped based on risk classification.<br>
                        </p>
                        """, unsafe_allow_html=True)

            c1,c2 = st.columns(2)

            with c1:
                # Savings
                sav_transaction_df['savings_month'] = pd.to_datetime(sav_transaction_df['date_created']).dt.strftime('%b %Y')
                ajo_grouped_by_month = sav_transaction_df.groupby(['savings_month']).agg(
                                amount_saved=('amount', 'sum')).reset_index()

                # Loan savings
                loan_saving_df['savings_month'] = pd.to_datetime(loan_saving_df['ajosavings_created_at']).dt.strftime('%b %Y')
                loan_savings_by_month = loan_saving_df.groupby('savings_month').agg(
                        loan_amount_saved=('ajo_savings_plus_escrow', 'sum')).reset_index()

                # Merge saving_transaction and loan_saving
                merged_df = pd.merge(ajo_grouped_by_month, loan_savings_by_month, on='savings_month')

                # Generate a list of all months from Jan 2024 to Dec 2030 (or further as needed)
                start_date = pd.to_datetime("January 2024")
                end_date_ = pd.to_datetime("December 2030")
                date_range = pd.date_range(start=start_date, end=end_date_, freq='MS')  # 'MS' = month start frequency
                date_order = [date.strftime('%b %Y') for date in date_range]

                # Ensure the 'Month' column follows the desired order
                merged_df['savings_month'] = pd.to_datetime(merged_df['savings_month'], format='%b %Y')
                merged_df['savings_month'] = pd.Categorical(merged_df['savings_month'].dt.strftime('%b %Y'), categories=date_order, ordered=True)

                # Sort the DataFrame by the ordered 'Month' column
                merged_df = merged_df.sort_values('savings_month').reset_index(drop=True)

                # merge to all trend
                merged_df = all_trend.merge(merged_df, how='left', right_on = 'savings_month', left_on = 'date_disbursed')

                with st.container():
                    fig6 = px.line(
                        merged_df,
                        x="savings_month",
                        y=["amount_saved", "loan_amount_saved"],
                        title="Saving Trend",
                    )
                    fig6.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig6.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig6)

                    # Expandable description
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart tracks monthly savings trends, including:<br>
                        - <b>Amount Saved:</b> Direct savings contributions.<br>
                        - <b>Loan Savings:</b> Loan-linked savings, including escrow amounts.<br>
                        </p>
                        """, unsafe_allow_html=True)

            with c2:
                # Generate a list of all months from Feb 2024 to Dec 2030 (or further as needed)
                start_date = pd.to_datetime("Feb 2024")
                end_date_ = pd.to_datetime("Dec 2030")
                date_range = pd.date_range(start=start_date, end=end_date_, freq='MS')  # 'MS' = month start frequency
                month_year_order = [date.strftime('%b %Y') for date in date_range]

                # Ensure the 'Month' column follows the desired order
                users_retake_df['Month_and_Year'] = pd.to_datetime(users_retake_df['Month_and_Year'], format='%b %Y')
                users_retake_df['Month_and_Year'] = pd.Categorical(users_retake_df['Month_and_Year'].dt.strftime('%b %Y'), categories=month_year_order, ordered=True)

                # Sort the DataFrame by the ordered 'Month' column
                users_retake_df = users_retake_df.sort_values('Month_and_Year').reset_index(drop=True)

                # merge to all trend
                users_retake_df = all_trend.merge(users_retake_df, how='left', right_on = 'Month_and_Year', left_on = 'date_disbursed')

                with st.container():
                    fig6 = px.line(
                        users_retake_df,
                        x="Month_and_Year",
                        y="total_repeat_users",
                        title="Returning borrowers",
                        labels={"total_repeat_users": "total_repeat_users"},
                    )
                    fig6.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig6.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )
                    st.plotly_chart(fig6)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart displays the trend of returning borrowers over time, helping to analyze repeat loan activity.<br>
                        - Total Repeat Users: Users who took loans more than once (including their first occurrence).
                        </p>
                        """, unsafe_allow_html=True)

            # Write loan report to sidebar as a button
            with file3:
                if grp_mth_open_port.empty:
                    st.warning("No data found for the selected filters. Please adjust your selections.")
                else:
                    # Format: "15_Jan_2025"
                    today_str = datetime.now().strftime("%d_%b_%Y")
                    file_name = f"Cohort_summary_till_{today_str}.csv"

                    csv = grp_mth_open_port.to_csv(index=False).encode("utf-8")
                    st.download_button(
                        label="⬇️ Download",
                        data=csv,
                        file_name=file_name,
                        mime="text/csv"
                    )

            # Write monthly report to sidebar as a button
            with file4:
                if all_trend.empty:
                    st.warning("No data found for the selected filters. Please adjust your selections.")
                else:
                    # Format: "15_Jan_2025"
                    today_str = datetime.now().strftime("%d_%b_%Y")
                    file_name = f"Monthly_summary_till_{today_str}.csv"

                    csv = all_trend.to_csv(index=False).encode("utf-8")
                    st.download_button(
                        label="⬇️ Download",
                        data=csv,
                        file_name=file_name,
                        mime="text/csv"
                    )

            pass
        except (ValueError, IndexError, KeyError, AttributeError):
            st.warning("Not enough dataset to load this, Kindly skip.")

    ################################################################################### Tab 4 #################################################################################

    with tab4:

    ################################################################################### Copy Datasets #################################################################################

        # Copy fiiltered_data as dataframe
        loan_disk_df = new_filtered_data.copy()

        # Copy filtered_repayment_df as dataframe
        loan_disk_repay_df = filtered_repayment_df.copy()

        # Copy past_due_date as dataframe
        loan_disk_past_mat_df = past_due_date.copy()

        # Copy fiiltered_data as dataframe
        loan_disk_missed_repay_df = new_filtered_data.copy()
        # Filters only missed repayment
        loan_disk_missed_repay_df = loan_disk_missed_repay_df[loan_disk_missed_repay_df['missed_days'] > 0]

        # Completed_loan_disk as dataframe
        completed_loan_disk = new_filtered_data.copy()
        # Filters only completed loans
        completed_loan_disk = completed_loan_disk[completed_loan_disk['status'] == 'COMPLETED']

        # Select Supervisor from unique list
        selected_supervisor = st.selectbox("Select Supervisor", pd.Series(loan_disk_df['sup_name'].unique()).sort_values(), placeholder="Select or Enter Supervisor To Begin...", index=None)

        if selected_supervisor:
            # Filter loans based on selected supervisor
            filtered_loan_disk_loans = loan_disk_df[loan_disk_df['sup_name'] == selected_supervisor]

            # Get unique agents under the selected supervisor
            unique_agents = pd.Series(filtered_loan_disk_loans['agent_full_name'].unique()).sort_values()

            # Select Agent (Conditional: Only appears when a supervisor is selected)
            selected_agent = st.selectbox("Select Agent", ["All Agents"] + list(unique_agents), index=0)

            # Further filter by agent (if an agent is selected and not 'All Agents')
            if selected_agent != "All Agents":
                filtered_loan_disk_loans = filtered_loan_disk_loans[filtered_loan_disk_loans['agent_full_name'] == selected_agent]

            # Get loan IDs after filtering by supervisor (and agent if selected)
            loan_ids = filtered_loan_disk_loans['id'].unique()

            # Filter other datasets using loan IDs
            filtered_loan_disk_repayment_df = loan_disk_repay_df[loan_disk_repay_df['ajo_loan_id'].isin(loan_ids)]
            filtered_loan_disk_past_mat_df = loan_disk_past_mat_df[loan_disk_past_mat_df['id'].isin(loan_ids)]
            filtered_loan_disk_missed_repay_df = loan_disk_missed_repay_df[loan_disk_missed_repay_df['id'].isin(loan_ids)]
            filtered_loan_disk_completed_df = completed_loan_disk[completed_loan_disk['id'].isin(loan_ids)]
            filtered_exp_till_today_ = exp_till_today_[exp_till_today_['id'].isin(loan_ids)]
            filtered_exp_till_yesterday_ = exp_till_yesterday_[exp_till_yesterday_['id'].isin(loan_ids)]
            filtered_exp_till_ThisWeek_ = exp_till_ThisWeek_[exp_till_ThisWeek_['id'].isin(loan_ids)]
            filtered_exp_till_ThisMonth_ = exp_till_ThisMonth_[exp_till_ThisMonth_['id'].isin(loan_ids)]
            filtered_exp_till_ThisYear_ = exp_till_ThisYear_[exp_till_ThisYear_['id'].isin(loan_ids)]
            filtered_exp_all_time = exp_all_time[exp_all_time['id'].isin(loan_ids)]

            st.markdown("---")

    ######################################################################### Loan summarised metrics ####################################################################################

            # Further filter by agent (if an agent is selected and not 'All Agents')
            if selected_agent != "All Agents":
                # Custom CSS for bordered metrics
                st.markdown(
                    """
                    <style>
                    ._metric-container {
                        border: 4px solid #4b9ca5; /* Sea green border */
                        border-radius: 0px;
                        padding: 10px;
                        text-align: center;
                        margin: 15px;
                        width: 100%;
                        box-shadow: 3px 3px 5px rgba(0, 151, 167, 0.5); /* shadow */
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                    }
                    ._metric-value {
                        font-size: 20px;
                        font-weight: bold;
                        white-space: normal;
                        max-width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    ._metric-title {
                        font-size: 30px;
                        color: #777;
                        font-weight: bold;
                        margin-bottom: 10px;
                        white-space: normal;
                        text-align: center;
                    }
                    ._metric-label {
                        font-size: 17px;
                        color: #555;
                        font-weight: bold;
                        white-space: normal;
                        text-align: center;
                    }
                    ._metric-name {
                        font-size: 17px;
                        font-weight: lighter;
                        white-space: normal;
                        text-align: center;
                    }
                    </style>
                    """, unsafe_allow_html=True
                )

                # Custom metric display function for entire category
                def bordered_category_(metrics):
                    st.markdown(
                        """
                        <div style='text-align:center; font-size:15px;'>
                            <h1 style='text-shadow: 3px 3px 6px rgba(32, 178, 170, 0.8);'>
                                <strong>Seeds Metrics</strong>
                            </h1>
                        </div>
                        """,
                        unsafe_allow_html=True
                    )
                    for label1, name1, value1, label2, name2, value2, label3, name3, value3, label4, name4, value4 in metrics:
                        st.markdown(
                            f"""
                            <div style="display: flex; justify-content: space-between; width: 100%;">
                                <div style="text-align: center; flex: 1; padding: 10px;">
                                <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                    <div class="_metric-label">{label1}</div>
                                    <div class="_metric-name">{name1}</div>
                                    <div class="_metric-value">{value1}</div>
                                <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                                </div>
                                <div style="text-align: center; flex: 1; padding: 10px;">
                                <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                <div class="_metric-label">{label2}</div>
                                    <div class="_metric-name">{name2}</div>
                                    <div class="_metric-value">{value2}</div>
                                <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                                </div>
                                <div style="text-align: center; flex: 1; padding: 10px;">
                                <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                    <div class="_metric-label">{label3}</div>
                                    <div class="_metric-name">{name3}</div>
                                    <div class="_metric-value">{value3}</div>
                                <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                                </div>
                                <div style="text-align: center; flex: 1; padding: 10px;">
                                <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                    <div class="_metric-label">{label4}</div>
                                    <div class="_metric-name">{name4}</div>
                                    <div class="_metric-value">{value4}</div>
                                <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                                </div>
                            </div>
                            """, unsafe_allow_html=True
                        )
                    st.markdown("</div>", unsafe_allow_html=True)  # Close container

                # Filter datasets for loans by paid_date
                LD_repay_df_today = filtered_loan_disk_repayment_df[filtered_loan_disk_repayment_df['paid_date'] == today]
                LD_repay_df_yesterday = filtered_loan_disk_repayment_df[filtered_loan_disk_repayment_df['paid_date'] == yesterday]
                LD_repay_df_month = filtered_loan_disk_repayment_df[filtered_loan_disk_repayment_df['paid_date'] >= start_of_month]

                # Aggregate data for repayment
                LD_repay_today_total = LD_repay_df_today['repayment_by_date'].sum()
                LD_repay_yesterday_total = LD_repay_df_yesterday['repayment_by_date'].sum()
                LD_repay_month_total = LD_repay_df_month['repayment_by_date'].sum()
                LD_repay_all_time_total = filtered_loan_disk_repayment_df['repayment_by_date'].sum()

                # Filter datasets for loans by date_disbursed
                LD_df_today = filtered_loan_disk_loans[filtered_loan_disk_loans['date_disbursed'] == today]
                LD_df_yesterday = filtered_loan_disk_loans[filtered_loan_disk_loans['date_disbursed'] == yesterday]
                LD_df_month = filtered_loan_disk_loans[filtered_loan_disk_loans['date_disbursed'] >= start_of_month]

                # Aggregate data for disbursement
                LD_disbursed_today_total = LD_df_today['amount'].sum()
                LD_disbursed_yesterday_total = LD_df_yesterday['amount'].sum()
                LD_disbursed_month_total = LD_df_month['amount'].sum()
                LD_disbursed_all_time_total = filtered_loan_disk_loans['amount'].sum()

                LD_active_users = filtered_loan_disk_loans[filtered_loan_disk_loans['status']=='OPEN'] # Filter data for active users
                LD_active_users = LD_active_users['id'].count() # Gets count of active users
                LD_outstanding_loans = filtered_loan_disk_loans['open_portfolio'].sum() # Aggregate data for outstanding loans
                LD_past_mat_all_time_total = filtered_loan_disk_past_mat_df['open_portfolio'].sum() # Aggregate data for past maturity
                LD_missed_repay_all_time_total = filtered_loan_disk_missed_repay_df['missed_repayment'].sum() # Aggregate data for missed repayment
                filtered_exp_till_today_ = filtered_exp_till_today_['exp_repay_today'].sum()
                filtered_exp_till_yesterday_ = filtered_exp_till_yesterday_['exp_repay_today'].sum()
                filtered_exp_till_ThisWeek_ = filtered_exp_till_ThisWeek_['exp_repay_today'].sum()
                filtered_exp_till_ThisMonth_ = filtered_exp_till_ThisMonth_['exp_repay_today'].sum()
                filtered_exp_till_ThisYear_ = filtered_exp_till_ThisYear_['exp_repay_today'].sum()
                filtered_exp_all_time = filtered_exp_all_time['exp_repay_today'].sum()

                bordered_category_([
                    ("Today", "Loan Disbursed", f"N{LD_disbursed_today_total:,.2f}", "Yesterday", "Loan Disbursed", f"N{LD_disbursed_yesterday_total:,.2f}", "This Month", "Loan Disbursed", f"N{LD_disbursed_month_total:,.2f}", "All-Time", "Loan Disbursed", f"N{LD_disbursed_all_time_total:,.2f}"),
                    ("Today", "Loan Paid", f"N{LD_repay_today_total:,.2f}", "Yesterday", "Loan Paid", f"N{LD_repay_yesterday_total:,.2f}", "This Month", "Loan Paid", f"N{LD_repay_month_total:,.2f}", "All-Time", "Loan Paid", f"N{LD_repay_all_time_total:,.2f}"),
                    ("Today", "Due Collections", f"N{filtered_exp_till_today_:,.2f}", "Yesterday", "Due Collections", f"N{filtered_exp_till_yesterday_:,.2f}", "This Month", "Due Collections", f"N{filtered_exp_till_ThisMonth_:,.2f}", "All-Time", "Due Collections", f"N{filtered_exp_all_time:,.2f}"),
                    ("All-Time", "Total Active Borrowers", f"{LD_active_users:,.0f} users", "All-time", "Missed Repayment", f"N{LD_missed_repay_all_time_total:,.2f}", "All-time", "Outstanding Loan", f"N{LD_outstanding_loans:,.2f}", "All-Time", "Loans Past Maturity", f"N{LD_past_mat_all_time_total:,.2f}"),
                ])

    ######################################################################### Gets dataframe by Date filters ####################################################################################

            else:
                # Filter loans for today alone
                loans_disk_df_today = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] == today]

                # Filter loans for yesterday alone
                loans_disk_df_yesterday = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] == yesterday]

                # Filter loans from this week alone
                loans_disk_df_this_week = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] >= start_of_week]

                # Filter loans from this month alone
                loans_disk_df_this_month = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] >= start_of_month]

                # Filter loans from this year alone
                loans_disk_df_this_year = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] >= start_of_year]

                # Filter loans all time
                loans_disk_df_all_time = filtered_loan_disk_loans.copy()

    ######################################################################### Agents Disbursement ####################################################################################

                @st.cache_data(show_spinner=False)
                def create_disbursement_chart(df, title):
                    # Group by 'agent_full_name' to calculate total amount per agent
                    agent_summary = df.groupby('agent_full_name', as_index=False).agg(
                        total_amount=('amount', 'sum')  # Summing the amount per agent
                    )

                    # Format amount with commas
                    agent_summary['total_amount'] = agent_summary['total_amount'].round(2)
                    agent_summary['formatted_total_amount'] = agent_summary['total_amount'].apply(lambda x: f"{x:,.0f}")

                    # Sort the main dataset by total_amount
                    df = agent_summary.sort_values(by='total_amount', ascending=False)

                    # Create a bar chart with amount and total amount
                    fig = px.bar(
                        df,
                        x='total_amount',
                        y='agent_full_name',
                        orientation='h',
                        title=title,
                        color='total_amount',
                        color_continuous_scale='blues',
                        hover_data={'total_amount': False, 'formatted_total_amount': True}  # Show formatted value
                    )

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    return fig

    ######################################################################### Gets repayment dataframe by Date filters ####################################################################################

                # Drop 'agent_id' to mergfe successfully and avoid a clash with 'agent_id' in repayment table
                loan_disk_repay_merge = filtered_loan_disk_loans.drop(columns='agent_id')

                # merge repayments and loans by months
                merged_loan_disk_with_repay = loan_disk_repay_merge.merge(filtered_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

                # Filter loans for today alone
                loans_disk_repayment_df_today = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] == today]

                # Filter loans for yesterday alone
                loans_disk_repayment_df_yesterday = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] == yesterday]

                # Filter loans from this week alone
                loans_disk_repayment_df_this_week = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] >= start_of_week]

                # Filter loans from this month alone
                loans_disk_repayment_df_this_month = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] >= start_of_month]

                # Filter loans from this year alone
                loans_disk_repayment_df_this_year = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] >= start_of_year]

                # Filter loans from this year alone
                loans_disk_repayment_df_all_time = merged_loan_disk_with_repay.copy()

    ######################################################################### Agents Repayment ####################################################################################

                @st.cache_data(show_spinner=False)
                def create_repayment_chart(df, exp_repay_df, title):
                    # Merge actual repayments with expected repayments
                    df = df.groupby(['agent_id', 'agent_full_name'])['repayment_by_date'].sum().reset_index()
                    exp_repay_df = exp_repay_df.groupby(['agent_id'])['exp_repay_today'].sum().reset_index()
                    df = df.merge(exp_repay_df, on='agent_id', how='left')

                    # Summarize by agent
                    df_summary = df.groupby('agent_full_name', as_index=False).agg(
                        total_repayment=('repayment_by_date', 'sum'),
                        total_expected=('exp_repay_today', 'sum')
                    )

                    # Compute repayment rate, treating zero expected as full completion
                    df_summary['repayment_rate'] = np.where(
                        df_summary['total_expected'] == 0,
                        1.0,
                        df_summary['total_repayment'] / df_summary['total_expected']
                    )

                    # Cap at 100%
                    df_summary['repayment_rate'] = df_summary['repayment_rate'].clip(upper=1.0)

                    # Categorize for coloring
                    df_summary['progress_category'] = np.where(
                        df_summary['repayment_rate'] >= 1.0,
                        "Fully Met ✅",
                        "Below Target ❌"
                    )

                    # Format for display
                    df_summary['formatted_repayment'] = df_summary['total_repayment'].map("{:,.2f}".format)
                    df_summary['formatted_expected'] = df_summary['total_expected'].map("{:,.2f}".format)
                    df_summary['display_rate'] = (df_summary['repayment_rate'] * 100).round(0).astype(int).astype(str) + "%"

                    # Sort by rate descending
                    df_summary = df_summary.sort_values('repayment_rate', ascending=False)

                    # Build bar chart
                    fig = px.bar(
                        df_summary,
                        x='repayment_rate',
                        y='agent_full_name',
                        orientation='h',
                        title=title,
                        color='progress_category',
                        color_discrete_map={"Fully Met ✅": "green", "Below Target ❌": "red"},
                        text='display_rate',
                        hover_data={
                            'formatted_repayment': True,
                            'formatted_expected': True,
                            'display_rate': True,
                            'progress_category': False,
                            'repayment_rate': False
                        }
                    )

                    # Tweak hover and layout
                    fig.update_traces(
                        hovertemplate=(
                            "Repayment: %{customdata[0]}<br>"
                            "Expected: %{customdata[1]}<br>"
                            "Rate: %{text}"
                        ),
                        textposition='inside'
                    )
                    fig.update_layout(
                        xaxis=dict(title='Repayment Progress (%)', tickformat=".0%"),
                        yaxis=dict(title='Agent'),
                        paper_bgcolor="rgba(0,0,0,0)",
                        plot_bgcolor="rgba(0,0,0,0)",
                        modebar={"bgcolor": "rgba(0,0,0,0)"}
                    )

                    st.plotly_chart(fig, use_container_width=True)

    ############################################################## Make plots based on progress and disbursements #########################################################################

                loan_id_ = filtered_loan_disk_loans[['id', 'agent_id']]
                filtered_exp_till_today_ = filtered_exp_till_today_.merge(loan_id_, how='left', on='id')
                filtered_exp_till_yesterday_ = filtered_exp_till_yesterday_.merge(loan_id_, how='left', on='id')
                filtered_exp_till_ThisWeek_ = filtered_exp_till_ThisWeek_.merge(loan_id_, how='left', on='id')
                filtered_exp_till_ThisMonth_ = filtered_exp_till_ThisMonth_.merge(loan_id_, how='left', on='id')
                filtered_exp_till_ThisYear_ = filtered_exp_till_ThisYear_.merge(loan_id_, how='left', on='id')
                filtered_exp_all_time = filtered_exp_all_time.merge(loan_id_, how='left', on='id')

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_today, "Disbursement By Agents (Today)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_today, filtered_exp_till_today_, "Collections By Agents (Today)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_yesterday, "Disbursement By Agents (Yesterday)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_yesterday, filtered_exp_till_yesterday_, "Collections By Agents (Yesterday)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_this_week, "Disbursement By Agents (This Week)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_this_week, filtered_exp_till_ThisWeek_, "Collections By Agents (This Week)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_this_month, "Disbursement By Agents (This Month)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_this_month, filtered_exp_till_ThisMonth_, "Collections By Agents (This Month)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_this_year, "Disbursement By Agents (This Year)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_this_year, filtered_exp_till_ThisYear_, "Collections By Agents (This Year)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_all_time, "Disbursement By Agents (All Time)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_all_time, filtered_exp_all_time, "Collections By Agents (All Time)")

            st.markdown("---")

    ############################################################### Dropdown to select between Open Loans and Completed Loans #########################################################################

            ## PAST MATURITY
            summary_past_mat_loan_disk = filtered_loan_disk_past_mat_df.copy()
            ### Copy dataset to get last date in repayment
            last_date_repayment = filtered_loan_disk_repayment_df.copy()
            ### Gets the last date by sorting and grouping
            last_date_repayment = last_date_repayment.sort_values(by=['ajo_loan_id', 'paid_date']).groupby('ajo_loan_id').last().reset_index()
            #### Merge past maturity with repayments
            all_past_due_loan_disk = filtered_loan_disk_past_mat_df.merge(filtered_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')
            all_past_due_loan_disk = all_past_due_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True]).reset_index()

            # Convert to datetime
            summary_past_mat_loan_disk['start_date'] = pd.to_datetime(summary_past_mat_loan_disk['start_date'])  # Convert to datetime
            all_past_due_loan_disk['start_date'] = pd.to_datetime(all_past_due_loan_disk['start_date'])  # Convert to datetime
            # Calculate number of days from start_date till today
            summary_past_mat_loan_disk['age_in_days'] = (datetime.today() - summary_past_mat_loan_disk['start_date']).dt.days
            all_past_due_loan_disk['age_in_days'] = (datetime.today() - all_past_due_loan_disk['start_date']).dt.days
            # # Convert days to months (assuming 30 days per month for approximation)
            # summary_past_mat_loan_disk['age_in_months'] = (summary_past_mat_loan_disk['age_in_days'] / 30).apply(lambda x: round(x))
            # Gets duration
            summary_past_mat_loan_disk['Duration'] = summary_past_mat_loan_disk['start_date'] - datetime.today()
            all_past_due_loan_disk['Duration'] = all_past_due_loan_disk['start_date'] - datetime.today()
            # Compute cumulative sum per 'id'
            all_past_due_loan_disk['cumulative_repayment'] = all_past_due_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_past_mat_loan_disk['principal_balance'] = (summary_past_mat_loan_disk['principal_element'] - summary_past_mat_loan_disk['repayment']).clip(lower=0)
            all_past_due_loan_disk['principal_paid'] = (all_past_due_loan_disk['principal_element_pct'] * all_past_due_loan_disk['cumulative_repayment']).round(2)
            all_past_due_loan_disk['principal_balance'] = (all_past_due_loan_disk['amount'] - all_past_due_loan_disk['principal_paid']).clip(lower=0)
            # Subtract from initial amount in the same row
            all_past_due_loan_disk['Bal_per_paid_date'] = all_past_due_loan_disk['expected_repayment'] - all_past_due_loan_disk['cumulative_repayment']
            # Get few columnsin repayment before merging
            last_date_repayment = last_date_repayment[['ajo_loan_id', 'paid_date', 'repayment_by_date']]
            # merge summary with repayment to get the last date of payment
            summary_past_mat_loan_disk = summary_past_mat_loan_disk.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
            # sort by dates
            summary_past_mat_loan_disk = summary_past_mat_loan_disk.sort_values(by=['past_maturity'], ascending=False).reset_index()
            all_past_due_loan_disk = all_past_due_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()
            # Arranges dataset for summary
            summary_past_mat_loan_disk = summary_past_mat_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'expected_repayment', 'repayment', 'due_today_amount', 'open_portfolio', 'end_date',
            'past_maturity', 'paid_date', 'repayment_by_date', 'age_in_days', 'Duration']]
            # Arranges dataset for all past due
            all_past_due_loan_disk = all_past_due_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio', 'end_date',
            'past_maturity', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date', 'due_today_amount']]
            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "agent_full_name": "Loan Officer",
                "agent_contact": "LO Contact",
                "guarantor_phone_number": "Guarantor Contact",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment_by_date": "Repayment",
                "open_portfolio": "Pending Due",
                "past_maturity": "Days Past",
                "end_date": "Maturity Date",
                "paid_date": "Payment Date",
                "repayment": "Paid",
            }

            summary_past_mat_loan_disk.rename(columns=column_mappings, inplace=True)
            all_past_due_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Missed Repayment Summary ####################################################################################

            ## MISSED REPAYMENT
            summary_completed_loan_disk = filtered_loan_disk_completed_df.copy()
            #### Merge missed repayments
            all_completed_loan_disk = filtered_loan_disk_completed_df.merge(filtered_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

            # Sort by 'missed_days'
            summary_completed_loan_disk = summary_completed_loan_disk.sort_values(by=['date_disbursed', 'users_full_name'], ascending=True)
            all_completed_loan_disk = all_completed_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True])
            # Calculate number of days from start_date till today
            summary_completed_loan_disk['age_in_days'] = (summary_completed_loan_disk['end_date'] - summary_completed_loan_disk['start_date']).dt.days
            all_completed_loan_disk['age_in_days'] = (all_completed_loan_disk['end_date'] - all_completed_loan_disk['start_date']).dt.days
            # Gets duration
            summary_completed_loan_disk['Duration'] = summary_completed_loan_disk['start_date'] - summary_completed_loan_disk['end_date']
            all_completed_loan_disk['Duration'] = all_completed_loan_disk['start_date'] - all_completed_loan_disk['end_date']
            # merge summary with repayment to get the last date of payment
            summary_completed_loan_disk = summary_completed_loan_disk.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
            # Compute cumulative sum per 'id'
            all_completed_loan_disk['cumulative_repayment'] = all_completed_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_completed_loan_disk['principal_balance'] = (summary_completed_loan_disk['principal_element'] - summary_completed_loan_disk['repayment']).clip(lower=0)
            all_completed_loan_disk['principal_paid'] = (all_completed_loan_disk['principal_element_pct'] * all_completed_loan_disk['cumulative_repayment']).round(2)
            all_completed_loan_disk['principal_balance'] = (all_completed_loan_disk['amount'] - all_completed_loan_disk['principal_paid']).clip(lower=0)
            # Subtract from initial amount in the same row
            all_completed_loan_disk['Bal_per_paid_date'] = all_completed_loan_disk['expected_repayment'] - all_completed_loan_disk['cumulative_repayment']
            # sort by dates
            summary_completed_loan_disk = summary_completed_loan_disk.sort_values(by=['date_disbursed', 'users_full_name'], ascending=True).reset_index()
            all_completed_loan_disk = all_completed_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()
            # Gets columns and arragnes
            summary_completed_loan_disk = summary_completed_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'principal_element', 'expected_repayment', 'repayment', 'due_today_amount', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'age_in_days', 'Duration']]
            # Arranges dataset for all past due
            all_completed_loan_disk = all_completed_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date']]
            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "agent_full_name": "Loan Officer",
                "agent_contact": "LO Contact",
                "guarantor_phone_number": "Guarantor Contact",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment": "Paid",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "paid_date": "Last Payment Date",
                "repayment_by_date": "Last Payment"
            }

            summary_completed_loan_disk.rename(columns=column_mappings, inplace=True)
            all_completed_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Missed Repayment Summary ####################################################################################

            ## MISSED REPAYMENT
            summary_missed_repayment_loan_disk = filtered_loan_disk_missed_repay_df.copy()
            #### Merge missed repayments
            all_missed_repay_loan_disk = filtered_loan_disk_missed_repay_df.merge(filtered_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

            # Sort by 'missed_days'
            summary_missed_repayment_loan_disk = summary_missed_repayment_loan_disk.sort_values(by='missed_days', ascending=False)
            all_missed_repay_loan_disk = all_missed_repay_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True])
            # Convert to datetime
            summary_missed_repayment_loan_disk['start_date'] = pd.to_datetime(summary_missed_repayment_loan_disk['start_date'])
            all_missed_repay_loan_disk['start_date'] = pd.to_datetime(all_missed_repay_loan_disk['start_date'])

            # Get current date and ensure it's a Timestamp
            current_date = pd.Timestamp.today().normalize()
            # Calculate Loan Duration
            summary_missed_repayment_loan_disk['age_in_days'] = (summary_missed_repayment_loan_disk['end_date'].clip(upper=current_date) - (summary_missed_repayment_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            all_missed_repay_loan_disk['age_in_days'] = (all_missed_repay_loan_disk['end_date'].clip(upper=current_date) - (all_missed_repay_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            # Gets loan duration in months
            summary_missed_repayment_loan_disk['Duration'] = (summary_missed_repayment_loan_disk['end_date'].clip(upper=current_date) - (summary_missed_repayment_loan_disk['start_date'] - pd.Timedelta(days=4)))
            all_missed_repay_loan_disk['Duration'] = (all_missed_repay_loan_disk['end_date'].clip(upper=current_date) - (all_missed_repay_loan_disk['start_date'] - pd.Timedelta(days=4)))

            # merge summary with repayment to get the last date of payment
            summary_missed_repayment_loan_disk = summary_missed_repayment_loan_disk.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
            # Compute cumulative sum per 'id'
            all_missed_repay_loan_disk['cumulative_repayment'] = all_missed_repay_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_missed_repayment_loan_disk['principal_balance'] = (summary_missed_repayment_loan_disk['principal_element'] - summary_missed_repayment_loan_disk['repayment']).clip(lower=0)
            all_missed_repay_loan_disk['principal_paid'] = (all_missed_repay_loan_disk['principal_element_pct'] * all_missed_repay_loan_disk['cumulative_repayment']).round(2)
            all_missed_repay_loan_disk['principal_balance'] = (all_missed_repay_loan_disk['amount'] - all_missed_repay_loan_disk['principal_paid']).clip(lower=0)
            # Subtract from initial amount in the same row
            all_missed_repay_loan_disk['Bal_per_paid_date'] = all_missed_repay_loan_disk['expected_repayment'] - all_missed_repay_loan_disk['cumulative_repayment']
            # sort by dates
            summary_missed_repayment_loan_disk = summary_missed_repayment_loan_disk.sort_values(by=['missed_days', 'users_full_name'], ascending=[False, True]).reset_index()
            all_missed_repay_loan_disk = all_missed_repay_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()
            # Gets columns and arragnes
            summary_missed_repayment_loan_disk = summary_missed_repayment_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'expected_repayment', 'repayment', 'due_today_amount', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'age_in_days', 'Duration']]
            # Arranges dataset for all past due
            all_missed_repay_loan_disk = all_missed_repay_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date', 'due_today_amount']]
            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "agent_full_name": "Loan Officer",
                "agent_contact": "LO Contact",
                "guarantor_phone_number": "Guarantor Contact",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment": "Paid",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "paid_date": "Last Payment Date",
                "repayment_by_date": "Last Payment"
            }

            summary_missed_repayment_loan_disk.rename(columns=column_mappings, inplace=True)
            all_missed_repay_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Repayment summary ####################################################################################

            # Copy filtered data
            summary_repayment_loan_disk = filtered_loan_disk_repayment_df.copy()
            LoanDisk_df = filtered_loan_disk_loans.copy()

            # Summarize repayments by loan ID
            summary_repayment_loan_disk = summary_repayment_loan_disk.groupby('ajo_loan_id')['repayment_by_date'].sum().reset_index()

            # Merge with loan details
            summary_repayment_loan_disk = summary_repayment_loan_disk.merge(LoanDisk_df, how='left', left_on='ajo_loan_id', right_on='id')
            all_repayment_loan_disk = filtered_loan_disk_repayment_df.merge(LoanDisk_df, how='left', left_on='ajo_loan_id', right_on='id')
            all_repayment_loan_disk = all_repayment_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True])

            # Calculate principal balance
            summary_repayment_loan_disk['principal_balance'] = (summary_repayment_loan_disk['amount'] - summary_repayment_loan_disk['repayment']).clip(lower=0)
            all_repayment_loan_disk['principal_balance'] = (all_repayment_loan_disk['amount'] - all_repayment_loan_disk['repayment']).clip(lower=0)

            # Get current date and ensure it's a Timestamp
            current_date = pd.Timestamp.today().normalize()
            # Calculate Loan Duration
            summary_repayment_loan_disk['age_in_days'] = (summary_repayment_loan_disk['end_date'].clip(upper=current_date) - (summary_repayment_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            all_repayment_loan_disk['age_in_days'] = (all_repayment_loan_disk['end_date'].clip(upper=current_date) - (all_repayment_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            # Gets loan duration in months
            summary_repayment_loan_disk['Duration'] = (summary_repayment_loan_disk['end_date'].clip(upper=current_date) - (summary_repayment_loan_disk['start_date'] - pd.Timedelta(days=4)))
            all_repayment_loan_disk['Duration'] = (all_repayment_loan_disk['end_date'].clip(upper=current_date) - (all_repayment_loan_disk['start_date'] - pd.Timedelta(days=4)))
            # Calculate days left only for loans that haven't reached their end date
            summary_repayment_loan_disk['days_to_maturity'] = (summary_repayment_loan_disk['end_date'] - current_date).dt.days
            all_repayment_loan_disk['days_to_maturity'] = (all_repayment_loan_disk['end_date'] - current_date).dt.days
            # If 'end_date' is already passed, set 'days_to_maturity' to 0
            summary_repayment_loan_disk.loc[summary_repayment_loan_disk['days_to_maturity'] < 0, 'days_to_maturity'] = 0
            all_repayment_loan_disk.loc[all_repayment_loan_disk['days_to_maturity'] < 0, 'days_to_maturity'] = 0
            # Compute cumulative repayment and balance per paid date
            all_repayment_loan_disk['cumulative_repayment'] = all_repayment_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_repayment_loan_disk['principal_balance'] = (summary_repayment_loan_disk['principal_element'] - summary_repayment_loan_disk['repayment']).clip(lower=0)
            all_repayment_loan_disk['principal_paid'] = (all_repayment_loan_disk['principal_element_pct'] * all_repayment_loan_disk['cumulative_repayment']).round(2)
            all_repayment_loan_disk['principal_balance'] = (all_repayment_loan_disk['amount'] - all_repayment_loan_disk['principal_paid']).clip(lower=0)
            all_repayment_loan_disk['Bal_per_paid_date'] = all_repayment_loan_disk['expected_repayment'] - all_repayment_loan_disk['cumulative_repayment']

            # sort by dates
            summary_repayment_loan_disk = summary_repayment_loan_disk.sort_values(by=['date_disbursed', 'users_full_name'], ascending=True).reset_index()
            all_repayment_loan_disk = all_repayment_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()

            # Select and arrange summary columns
            summary_repayment_loan_disk = summary_repayment_loan_disk[[
                'date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name',
                'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'expected_repayment',
                'repayment_by_date', 'open_portfolio', 'missed_days', 'missed_repayment', 'age_in_days', 'Duration', 'days_to_maturity', 'end_date'
            ]]

            # Select and arrange all repayment data columns
            all_repayment_loan_disk = all_repayment_loan_disk[[
                'date_disbursed', 'users_full_name', 'users_phone_number', 'agent_full_name', 'agent_contact',
                'guarantor_phone_number', 'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio',
                'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date', 'age_in_days', 'Duration', 'days_to_maturity', 'end_date'
            ]]

            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "agent_full_name": "Loan Officer",
                "agent_contact": "LO Contact",
                "guarantor_phone_number": "Guarantor Contact",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment_by_date": "Paid Amount",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "paid_date": "Payment Date",
                "repayment": "Paid",
                "end_date": "Maturity Date"
            }

            summary_repayment_loan_disk.rename(columns=column_mappings, inplace=True)
            all_repayment_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Selectbox to get options ####################################################################################

            # Select box for Loan Status
            loan_category = st.selectbox("Loan Status", ["Open", "Completed"], placeholder="Select Loan Status", index=None)

            # If 'Open Loans' is selected, show another dropdown for specific loan types
            if loan_category == "Open":
                open_loan_option = st.selectbox(
                    "Loan Sub-Status",
                    ["Past Maturity", "Missed Repayment", "Loan Repayment"],
                    placeholder="Select Loan Type",
                    index=None
                )

                if open_loan_option:
                    show_full_data = st.toggle("Show Expanded Dataset")

                    if show_full_data:
                        if open_loan_option == "Past Maturity":
                            dataset = all_past_due_loan_disk.copy()
                        elif open_loan_option == "Missed Repayment":
                            dataset = all_missed_repay_loan_disk.copy()
                        elif open_loan_option == "Loan Repayment":
                            dataset = all_repayment_loan_disk.copy()

                        unique_users = pd.Series(dataset['Name'].unique()).sort_values()
                        selected_user = st.selectbox("Select User", ["All Users"] + list(unique_users), index=0)

                        if selected_user != "All Users":
                            dataset = dataset[dataset['Name'] == selected_user]

                            show_reappearing = st.toggle("Show Individual Transactions (if user has other loans)")

                            if show_reappearing:
                                dataset['Date'] = pd.to_datetime(dataset['Date'])
                                # dataset = dataset.sort_values(['Name', 'Date'])

                                # Identify separate transactions by checking when a user reappears after completing a previous transaction
                                dataset['Transaction_Group'] = (dataset['Name'] != dataset['Name'].shift()) | (dataset['Date'].diff().dt.days > 1)
                                dataset['Transaction_Group'] = dataset['Transaction_Group'].cumsum()

                                # Split dataset into separate DataFrames per transaction occurrence
                                for group_num, transaction_data in dataset.groupby("Transaction_Group"):
                                    st.write(f"### Transaction {group_num} for {selected_user}")
                                    st.write(transaction_data.drop(columns=['Transaction_Group']))
                            else:
                                st.write(f"### Transaction for {selected_user}")
                                st.write(dataset)
                        else:
                            st.write(f"### Transaction for {selected_user}")
                            st.write(dataset)
                    else:
                        st.write("### Summarized Dataset")
                        st.write(summary_past_mat_loan_disk if open_loan_option == "Past Maturity" else
                                summary_missed_repayment_loan_disk if open_loan_option == "Missed Repayment" else
                                summary_repayment_loan_disk)

            # If 'Completed Loans' is selected
            elif loan_category == "Completed":
                completed_loan_option = st.selectbox("Loan Sub-Status", ["Loans Completed"], placeholder="Select Loan Type", index=None)

                if completed_loan_option:
                    show_full_data = st.toggle("Show Expanded Dataset")

                    if show_full_data:
                        dataset = all_completed_loan_disk.copy()
                        unique_users = pd.Series(dataset['Name'].unique()).sort_values()
                        selected_user = st.selectbox("Select User", ["All Users"] + list(unique_users), index=0)

                        if selected_user != "All Users":
                            dataset = dataset[dataset['Name'] == selected_user]

                            show_reappearing = st.toggle("Show Individual Transactions (if user has other loans)")

                            if show_reappearing:
                                dataset['Date'] = pd.to_datetime(dataset['Date'])
                                # dataset = dataset.sort_values(['Name', 'Date'])
                                dataset['Transaction_Group'] = (dataset['Name'] != dataset['Name'].shift()) | (dataset['Date'].diff().dt.days > 1)
                                dataset['Transaction_Group'] = dataset['Transaction_Group'].cumsum()

                                for group_num, transaction_data in dataset.groupby("Transaction_Group"):
                                    st.write(f"### Completed Loan Transaction {group_num} for {selected_user}")
                                    st.write(transaction_data.drop(columns=['Transaction_Group']))
                            else:
                                st.write(f"### Completed Loan Transaction for {selected_user}")
                                st.write(dataset)
                        else:
                            st.write(f"### Completed Loan Transaction for {selected_user}")
                            st.write(dataset)
                    else:
                        st.write("### Summarized Dataset")
                        st.write(summary_completed_loan_disk)

    ################################################################################### Tab 5 #################################################################################

    with tab5:

    ######################################################################## Merchant portfolio ####################################################################################

        # Copy fiiltered_data as dataframe
        filtered_merchants_df = filtered_data[filtered_data['loan_type']=='MERCHANT_OVERDRAFT'] # This is for all merchant loans
        merchant_loan_disk_df = filtered_merchants_df.copy()

        # Copy filtered_repayment_df as dataframe
        merchant_loan_disk_repay_df = filtered_repayment_df.copy()

        # Copy past_due_date as dataframe
        merchant_loan_disk_past_mat_df = past_due_date.copy()

        # Copy fiiltered_data as dataframe
        merchant_loan_disk_missed_repay_df = filtered_merchants_df.copy()
        # Filters only missed repayment
        merchant_loan_disk_missed_repay_df = merchant_loan_disk_missed_repay_df[merchant_loan_disk_missed_repay_df['missed_days'] > 0]

        # Completed_merchant_loan_disk as dataframe
        completed_merchant_loan_disk = filtered_merchants_df.copy()
        # Filters only completed loans
        completed_merchant_loan_disk = completed_merchant_loan_disk[completed_merchant_loan_disk['status'] == 'COMPLETED']

        # Select Merchant from unique list
        selected_merchant = st.selectbox("Select Merchant", pd.Series(merchant_loan_disk_df['merchant_agents'].unique()).sort_values(), placeholder="Select or Enter Merchant To Begin...", index=None)

        if selected_merchant:
            # Filter loans based on selected merchant
            filtered_merchant_loan_disk_loans = merchant_loan_disk_df[merchant_loan_disk_df['merchant_agents'] == selected_merchant]

            # Get unique agents under the selected merchant
            unique_agents = pd.Series(filtered_merchant_loan_disk_loans['merchant_agents'].unique()).sort_values()

            # # Select Agent (Conditional: Only appears when a merchant is selected)
            # selected_agent = st.selectbox("Select Merchant Agent", ["All Agents"] + list(unique_agents), index=0)

            # # Further filter by agent (if an agent is selected and not 'All Agents')
            # if selected_agent != "All Agents":
            #     filtered_merchant_loan_disk_loans = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans['merchant_agents'] == selected_agent]

            # Get loan IDs after filtering by merchant (and agent if selected)
            loan_ids = filtered_merchant_loan_disk_loans['id'].unique()

            # Filter other datasets using loan IDs
            filtered_merchant_loan_disk_repayment_df = merchant_loan_disk_repay_df[merchant_loan_disk_repay_df['ajo_loan_id'].isin(loan_ids)]
            filtered_merchant_loan_disk_past_mat_df = merchant_loan_disk_past_mat_df[merchant_loan_disk_past_mat_df['id'].isin(loan_ids)]
            filtered_merchant_loan_disk_missed_repay_df = merchant_loan_disk_missed_repay_df[merchant_loan_disk_missed_repay_df['id'].isin(loan_ids)]
            filtered_merchant_loan_disk_completed_df = completed_merchant_loan_disk[completed_merchant_loan_disk['id'].isin(loan_ids)]
            filtered_exp_till_today_ = exp_till_today_[exp_till_today_['id'].isin(loan_ids)]
            filtered_exp_till_yesterday_ = exp_till_yesterday_[exp_till_yesterday_['id'].isin(loan_ids)]
            filtered_exp_till_ThisWeek_ = exp_till_ThisWeek_[exp_till_ThisWeek_['id'].isin(loan_ids)]
            filtered_exp_till_ThisMonth_ = exp_till_ThisMonth_[exp_till_ThisMonth_['id'].isin(loan_ids)]
            filtered_exp_till_ThisYear_ = exp_till_ThisYear_[exp_till_ThisYear_['id'].isin(loan_ids)]
            filtered_exp_all_time = exp_all_time[exp_all_time['id'].isin(loan_ids)]

            st.markdown("---")

    ######################################################################### Loan summarised metrics ####################################################################################

            # # Further filter by agent (if an agent is selected and not 'All Agents')
            # if selected_agent != "All Agents":

            # Custom CSS for bordered metrics
            st.markdown(
                """
                <style>
                ._metric-container {
                    border: 4px solid #4b9ca5; /* Sea green border */
                    border-radius: 0px;
                    padding: 10px;
                    text-align: center;
                    margin: 15px;
                    width: 100%;
                    box-shadow: 3px 3px 5px rgba(0, 151, 167, 0.5); /* shadow */
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }
                ._metric-value {
                    font-size: 20px;
                    font-weight: bold;
                    white-space: normal;
                    max-width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                ._metric-title {
                    font-size: 30px;
                    color: #777;
                    font-weight: bold;
                    margin-bottom: 10px;
                    white-space: normal;
                    text-align: center;
                }
                ._metric-label {
                    font-size: 17px;
                    color: #555;
                    font-weight: bold;
                    white-space: normal;
                    text-align: center;
                }
                ._metric-name {
                    font-size: 17px;
                    font-weight: lighter;
                    white-space: normal;
                    text-align: center;
                }
                </style>
                """, unsafe_allow_html=True
            )

            # Custom metric display function for entire category
            def bordered_category_(metrics):
                st.markdown(
                    """
                    <div style='text-align:center; font-size:15px;'>
                        <h1 style='text-shadow: 3px 3px 6px rgba(32, 178, 170, 0.8);'>
                            <strong>Seeds Metrics</strong>
                        </h1>
                    </div>
                    """,
                    unsafe_allow_html=True
                )
                for label1, name1, value1, label2, name2, value2, label3, name3, value3, label4, name4, value4 in metrics:
                    st.markdown(
                        f"""
                        <div style="display: flex; justify-content: space-between; width: 100%;">
                            <div style="text-align: center; flex: 1; padding: 10px;">
                            <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                <div class="_metric-label">{label1}</div>
                                <div class="_metric-name">{name1}</div>
                                <div class="_metric-value">{value1}</div>
                            <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                            </div>
                            <div style="text-align: center; flex: 1; padding: 10px;">
                            <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                            <div class="_metric-label">{label2}</div>
                                <div class="_metric-name">{name2}</div>
                                <div class="_metric-value">{value2}</div>
                            <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                            </div>
                            <div style="text-align: center; flex: 1; padding: 10px;">
                            <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                <div class="_metric-label">{label3}</div>
                                <div class="_metric-name">{name3}</div>
                                <div class="_metric-value">{value3}</div>
                            <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                            </div>
                            <div style="text-align: center; flex: 1; padding: 10px;">
                            <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                <div class="_metric-label">{label4}</div>
                                <div class="_metric-name">{name4}</div>
                                <div class="_metric-value">{value4}</div>
                            <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                            </div>
                        </div>
                        """, unsafe_allow_html=True
                    )
                st.markdown("</div>", unsafe_allow_html=True)  # Close container

            # Filter datasets for loans by paid_date
            LD_repay_df_today = filtered_merchant_loan_disk_repayment_df[filtered_merchant_loan_disk_repayment_df['paid_date'] == today]
            LD_repay_df_yesterday = filtered_merchant_loan_disk_repayment_df[filtered_merchant_loan_disk_repayment_df['paid_date'] == yesterday]
            LD_repay_df_month = filtered_merchant_loan_disk_repayment_df[filtered_merchant_loan_disk_repayment_df['paid_date'] >= start_of_month]

            # Aggregate data for repayment
            LD_repay_today_total = LD_repay_df_today['repayment_by_date'].sum()
            LD_repay_yesterday_total = LD_repay_df_yesterday['repayment_by_date'].sum()
            LD_repay_month_total = LD_repay_df_month['repayment_by_date'].sum()
            LD_repay_all_time_total = filtered_merchant_loan_disk_repayment_df['repayment_by_date'].sum()

            # Filter datasets for loans by date_disbursed
            LD_df_today = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans['date_disbursed'] == today]
            LD_df_yesterday = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans['date_disbursed'] == yesterday]
            LD_df_month = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans['date_disbursed'] >= start_of_month]

            # Aggregate data for disbursement
            LD_disbursed_today_total = LD_df_today['amount'].sum()
            LD_disbursed_yesterday_total = LD_df_yesterday['amount'].sum()
            LD_disbursed_month_total = LD_df_month['amount'].sum()
            LD_disbursed_all_time_total = filtered_merchant_loan_disk_loans['amount'].sum()

            LD_active_users = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans['status']=='OPEN'] # Filter data for active users
            LD_active_users = LD_active_users['id'].count() # Gets count of active users
            LD_outstanding_loans = filtered_merchant_loan_disk_loans['open_portfolio'].sum() # Aggregate data for outstanding loans
            LD_past_mat_all_time_total = filtered_merchant_loan_disk_past_mat_df['open_portfolio'].sum() # Aggregate data for past maturity
            LD_missed_repay_all_time_total = filtered_merchant_loan_disk_missed_repay_df['missed_repayment'].sum() # Aggregate data for missed repayment
            filtered_exp_till_today_ = filtered_exp_till_today_['exp_repay_today'].sum()
            filtered_exp_till_yesterday_ = filtered_exp_till_yesterday_['exp_repay_today'].sum()
            filtered_exp_till_ThisWeek_ = filtered_exp_till_ThisWeek_['exp_repay_today'].sum()
            filtered_exp_till_ThisMonth_ = filtered_exp_till_ThisMonth_['exp_repay_today'].sum()
            filtered_exp_till_ThisYear_ = filtered_exp_till_ThisYear_['exp_repay_today'].sum()
            filtered_exp_all_time = filtered_exp_all_time['exp_repay_today'].sum()

            bordered_category_([
                ("Today", "Loan Disbursed", f"N{LD_disbursed_today_total:,.2f}", "Yesterday", "Loan Disbursed", f"N{LD_disbursed_yesterday_total:,.2f}", "This Month", "Loan Disbursed", f"N{LD_disbursed_month_total:,.2f}", "All-Time", "Loan Disbursed", f"N{LD_disbursed_all_time_total:,.2f}"),
                ("Today", "Loan Paid", f"N{LD_repay_today_total:,.2f}", "Yesterday", "Loan Paid", f"N{LD_repay_yesterday_total:,.2f}", "This Month", "Loan Paid", f"N{LD_repay_month_total:,.2f}", "All-Time", "Loan Paid", f"N{LD_repay_all_time_total:,.2f}"),
                ("Today", "Due Collections", f"N{filtered_exp_till_today_:,.2f}", "Yesterday", "Due Collections", f"N{filtered_exp_till_yesterday_:,.2f}", "This Month", "Due Collections", f"N{filtered_exp_till_ThisMonth_:,.2f}", "All-Time", "Due Collections", f"N{filtered_exp_all_time:,.2f}"),
                ("All-Time", "Total Active Borrowers", f"{LD_active_users:,.0f} users", "All-time", "Missed Repayment", f"N{LD_missed_repay_all_time_total:,.2f}", "All-time", "Outstanding Loan", f"N{LD_outstanding_loans:,.2f}", "All-Time", "Loans Past Maturity", f"N{LD_past_mat_all_time_total:,.2f}"),
            ])

    # ######################################################################### Gets dataframe by Date filters ####################################################################################

    #             else:
    #                 # Filter loans for today alone
    #                 loans_disk_df_today = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans["date_disbursed"] == today]

    #                 # Filter loans for yesterday alone
    #                 loans_disk_df_yesterday = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans["date_disbursed"] == yesterday]

    #                 # Filter loans from this week alone
    #                 loans_disk_df_this_week = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans["date_disbursed"] >= start_of_week]

    #                 # Filter loans from this month alone
    #                 loans_disk_df_this_month = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans["date_disbursed"] >= start_of_month]

    #                 # Filter loans from this year alone
    #                 loans_disk_df_this_year = filtered_merchant_loan_disk_loans[filtered_merchant_loan_disk_loans["date_disbursed"] >= start_of_year]

    #                 # Filter loans all time
    #                 loans_disk_df_all_time = filtered_merchant_loan_disk_loans.copy()

    # ######################################################################### Agents Disbursement ####################################################################################

    #                 @st.cache_data(show_spinner=False)
    #                 def create_disbursement_chart(df, title):
    #                     # Group by 'merchant_agents' to calculate total amount per agent
    #                     agent_summary = df.groupby('merchant_agents', as_index=False).agg(
    #                         total_amount=('amount', 'sum')  # Summing the amount per agent
    #                     )

    #                     # Format amount with commas
    #                     agent_summary['total_amount'] = agent_summary['total_amount'].round(2)
    #                     agent_summary['formatted_total_amount'] = agent_summary['total_amount'].apply(lambda x: f"{x:,.0f}")

    #                     # Sort the main dataset by total_amount
    #                     df = agent_summary.sort_values(by='total_amount', ascending=False)

    #                     # Create a bar chart with amount and total amount
    #                     fig = px.bar(
    #                         df,
    #                         x='total_amount',
    #                         y='merchant_agents',
    #                         orientation='h',
    #                         title=title,
    #                         color='total_amount',
    #                         color_continuous_scale='blues',
    #                         hover_data={'total_amount': False, 'formatted_total_amount': True}  # Show formatted value
    #                     )

    #                     # Update layout to reduce background opacity
    #                     fig.update_layout(
    #                         paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
    #                         plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background
    #                         modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
    #                     )

    #                     return fig

    # ######################################################################### Gets repayment dataframe by Date filters ####################################################################################

    #                 # Drop 'agent_id' to mergfe successfully and avoid a clash with 'agent_id' in repayment table
    #                 merchant_loan_disk_repay_merge = filtered_merchant_loan_disk_loans.drop(columns='agent_id')

    #                 # merge repayments and loans by months
    #                 merged_merchant_loan_disk_with_repay = merchant_loan_disk_repay_merge.merge(filtered_merchant_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

    #                 # Filter loans for today alone
    #                 loans_disk_repayment_df_today = merged_merchant_loan_disk_with_repay[merged_merchant_loan_disk_with_repay["paid_date"] == today]

    #                 # Filter loans for yesterday alone
    #                 loans_disk_repayment_df_yesterday = merged_merchant_loan_disk_with_repay[merged_merchant_loan_disk_with_repay["paid_date"] == yesterday]

    #                 # Filter loans from this week alone
    #                 loans_disk_repayment_df_this_week = merged_merchant_loan_disk_with_repay[merged_merchant_loan_disk_with_repay["paid_date"] >= start_of_week]

    #                 # Filter loans from this month alone
    #                 loans_disk_repayment_df_this_month = merged_merchant_loan_disk_with_repay[merged_merchant_loan_disk_with_repay["paid_date"] >= start_of_month]

    #                 # Filter loans from this year alone
    #                 loans_disk_repayment_df_this_year = merged_merchant_loan_disk_with_repay[merged_merchant_loan_disk_with_repay["paid_date"] >= start_of_year]

    #                 # Filter loans from this year alone
    #                 loans_disk_repayment_df_all_time = merged_merchant_loan_disk_with_repay.copy()

    # ######################################################################### Agents Repayment ####################################################################################

    #                 @st.cache_data(show_spinner=False)
    #                 def create_repayment_chart(df, exp_repay_df, title):
    #                     # Merge actual repayments with expected repayments
    #                     df = df.groupby(['agent_id', 'merchant_agents'])['repayment_by_date'].sum().reset_index()
    #                     exp_repay_df = exp_repay_df.groupby(['agent_id'])['exp_repay_today'].sum().reset_index()
    #                     df = df.merge(exp_repay_df, on='agent_id', how='left')

    #                     # Summarize by agent
    #                     df_summary = df.groupby('merchant_agents', as_index=False).agg(
    #                         total_repayment=('repayment_by_date', 'sum'),
    #                         total_expected=('exp_repay_today', 'sum')
    #                     )

    #                     # Compute repayment rate, treating zero expected as full completion
    #                     df_summary['repayment_rate'] = np.where(
    #                         df_summary['total_expected'] == 0,
    #                         1.0,
    #                         df_summary['total_repayment'] / df_summary['total_expected']
    #                     )

    #                     # Cap at 100%
    #                     df_summary['repayment_rate'] = df_summary['repayment_rate'].clip(upper=1.0)

    #                     # Categorize for coloring
    #                     df_summary['progress_category'] = np.where(
    #                         df_summary['repayment_rate'] >= 1.0,
    #                         "Fully Met ✅",
    #                         "Below Target ❌"
    #                     )

    #                     # Format for display
    #                     df_summary['formatted_repayment'] = df_summary['total_repayment'].map("{:,.2f}".format)
    #                     df_summary['formatted_expected'] = df_summary['total_expected'].map("{:,.2f}".format)
    #                     df_summary['display_rate'] = (df_summary['repayment_rate'] * 100).round(0).astype(int).astype(str) + "%"

    #                     # Sort by rate descending
    #                     df_summary = df_summary.sort_values('repayment_rate', ascending=False)

    #                     # Build bar chart
    #                     fig = px.bar(
    #                         df_summary,
    #                         x='repayment_rate',
    #                         y='merchant_agents',
    #                         orientation='h',
    #                         title=title,
    #                         color='progress_category',
    #                         color_discrete_map={"Fully Met ✅": "green", "Below Target ❌": "red"},
    #                         text='display_rate',
    #                         hover_data={
    #                             'formatted_repayment': True,
    #                             'formatted_expected': True,
    #                             'display_rate': True,
    #                             'progress_category': False,
    #                             'repayment_rate': False
    #                         }
    #                     )

    #                     # Tweak hover and layout
    #                     fig.update_traces(
    #                         hovertemplate=(
    #                             "Repayment: %{customdata[0]}<br>"
    #                             "Expected: %{customdata[1]}<br>"
    #                             "Rate: %{text}"
    #                         ),
    #                         textposition='inside'
    #                     )
    #                     fig.update_layout(
    #                         xaxis=dict(title='Repayment Progress (%)', tickformat=".0%"),
    #                         yaxis=dict(title='Agent'),
    #                         paper_bgcolor="rgba(0,0,0,0)",
    #                         plot_bgcolor="rgba(0,0,0,0)",
    #                         modebar={"bgcolor": "rgba(0,0,0,0)"}
    #                     )

    #                     st.plotly_chart(fig, use_container_width=True)

    # ############################################################## Make plots based on progress and disbursements #########################################################################

    #                 loan_id_ = filtered_merchant_loan_disk_loans[['id', 'agent_id']]
    #                 filtered_exp_till_today_ = filtered_exp_till_today_.merge(loan_id_, how='left', on='id')
    #                 filtered_exp_till_yesterday_ = filtered_exp_till_yesterday_.merge(loan_id_, how='left', on='id')
    #                 filtered_exp_till_ThisWeek_ = filtered_exp_till_ThisWeek_.merge(loan_id_, how='left', on='id')
    #                 filtered_exp_till_ThisMonth_ = filtered_exp_till_ThisMonth_.merge(loan_id_, how='left', on='id')
    #                 filtered_exp_till_ThisYear_ = filtered_exp_till_ThisYear_.merge(loan_id_, how='left', on='id')
    #                 filtered_exp_all_time = filtered_exp_all_time.merge(loan_id_, how='left', on='id')

    #                 col1, col2 = st.columns(2)
    #                 with col1:
    #                     st.plotly_chart(create_disbursement_chart(loans_disk_df_today, "Disbursement By Agents (Today)"), use_container_width=True)
    #                 with col2:
    #                     create_repayment_chart(loans_disk_repayment_df_today, filtered_exp_till_today_, "Collections By Agents (Today)")

    #                 st.markdown("---")

    #                 col1, col2 = st.columns(2)
    #                 with col1:
    #                     st.plotly_chart(create_disbursement_chart(loans_disk_df_yesterday, "Disbursement By Agents (Yesterday)"), use_container_width=True)
    #                 with col2:
    #                     create_repayment_chart(loans_disk_repayment_df_yesterday, filtered_exp_till_yesterday_, "Collections By Agents (Yesterday)")

    #                 st.markdown("---")

    #                 col1, col2 = st.columns(2)
    #                 with col1:
    #                     st.plotly_chart(create_disbursement_chart(loans_disk_df_this_week, "Disbursement By Agents (This Week)"), use_container_width=True)
    #                 with col2:
    #                     create_repayment_chart(loans_disk_repayment_df_this_week, filtered_exp_till_ThisWeek_, "Collections By Agents (This Week)")

    #                 st.markdown("---")

    #                 col1, col2 = st.columns(2)
    #                 with col1:
    #                     st.plotly_chart(create_disbursement_chart(loans_disk_df_this_month, "Disbursement By Agents (This Month)"), use_container_width=True)
    #                 with col2:
    #                     create_repayment_chart(loans_disk_repayment_df_this_month, filtered_exp_till_ThisMonth_, "Collections By Agents (This Month)")

    #                 st.markdown("---")

    #                 col1, col2 = st.columns(2)
    #                 with col1:
    #                     st.plotly_chart(create_disbursement_chart(loans_disk_df_this_year, "Disbursement By Agents (This Year)"), use_container_width=True)
    #                 with col2:
    #                     create_repayment_chart(loans_disk_repayment_df_this_year, filtered_exp_till_ThisYear_, "Collections By Agents (This Year)")

    #                 st.markdown("---")

    #                 col1, col2 = st.columns(2)
    #                 with col1:
    #                     st.plotly_chart(create_disbursement_chart(loans_disk_df_all_time, "Disbursement By Agents (All Time)"), use_container_width=True)
    #                 with col2:
    #                     create_repayment_chart(loans_disk_repayment_df_all_time, filtered_exp_all_time, "Collections By Agents (All Time)")

            st.markdown("---")

    ############################################################### Dropdown to select between Open Loans and Completed Loans #########################################################################

            ## PAST MATURITY
            summary_past_mat_merchant_loan_disk = filtered_merchant_loan_disk_past_mat_df.copy()
            ### Copy dataset to get last date in repayment
            last_date_repayment = filtered_merchant_loan_disk_repayment_df.copy()
            ### Gets the last date by sorting and grouping
            last_date_repayment = last_date_repayment.sort_values(by=['ajo_loan_id', 'paid_date']).groupby('ajo_loan_id').last().reset_index()
            #### Merge past maturity with repayments
            all_past_due_merchant_loan_disk = filtered_merchant_loan_disk_past_mat_df.merge(filtered_merchant_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')
            all_past_due_merchant_loan_disk = all_past_due_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True]).reset_index()

            # Convert to datetime
            summary_past_mat_merchant_loan_disk['start_date'] = pd.to_datetime(summary_past_mat_merchant_loan_disk['start_date'])  # Convert to datetime
            all_past_due_merchant_loan_disk['start_date'] = pd.to_datetime(all_past_due_merchant_loan_disk['start_date'])  # Convert to datetime
            # Calculate number of days from start_date till today
            summary_past_mat_merchant_loan_disk['age_in_days'] = (datetime.today() - summary_past_mat_merchant_loan_disk['start_date']).dt.days
            all_past_due_merchant_loan_disk['age_in_days'] = (datetime.today() - all_past_due_merchant_loan_disk['start_date']).dt.days
            # # Convert days to months (assuming 30 days per month for approximation)
            # summary_past_mat_merchant_loan_disk['age_in_months'] = (summary_past_mat_merchant_loan_disk['age_in_days'] / 30).apply(lambda x: round(x))
            # Gets duration
            summary_past_mat_merchant_loan_disk['Duration'] = summary_past_mat_merchant_loan_disk['start_date'] - datetime.today()
            all_past_due_merchant_loan_disk['Duration'] = all_past_due_merchant_loan_disk['start_date'] - datetime.today()
            # Compute cumulative sum per 'id'
            all_past_due_merchant_loan_disk['cumulative_repayment'] = all_past_due_merchant_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_past_mat_merchant_loan_disk['principal_balance'] = (summary_past_mat_merchant_loan_disk['principal_element'] - summary_past_mat_merchant_loan_disk['repayment']).clip(lower=0)
            all_past_due_merchant_loan_disk['principal_paid'] = (all_past_due_merchant_loan_disk['principal_element_pct'] * all_past_due_merchant_loan_disk['cumulative_repayment']).round(2)
            all_past_due_merchant_loan_disk['principal_balance'] = (all_past_due_merchant_loan_disk['amount'] - all_past_due_merchant_loan_disk['principal_paid']).clip(lower=0)
            # Subtract from initial amount in the same row
            all_past_due_merchant_loan_disk['Bal_per_paid_date'] = all_past_due_merchant_loan_disk['expected_repayment'] - all_past_due_merchant_loan_disk['cumulative_repayment']
            # Get few columnsin repayment before merging
            last_date_repayment = last_date_repayment[['ajo_loan_id', 'paid_date', 'repayment_by_date']]
            # merge summary with repayment to get the last date of payment
            summary_past_mat_merchant_loan_disk = summary_past_mat_merchant_loan_disk.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
            # sort by dates
            summary_past_mat_merchant_loan_disk = summary_past_mat_merchant_loan_disk.sort_values(by=['past_maturity'], ascending=False).reset_index()
            all_past_due_merchant_loan_disk = all_past_due_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()
            # Arranges dataset for summary
            summary_past_mat_merchant_loan_disk = summary_past_mat_merchant_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'merchant_agents',
            'amount', 'principal_balance', 'expected_repayment', 'repayment', 'due_today_amount', 'open_portfolio', 'end_date',
            'past_maturity', 'paid_date', 'repayment_by_date', 'age_in_days', 'Duration']]
            # Arranges dataset for all past due
            all_past_due_merchant_loan_disk = all_past_due_merchant_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'merchant_agents',
            'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio', 'end_date',
            'past_maturity', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date', 'due_today_amount']]
            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "merchant_agents": "Merchant Officer",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment_by_date": "Repayment",
                "open_portfolio": "Pending Due",
                "past_maturity": "Days Past",
                "end_date": "Maturity Date",
                "paid_date": "Payment Date",
                "repayment": "Paid",
            }

            summary_past_mat_merchant_loan_disk.rename(columns=column_mappings, inplace=True)
            all_past_due_merchant_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Missed Repayment Summary ####################################################################################

            ## MISSED REPAYMENT
            summary_completed_merchant_loan_disk = filtered_merchant_loan_disk_completed_df.copy()
            #### Merge missed repayments
            all_completed_merchant_loan_disk = filtered_merchant_loan_disk_completed_df.merge(filtered_merchant_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

            # Sort by 'missed_days'
            summary_completed_merchant_loan_disk = summary_completed_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name'], ascending=True)
            all_completed_merchant_loan_disk = all_completed_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True])
            # Calculate number of days from start_date till today
            summary_completed_merchant_loan_disk['age_in_days'] = (summary_completed_merchant_loan_disk['end_date'] - summary_completed_merchant_loan_disk['start_date']).dt.days
            all_completed_merchant_loan_disk['age_in_days'] = (all_completed_merchant_loan_disk['end_date'] - all_completed_merchant_loan_disk['start_date']).dt.days
            # Gets duration
            summary_completed_merchant_loan_disk['Duration'] = summary_completed_merchant_loan_disk['start_date'] - summary_completed_merchant_loan_disk['end_date']
            all_completed_merchant_loan_disk['Duration'] = all_completed_merchant_loan_disk['start_date'] - all_completed_merchant_loan_disk['end_date']
            # merge summary with repayment to get the last date of payment
            summary_completed_merchant_loan_disk = summary_completed_merchant_loan_disk.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
            # Compute cumulative sum per 'id'
            all_completed_merchant_loan_disk['cumulative_repayment'] = all_completed_merchant_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_completed_merchant_loan_disk['principal_balance'] = (summary_completed_merchant_loan_disk['principal_element'] - summary_completed_merchant_loan_disk['repayment']).clip(lower=0)
            all_completed_merchant_loan_disk['principal_paid'] = (all_completed_merchant_loan_disk['principal_element_pct'] * all_completed_merchant_loan_disk['cumulative_repayment']).round(2)
            all_completed_merchant_loan_disk['principal_balance'] = (all_completed_merchant_loan_disk['amount'] - all_completed_merchant_loan_disk['principal_paid']).clip(lower=0)
            # Subtract from initial amount in the same row
            all_completed_merchant_loan_disk['Bal_per_paid_date'] = all_completed_merchant_loan_disk['expected_repayment'] - all_completed_merchant_loan_disk['cumulative_repayment']
            # sort by dates
            summary_completed_merchant_loan_disk = summary_completed_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name'], ascending=True).reset_index()
            all_completed_merchant_loan_disk = all_completed_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()
            # Gets columns and arragnes
            summary_completed_merchant_loan_disk = summary_completed_merchant_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'merchant_agents',
            'amount', 'principal_balance', 'principal_element', 'expected_repayment', 'repayment', 'due_today_amount', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'age_in_days', 'Duration']]
            # Arranges dataset for all past due
            all_completed_merchant_loan_disk = all_completed_merchant_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'merchant_agents',
            'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date']]
            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "merchant_agents": "Merchant Officer",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment": "Paid",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "paid_date": "Last Payment Date",
                "repayment_by_date": "Last Payment"
            }

            summary_completed_merchant_loan_disk.rename(columns=column_mappings, inplace=True)
            all_completed_merchant_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Missed Repayment Summary ####################################################################################

            ## MISSED REPAYMENT
            summary_missed_repayment_merchant_loan_disk = filtered_merchant_loan_disk_missed_repay_df.copy()
            #### Merge missed repayments
            all_missed_repay_merchant_loan_disk = filtered_merchant_loan_disk_missed_repay_df.merge(filtered_merchant_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

            # Sort by 'missed_days'
            summary_missed_repayment_merchant_loan_disk = summary_missed_repayment_merchant_loan_disk.sort_values(by='missed_days', ascending=False)
            all_missed_repay_merchant_loan_disk = all_missed_repay_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True])
            # Convert to datetime
            summary_missed_repayment_merchant_loan_disk['start_date'] = pd.to_datetime(summary_missed_repayment_merchant_loan_disk['start_date'])
            all_missed_repay_merchant_loan_disk['start_date'] = pd.to_datetime(all_missed_repay_merchant_loan_disk['start_date'])

            # Get current date and ensure it's a Timestamp
            current_date = pd.Timestamp.today().normalize()
            # Calculate Loan Duration
            summary_missed_repayment_merchant_loan_disk['age_in_days'] = (summary_missed_repayment_merchant_loan_disk['end_date'].clip(upper=current_date) - (summary_missed_repayment_merchant_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            all_missed_repay_merchant_loan_disk['age_in_days'] = (all_missed_repay_merchant_loan_disk['end_date'].clip(upper=current_date) - (all_missed_repay_merchant_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            # Gets loan duration in months
            summary_missed_repayment_merchant_loan_disk['Duration'] = (summary_missed_repayment_merchant_loan_disk['end_date'].clip(upper=current_date) - (summary_missed_repayment_merchant_loan_disk['start_date'] - pd.Timedelta(days=4)))
            all_missed_repay_merchant_loan_disk['Duration'] = (all_missed_repay_merchant_loan_disk['end_date'].clip(upper=current_date) - (all_missed_repay_merchant_loan_disk['start_date'] - pd.Timedelta(days=4)))

            # merge summary with repayment to get the last date of payment
            summary_missed_repayment_merchant_loan_disk = summary_missed_repayment_merchant_loan_disk.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
            # Compute cumulative sum per 'id'
            all_missed_repay_merchant_loan_disk['cumulative_repayment'] = all_missed_repay_merchant_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_missed_repayment_merchant_loan_disk['principal_balance'] = (summary_missed_repayment_merchant_loan_disk['principal_element'] - summary_missed_repayment_merchant_loan_disk['repayment']).clip(lower=0)
            all_missed_repay_merchant_loan_disk['principal_paid'] = (all_missed_repay_merchant_loan_disk['principal_element_pct'] * all_missed_repay_merchant_loan_disk['cumulative_repayment']).round(2)
            all_missed_repay_merchant_loan_disk['principal_balance'] = (all_missed_repay_merchant_loan_disk['amount'] - all_missed_repay_merchant_loan_disk['principal_paid']).clip(lower=0)
            # Subtract from initial amount in the same row
            all_missed_repay_merchant_loan_disk['Bal_per_paid_date'] = all_missed_repay_merchant_loan_disk['expected_repayment'] - all_missed_repay_merchant_loan_disk['cumulative_repayment']
            # sort by dates
            summary_missed_repayment_merchant_loan_disk = summary_missed_repayment_merchant_loan_disk.sort_values(by=['missed_days', 'users_full_name'], ascending=[False, True]).reset_index()
            all_missed_repay_merchant_loan_disk = all_missed_repay_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()
            # Gets columns and arragnes
            summary_missed_repayment_merchant_loan_disk = summary_missed_repayment_merchant_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'merchant_agents',
            'amount', 'principal_balance', 'expected_repayment', 'repayment', 'due_today_amount', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'age_in_days', 'Duration']]
            # Arranges dataset for all past due
            all_missed_repay_merchant_loan_disk = all_missed_repay_merchant_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'merchant_agents',
            'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date', 'due_today_amount']]
            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "merchant_agents": "Merchant Officer",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment": "Paid",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "paid_date": "Last Payment Date",
                "repayment_by_date": "Last Payment"
            }

            summary_missed_repayment_merchant_loan_disk.rename(columns=column_mappings, inplace=True)
            all_missed_repay_merchant_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Repayment summary ####################################################################################

            # Copy filtered data
            summary_repayment_merchant_loan_disk = filtered_merchant_loan_disk_repayment_df.copy()
            LoanDisk_df = filtered_merchant_loan_disk_loans.copy()

            # Summarize repayments by loan ID
            summary_repayment_merchant_loan_disk = summary_repayment_merchant_loan_disk.groupby('ajo_loan_id')['repayment_by_date'].sum().reset_index()

            # Merge with loan details
            summary_repayment_merchant_loan_disk = summary_repayment_merchant_loan_disk.merge(LoanDisk_df, how='left', left_on='ajo_loan_id', right_on='id')
            all_repayment_merchant_loan_disk = filtered_merchant_loan_disk_repayment_df.merge(LoanDisk_df, how='left', left_on='ajo_loan_id', right_on='id')
            all_repayment_merchant_loan_disk = all_repayment_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True])

            # Calculate principal balance
            summary_repayment_merchant_loan_disk['principal_balance'] = (summary_repayment_merchant_loan_disk['amount'] - summary_repayment_merchant_loan_disk['repayment']).clip(lower=0)
            all_repayment_merchant_loan_disk['principal_balance'] = (all_repayment_merchant_loan_disk['amount'] - all_repayment_merchant_loan_disk['repayment']).clip(lower=0)

            # Get current date and ensure it's a Timestamp
            current_date = pd.Timestamp.today().normalize()
            # Calculate Loan Duration
            summary_repayment_merchant_loan_disk['age_in_days'] = (summary_repayment_merchant_loan_disk['end_date'].clip(upper=current_date) - (summary_repayment_merchant_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            all_repayment_merchant_loan_disk['age_in_days'] = (all_repayment_merchant_loan_disk['end_date'].clip(upper=current_date) - (all_repayment_merchant_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            # Gets loan duration in months
            summary_repayment_merchant_loan_disk['Duration'] = (summary_repayment_merchant_loan_disk['end_date'].clip(upper=current_date) - (summary_repayment_merchant_loan_disk['start_date'] - pd.Timedelta(days=4)))
            all_repayment_merchant_loan_disk['Duration'] = (all_repayment_merchant_loan_disk['end_date'].clip(upper=current_date) - (all_repayment_merchant_loan_disk['start_date'] - pd.Timedelta(days=4)))
            # Calculate days left only for loans that haven't reached their end date
            summary_repayment_merchant_loan_disk['days_to_maturity'] = (summary_repayment_merchant_loan_disk['end_date'] - current_date).dt.days
            all_repayment_merchant_loan_disk['days_to_maturity'] = (all_repayment_merchant_loan_disk['end_date'] - current_date).dt.days
            # If 'end_date' is already passed, set 'days_to_maturity' to 0
            summary_repayment_merchant_loan_disk.loc[summary_repayment_merchant_loan_disk['days_to_maturity'] < 0, 'days_to_maturity'] = 0
            all_repayment_merchant_loan_disk.loc[all_repayment_merchant_loan_disk['days_to_maturity'] < 0, 'days_to_maturity'] = 0
            # Compute cumulative repayment and balance per paid date
            all_repayment_merchant_loan_disk['cumulative_repayment'] = all_repayment_merchant_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_repayment_merchant_loan_disk['principal_balance'] = (summary_repayment_merchant_loan_disk['principal_element'] - summary_repayment_merchant_loan_disk['repayment']).clip(lower=0)
            all_repayment_merchant_loan_disk['principal_paid'] = (all_repayment_merchant_loan_disk['principal_element_pct'] * all_repayment_merchant_loan_disk['cumulative_repayment']).round(2)
            all_repayment_merchant_loan_disk['principal_balance'] = (all_repayment_merchant_loan_disk['amount'] - all_repayment_merchant_loan_disk['principal_paid']).clip(lower=0)
            all_repayment_merchant_loan_disk['Bal_per_paid_date'] = all_repayment_merchant_loan_disk['expected_repayment'] - all_repayment_merchant_loan_disk['cumulative_repayment']

            # sort by dates
            summary_repayment_merchant_loan_disk = summary_repayment_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name'], ascending=True).reset_index()
            all_repayment_merchant_loan_disk = all_repayment_merchant_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()

            # Select and arrange summary columns
            summary_repayment_merchant_loan_disk = summary_repayment_merchant_loan_disk[[
                'date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'merchant_agents',
                'amount', 'principal_balance', 'expected_repayment', 'repayment_by_date', 'open_portfolio', 'missed_days',
                'missed_repayment', 'age_in_days', 'Duration', 'days_to_maturity', 'end_date'
            ]]

            # Select and arrange all repayment data columns
            all_repayment_merchant_loan_disk = all_repayment_merchant_loan_disk[[
                'date_disbursed', 'users_full_name', 'users_phone_number', 'merchant_agents', 'amount', 'principal_balance',
                'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio', 'missed_days', 'missed_repayment',
                'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date', 'age_in_days', 'Duration', 'days_to_maturity', 'end_date'
            ]]

            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "merchant_agents": "Merchant Officer",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment_by_date": "Paid Amount",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "paid_date": "Payment Date",
                "repayment": "Paid",
                "end_date": "Maturity Date"
            }

            summary_repayment_merchant_loan_disk.rename(columns=column_mappings, inplace=True)
            all_repayment_merchant_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Selectbox to get options ####################################################################################

            # Select box for Loan Status
            loan_category = st.selectbox("Loan Status", ["Open", "Completed"], placeholder="Select Merchant Status", index=None)

            # If 'Open Loans' is selected, show another dropdown for specific loan types
            if loan_category == "Open":
                open_loan_option = st.selectbox(
                    "Merchant Loan Sub-Status",
                    ["Past Maturity", "Missed Repayment", "Loan Repayment"],
                    placeholder="Select Merchant Loan Type",
                    index=None
                )

                if open_loan_option:
                    show_full_data = st.toggle("Show Expanded Dataset For Merchant")

                    if show_full_data:
                        if open_loan_option == "Past Maturity":
                            dataset = all_past_due_merchant_loan_disk.copy()
                        elif open_loan_option == "Missed Repayment":
                            dataset = all_missed_repay_merchant_loan_disk.copy()
                        elif open_loan_option == "Loan Repayment":
                            dataset = all_repayment_merchant_loan_disk.copy()

                        unique_users = pd.Series(dataset['Name'].unique()).sort_values()
                        selected_user = st.selectbox("Select User", ["All Users"] + list(unique_users), index=0)

                        if selected_user != "All Users":
                            dataset = dataset[dataset['Name'] == selected_user]

                            show_reappearing = st.toggle("Show Individual Transactions For Merchants (if merchant users has other loans)")

                            if show_reappearing:
                                dataset['Date'] = pd.to_datetime(dataset['Date'])
                                # dataset = dataset.sort_values(['Name', 'Date'])

                                # Identify separate transactions by checking when a user reappears after completing a previous transaction
                                dataset['Transaction_Group'] = (dataset['Name'] != dataset['Name'].shift()) | (dataset['Date'].diff().dt.days > 1)
                                dataset['Transaction_Group'] = dataset['Transaction_Group'].cumsum()

                                # Split dataset into separate DataFrames per transaction occurrence
                                for group_num, transaction_data in dataset.groupby("Transaction_Group"):
                                    st.write(f"### Transaction {group_num} for {selected_user}")
                                    st.write(transaction_data.drop(columns=['Transaction_Group']))
                            else:
                                st.write(f"### Transaction for {selected_user}")
                                st.write(dataset)
                        else:
                            st.write(f"### Transaction for {selected_user}")
                            st.write(dataset)
                    else:
                        st.write("### Summarized Dataset")
                        st.write(summary_past_mat_merchant_loan_disk if open_loan_option == "Past Maturity" else
                                summary_missed_repayment_merchant_loan_disk if open_loan_option == "Missed Repayment" else
                                summary_repayment_merchant_loan_disk)

            # If 'Completed Loans' is selected
            elif loan_category == "Completed":
                completed_loan_option = st.selectbox("Merchant Loan Sub-Status", ["Loans Completed"], placeholder="Select Merchant Loan Type", index=None)

                if completed_loan_option:
                    show_full_data = st.toggle("Show Expanded Dataset For Merchant")

                    if show_full_data:
                        dataset = all_completed_merchant_loan_disk.copy()
                        unique_users = pd.Series(dataset['Name'].unique()).sort_values()
                        selected_user = st.selectbox("Select User", ["All Users"] + list(unique_users), index=0)

                        if selected_user != "All Users":
                            dataset = dataset[dataset['Name'] == selected_user]

                            show_reappearing = st.toggle("Show Individual Transactions For Merchant (if user has other loans)")

                            if show_reappearing:
                                dataset['Date'] = pd.to_datetime(dataset['Date'])
                                # dataset = dataset.sort_values(['Name', 'Date'])
                                dataset['Transaction_Group'] = (dataset['Name'] != dataset['Name'].shift()) | (dataset['Date'].diff().dt.days > 1)
                                dataset['Transaction_Group'] = dataset['Transaction_Group'].cumsum()

                                for group_num, transaction_data in dataset.groupby("Transaction_Group"):
                                    st.write(f"### Completed Loan Transaction {group_num} for {selected_user}")
                                    st.write(transaction_data.drop(columns=['Transaction_Group']))
                            else:
                                st.write(f"### Completed Loan Transaction for {selected_user}")
                                st.write(dataset)
                        else:
                            st.write(f"### Completed Loan Transaction for {selected_user}")
                            st.write(dataset)
                    else:
                        st.write("### Summarized Dataset")
                        st.write(summary_completed_merchant_loan_disk)

    ################################################################################### Tab 6 #################################################################################

    with tab6:

    ######################################################################## Pharmarcies ####################################################################################

        st.markdown(
            """
            <style>
                .center-container {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 80vh;
                    flex-direction: column;
                }
                .center-container h2 {
                    font-size: 2.5rem;
                    color: #FF6F61;
                }
            </style>

            <div class="center-container">
                <img src="https://media.giphy.com/media/3o7TKtnuHOHHUjR38Y/giphy.gif" width="200">
                <p></p>
                <p>⚠️ Coming Soon... </p>
            </div>
            """,
            unsafe_allow_html=True
        )

    ################################################################################### Tab 7 #################################################################################

    with tab7:

    ######################################################################## Growth Rate/Financial Projection ####################################################################################

        # Copy dataset
        df_project = new_filtered_data.copy()
        repay_project = filtered_repayment_df.copy()
        past_mat_project = past_due_date.copy()

        # Check if any DataFrame is empty, if so, exit early
        if df_project.empty or repay_project.empty or past_mat_project.empty:
            st.warning("One or more datasets are empty. Skipping processing.")
        else:
            try:
                st.header('Select Forecast view')
                tab1, tab2 = st.tabs(["Manual Month Prediction", "Auto Yearly Prediction"])
                with tab1:
                    # Convert date columns to datetime
                    df_project["date_disbursed"] = pd.to_datetime(df_project["date_disbursed"], format="%b %Y")
                    repay_project["paid_date"] = pd.to_datetime(repay_project["paid_date"], format="%b %Y")
                    past_mat_project["date_disbursed"] = pd.to_datetime(past_mat_project["date_disbursed"], format="%b %Y")

                    # User selects the month to start forecasting from
                    available_months = sorted(df_project["date_disbursed"].dt.to_period("M").unique())
                    available_months_str = [str(m) for m in available_months][10:]

                    # Set default month for projection
                    default_month = "2024-12"
                    default_index = available_months_str.index(default_month) if default_month in available_months_str else len(available_months_str) - 1

                    selected_month_str = st.selectbox("Select Forecast Start Month (Default is Nov 2024):", available_months_str, index=default_index)
                    selected_month = pd.Period(selected_month_str, freq="M")

                    # Filter data to exclude selected forecast start month and beyond
                    df_filtered = df_project[df_project["date_disbursed"].dt.to_period("M") < selected_month]
                    repay_filtered = repay_project[repay_project["paid_date"].dt.to_period("M") < selected_month]
                    past_mat_filtered = past_mat_project[past_mat_project["date_disbursed"].dt.to_period("M") < selected_month]

                    # Group data by month
                    df_grouped = df_filtered.groupby(df_filtered["date_disbursed"].dt.to_period("M"))[["amount", "expected_repayment"]].sum().reset_index()
                    repay_grouped = repay_filtered.groupby(repay_filtered["paid_date"].dt.to_period("M"))["repayment_by_date"].sum().reset_index()
                    past_mat_grouped = past_mat_filtered.groupby(past_mat_filtered["date_disbursed"].dt.to_period("M"))["open_portfolio"].sum().reset_index()

                    # Merge data
                    df_final = (
                        df_grouped
                        .merge(repay_grouped, left_on='date_disbursed', right_on='paid_date', how="left")
                        .merge(past_mat_grouped, on='date_disbursed', how="left")
                        .reset_index()
                    )

                    # Fill missing values
                    df_final['repayment_by_date'] = df_final['repayment_by_date'].fillna(0)
                    df_final['open_portfolio'] = df_final['open_portfolio'].fillna(0)

                    # Compute portfolio outstanding
                    df_final['portfolio_outstanding'] = (df_final['expected_repayment'].fillna(0) - df_final['repayment_by_date']).round(2)

                    # Rename columns
                    df_final = df_final.rename(columns={
                        'open_portfolio': 'past_maturity',
                        'repayment_by_date': 'repayments'
                    })

                    # Convert period to string
                    df_final["date_disbursed"] = df_final["date_disbursed"].astype(str)

                    # LIVE DATA: Create full grouped live data (up to current date)
                    df_project_grouped = df_project.groupby(df_project["date_disbursed"].dt.to_period("M"))[["amount", "expected_repayment"]].sum().reset_index()
                    repay_grouped_live = repay_project.groupby(repay_project["paid_date"].dt.to_period("M"))["repayment_by_date"].sum().reset_index()
                    past_mat_grouped_live = past_mat_project.groupby(past_mat_project["date_disbursed"].dt.to_period("M"))["open_portfolio"].sum().reset_index()

                    # Merge for live data
                    df_live_grouped = (
                        df_project_grouped
                        .merge(repay_grouped_live, left_on='date_disbursed', right_on='paid_date', how="left")
                        .merge(past_mat_grouped_live, on='date_disbursed', how="left")
                        .reset_index(drop=True)
                    )

                    # Compute portfolio outstanding
                    df_live_grouped['portfolio_outstanding'] = (df_live_grouped['expected_repayment'].fillna(0) - df_live_grouped['repayment_by_date']).round(2)

                    df_live_grouped = df_live_grouped.rename(columns={
                        'open_portfolio': 'past_maturity',
                        'repayment_by_date': 'repayments'
                    })

                    df_live_grouped["date"] = df_live_grouped["date_disbursed"].astype(str)

                    # User input: Select prediction period (1 to 24 months, default is 6 months)
                    prediction_months = st.slider("Select Duration (in months):", min_value=1, max_value=24, value=12)

                    # Generate future months
                    historical_months = list(df_final["date_disbursed"])
                    future_months = pd.date_range(
                        start=pd.to_datetime(historical_months[-1]) + pd.DateOffset(months=1),
                        periods=prediction_months,
                        freq="M"
                    ).strftime("%Y-%m")

                    all_months = historical_months + list(future_months)

                    st.markdown("---")

                    # Toggle growth rate mode
                    growth_mode = st.toggle("Enable Growth Rate Projection")

                    # Growth rate slider (default 20%)
                    if growth_mode:
                        growth_rate = st.slider("Select Growth Rate (%):", min_value=0, max_value=100, value=20)
                        growth_factor = 1 + (growth_rate / 100)
                    else:
                        growth_factor = 1

                    st.markdown("---")

                    def plot_regression(df, y_column, title, df_live):
                        st.markdown(f"<h3 style='text-align: center;'>{title} Forecast</h3>", unsafe_allow_html=True)

                        x = np.arange(len(df))
                        y = df[y_column].values

                        coeffs = np.polyfit(x, y, deg=1)
                        poly_eq = np.poly1d(coeffs)
                        x_extended = np.arange(len(all_months))
                        y_pred = poly_eq(x_extended)

                        if growth_mode:
                            y_pred[len(historical_months):] *= growth_factor

                        equation_text = f"y = {coeffs[0]:.2e}x + {coeffs[1]:.2e}"
                        r_squared = 1 - np.sum((y - poly_eq(x)) ** 2) / np.sum((y - np.mean(y)) ** 2)

                        if not growth_mode:
                            st.markdown(f"🔹**Equation:** {equation_text}")
                            st.markdown(f"🔹**R² = {r_squared:.4f}**")

                        std_dev = np.std(y - poly_eq(x))
                        upper_bound = y_pred + std_dev
                        lower_bound = y_pred - std_dev

                        cumulative_y = np.cumsum(y)
                        projected_cumulative_y = np.cumsum(y_pred[len(historical_months):]) + cumulative_y[-1]

                        last_actual_cumulative = cumulative_y[-1]
                        projected_cumulative_upper = np.cumsum(upper_bound[len(historical_months):]) + last_actual_cumulative
                        projected_cumulative_lower = np.cumsum(lower_bound[len(historical_months):]) + last_actual_cumulative

                        # Prepare live data
                        if y_column in df_live.columns:
                            live_months = df_live["date"]
                            live_values = df_live[y_column]
                            live_cumulative = np.cumsum(live_values)
                        else:
                            live_months = []
                            live_values = []
                            live_cumulative = []

                        col1, col2 = st.columns(2)

                        with col1:
                            fig = go.Figure()
                            fig.add_trace(go.Scatter(x=historical_months, y=y, mode="markers+lines", name="Historical Data", marker=dict(color="green")))
                            fig.add_trace(go.Scatter(x=historical_months, y=poly_eq(x), mode="lines", name="Best Fit Line", line=dict(color="blue", dash="dash")))
                            fig.add_trace(go.Scatter(x=future_months, y=y_pred[len(historical_months):], mode="lines+markers", name="Projected Data", line=dict(color="red", dash="dot")))
                            fig.add_trace(go.Scatter(x=[historical_months[-1], future_months[0]], y=[y[-1], y_pred[len(historical_months)]], mode="lines", name="Transition Line", line=dict(color="yellow", dash="dot")))
                            fig.add_trace(go.Scatter(x=all_months, y=upper_bound, mode="lines", name="Upper Bound", line=dict(color="lightblue", dash="dot")))
                            fig.add_trace(go.Scatter(x=all_months, y=lower_bound, mode="lines", name="Lower Bound", line=dict(color="lightblue", dash="dot")))
                            fig.add_trace(go.Scatter(x=live_months, y=live_values, mode="lines+markers", name="Live Data", line=dict(color="green", dash="dash")))
                            fig.update_layout(title=f"{title} Monthly Chart", xaxis_title="Month(s)", yaxis_title="Amount", showlegend=True, paper_bgcolor="rgba(0,0,0,0)", plot_bgcolor="rgba(0,0,0,0)", modebar={"bgcolor": "rgba(0, 0, 0, 0)"})
                            st.plotly_chart(fig)

                        with col2:
                            fig_cumulative = go.Figure()
                            fig_cumulative.add_trace(go.Scatter(x=historical_months, y=cumulative_y, mode="lines+markers", name="Historical Cumulative", line=dict(color="green")))
                            fig_cumulative.add_trace(go.Scatter(x=[historical_months[-1], future_months[0]], y=[cumulative_y[-1], projected_cumulative_y[0]], mode="lines", name="Cumulative Transition Line", line=dict(color="red", dash="dot")))
                            fig_cumulative.add_trace(go.Scatter(x=[historical_months[-1], future_months[0]], y=[cumulative_y[-1], projected_cumulative_upper[0]], mode="lines", name="Upper Cumulative Transition", line=dict(color="lightblue", dash="dot")))
                            fig_cumulative.add_trace(go.Scatter(x=[historical_months[-1], future_months[0]], y=[cumulative_y[-1], projected_cumulative_lower[0]], mode="lines", name="Lower Cumulative Transition", line=dict(color="lightblue", dash="dot")))
                            fig_cumulative.add_trace(go.Scatter(x=future_months, y=projected_cumulative_y, mode="lines+markers", name="Projected Cumulative", line=dict(color="red", dash="dot")))
                            fig_cumulative.add_trace(go.Scatter(x=future_months, y=projected_cumulative_upper, mode="lines+markers", name="Upper Projected Cumulative Bound", line=dict(color="lightblue", dash="dot")))
                            fig_cumulative.add_trace(go.Scatter(x=future_months, y=projected_cumulative_lower, mode="lines+markers", name="Lower Projected Cumulative Bound", line=dict(color="lightblue", dash="dot")))
                            fig_cumulative.add_trace(go.Scatter(x=live_months, y=live_cumulative, mode="lines+markers", name="Live Cumulative", line=dict(color="green", dash="dash")))
                            fig_cumulative.update_layout(title=f"{title} Cumulative Chart", xaxis_title="Month(s)", yaxis_title="Cumulative Amount", showlegend=True, paper_bgcolor="rgba(0,0,0,0)", plot_bgcolor="rgba(0,0,0,0)", modebar={"bgcolor": "rgba(0, 0, 0, 0)"})
                            st.plotly_chart(fig_cumulative)

                        st.markdown("---")

                    # Apply regression to all metrics
                    plot_regression(df_final, "amount", "Loan Disbursement", df_live_grouped)
                    plot_regression(df_final, "repayments", "Loan Repayment", df_live_grouped)
                    plot_regression(df_final, "portfolio_outstanding", "Portfolio Outstanding", df_live_grouped)

                with tab2:
                    # Convert date columns to datetime
                    df_project["date_disbursed"] = pd.to_datetime(df_project["date_disbursed"], format="%b %Y")
                    repay_project["paid_date"] = pd.to_datetime(repay_project["paid_date"], format="%b %Y")
                    past_mat_project["date_disbursed"] = pd.to_datetime(past_mat_project["date_disbursed"], format="%b %Y")

                    # Get the latest month and filter out current month
                    latest_month = df_project["date_disbursed"].max().to_period("M")
                    repay_latest_month = repay_project["paid_date"].max().to_period("M")
                    past_mat_latest_month = past_mat_project["date_disbursed"].max().to_period("M")

                    df_filtered_ = df_project[df_project["date_disbursed"].dt.to_period("M") < latest_month]
                    repay_filtered_ = repay_project[repay_project["paid_date"].dt.to_period("M") < repay_latest_month]
                    past_mat_filtered_ = past_mat_project[past_mat_project["date_disbursed"].dt.to_period("M") < past_mat_latest_month]

                    # Group data by month
                    df_grouped_ = df_filtered_.groupby(df_filtered_["date_disbursed"].dt.to_period("M"))[["amount", "expected_repayment"]].sum().reset_index()
                    repay_grouped_ = repay_filtered_.groupby(repay_filtered_["paid_date"].dt.to_period("M"))[["repayment_by_date"]].sum().reset_index()
                    past_mat_grouped_ = past_mat_filtered_.groupby(past_mat_filtered_["date_disbursed"].dt.to_period("M"))[["open_portfolio"]].sum().reset_index()

                    # Merge data
                    df_final_ = (
                        df_grouped_
                        .merge(repay_grouped_, left_on='date_disbursed', right_on='paid_date', how="left")
                        .merge(past_mat_grouped_, on='date_disbursed', how="left")
                        .reset_index()
                    )

                    # Fill missing values
                    df_final_['repayment_by_date'] = df_final_['repayment_by_date'].fillna(0)
                    df_final_['open_portfolio'] = df_final_['open_portfolio'].fillna(0)

                    # Compute portfolio outstanding
                    df_final_['portfolio_outstanding'] = (df_final_['expected_repayment'].fillna(0) - df_final_['repayment_by_date']).round(2)

                    # Rename columns
                    df_final_ = df_final_.rename(columns={'open_portfolio': 'past_maturity', 'repayment_by_date': 'repayments'})

                    # Convert period to string
                    df_final_["date_disbursed"] = df_final_["date_disbursed"].astype(str)

                    # User input: Select prediction period (1 to 5 years, default is 2)
                    year_options = [12 * i for i in range(1, 6)]  # [12, 24, 36, 48, 60]
                    year_labels = [str(i) for i in range(1, 6)]   # ["1", "2", "3", "4", "5"]

                    # Set slider
                    selected_year_index = st.select_slider(
                        "Select Prediction Period (Years):",
                        options=list(range(1, 6)),  # 1 to 5 years
                        value=2,
                        format_func=lambda x: f"{x} year{'s' if x > 1 else ''}"
                    )

                    # Preductions on slider to years
                    prediction_months_ = selected_year_index * 12

                    # Generate future months
                    historical_months = list(df_final_["date_disbursed"])
                    future_months = pd.date_range(
                        start=pd.to_datetime(historical_months[-1]) + pd.DateOffset(months=1),
                        periods=prediction_months_,
                        freq="M"
                    ).strftime("%Y-%m")

                    all_months = historical_months + list(future_months)

                    st.markdown("---")

                    # Regression function
                    def plot_regression(df, y_column, title):
                        st.markdown(f"<h3 style='text-align: center;'>{title} Forecast</h3>", unsafe_allow_html=True)

                        # Prepare data
                        x = np.arange(len(df))
                        y = df[y_column].values

                        # Fit regression model
                        coeffs = np.polyfit(x, y, deg=1)
                        poly_eq = np.poly1d(coeffs)

                        # Generate predictions
                        x_extended = np.arange(len(all_months))
                        y_pred = poly_eq(x_extended)

                        # Generate equation
                        equation_text = f"y = {coeffs[0]:.2e}x + {coeffs[1]:.2e}"
                        r_squared = 1 - np.sum((y - poly_eq(x)) ** 2) / np.sum((y - np.mean(y)) ** 2)

                        # Display equation
                        st.markdown(f"🔹**Equation:** {equation_text}")
                        st.markdown(f"🔹**R² = {r_squared:.4f}**")

                        # Compute error bounds
                        std_dev = np.std(y - poly_eq(x))
                        upper_bound = y_pred + std_dev
                        lower_bound = y_pred - std_dev

                        # Compute cumulative values
                        cumulative_y = np.cumsum(y)
                        projected_cumulative_y = np.cumsum(y_pred[len(historical_months):]) + cumulative_y[-1]

                        # Compute cumulative upper and lower bounds from last actual data
                        last_actual_cumulative = cumulative_y[-1]
                        projected_cumulative_upper = np.cumsum(upper_bound[len(historical_months):]) + last_actual_cumulative
                        projected_cumulative_lower = np.cumsum(lower_bound[len(historical_months):]) + last_actual_cumulative

                        # Layout in two columns
                        col1, col2 = st.columns(2)

                        # Main plot
                        with col1:
                            fig = go.Figure()

                            fig.add_trace(go.Scatter(x=historical_months, y=y, mode="markers+lines", name="Historical Data", marker=dict(color="green")))
                            fig.add_trace(go.Scatter(x=historical_months, y=poly_eq(x), mode="lines", name="Best Fit Line", line=dict(color="blue", dash="dash")))
                            fig.add_trace(go.Scatter(x=future_months, y=y_pred[len(historical_months):], mode="lines+markers", name="Projected Data", line=dict(color="red", dash="dot")))
                            fig.add_trace(go.Scatter(x=[historical_months[-1], future_months[0]], y=[y[-1], y_pred[len(historical_months)]], mode="lines", name="Transition Line", line=dict(color="yellow", dash="dot")))
                            fig.add_trace(go.Scatter(x=all_months, y=upper_bound, mode="lines", name="Upper Bound", line=dict(color="lightblue", dash="dot")))
                            fig.add_trace(go.Scatter(x=all_months, y=lower_bound, mode="lines", name="Lower Bound", line=dict(color="lightblue", dash="dot")))

                            fig.update_layout(
                                title=f"{title} Yearly Chart",
                                xaxis_title="Year(s)",
                                yaxis_title="Amount",
                                showlegend=True,
                                paper_bgcolor="rgba(0, 0, 0, 0)",
                                plot_bgcolor="rgba(0, 0, 0, 0)",
                                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}
                            )

                            st.plotly_chart(fig)

                        # Cumulative Plot
                        with col2:
                            fig_cumulative = go.Figure()

                            # Historical Cumulative Data
                            fig_cumulative.add_trace(go.Scatter(
                                x=historical_months, y=cumulative_y,
                                mode="lines+markers",
                                name="Historical Cumulative",
                                line=dict(color="green")
                            ))

                            # Transition lines from last actual data to projected cumulative values
                            fig_cumulative.add_trace(go.Scatter(
                                x=[historical_months[-1], future_months[0]],
                                y=[cumulative_y[-1], projected_cumulative_y[0]],
                                mode="lines",
                                name="Cumulative Transition Line",
                                line=dict(color="red", dash="dot")
                            ))

                            fig_cumulative.add_trace(go.Scatter(
                                x=[historical_months[-1], future_months[0]],
                                y=[cumulative_y[-1], projected_cumulative_upper[0]],
                                mode="lines",
                                name="Upper Cumulative Transition",
                                line=dict(color="lightblue", dash="dot")
                            ))

                            fig_cumulative.add_trace(go.Scatter(
                                x=[historical_months[-1], future_months[0]],
                                y=[cumulative_y[-1], projected_cumulative_lower[0]],
                                mode="lines",
                                name="Lower Cumulative Transition",
                                line=dict(color="lightblue", dash="dot")
                            ))

                            # Projected Cumulative Data
                            fig_cumulative.add_trace(go.Scatter(
                                x=future_months, y=projected_cumulative_y,
                                mode="lines+markers",
                                name="Projected Cumulative",
                                line=dict(color="red", dash="dot")
                            ))

                            # Upper and Lower Cumulative Bounds
                            fig_cumulative.add_trace(go.Scatter(
                                x=future_months, y=projected_cumulative_upper,
                                mode="lines+markers",
                                name="Upper Projected Cumulative Bound",
                                line=dict(color="lightblue", dash="dot")
                            ))

                            fig_cumulative.add_trace(go.Scatter(
                                x=future_months, y=projected_cumulative_lower,
                                mode="lines+markers",
                                name="Lower Projected Cumulative Bound",
                                line=dict(color="lightblue", dash="dot")
                            ))

                            # Layout settings
                            fig_cumulative.update_layout(
                                title=f"{title} Cumulative Chart",
                                xaxis_title="Year(s)",
                                yaxis_title="Cumulative Amount",
                                showlegend=True,
                                paper_bgcolor="rgba(0, 0, 0, 0)",
                                plot_bgcolor="rgba(0, 0, 0, 0)",
                                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}
                            )

                            st.plotly_chart(fig_cumulative)

                        st.markdown("---")

                    # Apply regression to all metrics
                    plot_regression(df_final_, "amount", "Loan Disbursement ")
                    plot_regression(df_final_, "repayments", "Loan Repayment ")
                    plot_regression(df_final_, "portfolio_outstanding", "Portfolio Outstanding ")
                pass
            except (ValueError, IndexError, KeyError, AttributeError):
                st.warning("Not enough dataset to load this, Kindly skip.")

    ############################################################################### Tab 8 #################################################################################

    with tab8:

        ######################################################################################## Repayment rates ##############################################################################

        @st.cache_data(show_spinner=False)
        def process_repayment_data(df, repay_df):
            # Convert 'date_disbursed' to datetime and extract the month
            df['date_disbursed'] = pd.to_datetime(df['date_disbursed'])
            df['actual_month'] = df['date_disbursed'].dt.to_period('M').dt.to_timestamp()

            # Copy df and get agent_full_name + metadata
            df_ = df[['agent_full_name', 'agent_id', 'verticals', 'sup_name']].drop_duplicates().reset_index(drop=True)

            # Group by agent and actual_month, summing repayment values
            repayment_by_month = (
                df.groupby(['agent_id', 'actual_month'])[['expected_repayment', 'backend_repayment']]
                .sum()
                .reset_index()
                .sort_values(by=['agent_id', 'actual_month'])
            )

            # Calculate cumulative expected and backend repayments
            repayment_by_month['cumulative_expected'] = repayment_by_month.groupby('agent_id')['expected_repayment'].cumsum()
            repayment_by_month['cumulative_backend'] = repayment_by_month.groupby('agent_id')['backend_repayment'].cumsum()

            # Process repay_df similarly
            repay_df = repay_df[['agent_id', 'paid_date', 'repayment_by_date']]
            repay_df['paid_date'] = pd.to_datetime(repay_df['paid_date'])
            repay_df['actual_month'] = repay_df['paid_date'].dt.to_period('M').dt.to_timestamp()

            repay_df = (
                repay_df.groupby(['agent_id', 'actual_month'])['repayment_by_date']
                .sum()
                .reset_index()
                .sort_values(by=['agent_id', 'actual_month'])
            )
            repay_df['cumulative_repayment'] = repay_df.groupby('agent_id')['repayment_by_date'].cumsum()

            # Merge the two DataFrames
            all_df = repay_df.merge(repayment_by_month, how='outer', on=['agent_id', 'actual_month']).fillna(0)

            # Recalculate cumulative values
            all_df['cumulative_repayment'] = all_df.groupby('agent_id')['repayment_by_date'].cumsum()
            all_df['cumulative_expected'] = all_df.groupby('agent_id')['expected_repayment'].cumsum()
            all_df['cumulative_backend'] = all_df.groupby('agent_id')['backend_repayment'].cumsum()

            # Repayment rates
            all_df['repayment_rate'] = (all_df['cumulative_repayment'] / all_df['cumulative_expected'] * 100).round(2)
            all_df['backend_rate'] = (all_df['cumulative_repayment'] / all_df['cumulative_backend'] * 100).round(2)

            # Merge metadata
            all_df = df_.merge(all_df, how='right', on='agent_id')

            # Add month label
            all_df['month_sequence'] = all_df.groupby('agent_id')['actual_month'].rank(method='dense').astype(int)
            all_df['month'] = 'Month ' + all_df['month_sequence'].astype(str)

            # Reorder
            all_df = all_df[[
                'agent_id', 'agent_full_name', 'verticals', 'sup_name',
                'actual_month', 'month', 'month_sequence',
                'repayment_by_date', 'expected_repayment', 'backend_repayment',
                'cumulative_repayment', 'cumulative_expected', 'cumulative_backend',
                'repayment_rate', 'backend_rate'
            ]]

            return all_df

        # Process data
        agent_rates_df = process_repayment_data(filtered_data, filtered_repayment_df)

        # Title
        st.markdown(
            """
            <div style='text-align: center;'>
                <h2>Monthly Repayment Rates (Agent-wise)</h2>
            </div>
            """,
            unsafe_allow_html=True
        )

        # Unique selections
        unique_agents = sorted(agent_rates_df['agent_full_name'].dropna().unique())
        unique_verticals = sorted(agent_rates_df['verticals'].dropna().unique())
        unique_sup_names = sorted(agent_rates_df['sup_name'].dropna().unique())

        col1, col2, col3 = st.columns(3)
        # Multiselect filters
        with col1:
            selected_verticals = st.multiselect("Filter by verticals", unique_verticals, placeholder='Select one or more Verticals...')
        with col2:
            selected_supervisors = st.multiselect("Filter by Supervisor", unique_sup_names, placeholder='Select one or more Supervisors...')
        with col3:
            selected_agents = st.multiselect("Filter by Loan Officer", unique_agents, placeholder='Select one or more Loan Officers...')

        # Check if no filters selected
        if not selected_verticals and not selected_supervisors and not selected_agents:
            st.info("Please select at least one loan officer to view the repayment rates.")
        else:
            # Apply filters
            plot_data = agent_rates_df.copy()
            if selected_verticals:
                plot_data = plot_data[plot_data['verticals'].isin(selected_verticals)]
            if selected_supervisors:
                plot_data = plot_data[plot_data['sup_name'].isin(selected_supervisors)]
            if selected_agents:
                plot_data = plot_data[plot_data['agent_full_name'].isin(selected_agents)]

            # Display results
            if not plot_data.empty:
                col1, col2 = st.columns(2)
                with col1:
                    fig = px.line(
                        plot_data,
                        x='month',
                        y='repayment_rate',
                        color='agent_full_name',
                        markers=True,
                        title='Repayment Rate Progress',
                        labels={
                            'month': 'Month',
                            'repayment_rate': 'Repayment Rate (%)',
                            'agent_full_name': 'Loan Officer'
                        }
                    )
                    fig.update_yaxes(range=[0, 100])
                    fig.add_hline(y=100, line_dash="dot", line_color="red", opacity=0.5)
                    fig.update_layout(
                        height=500,
                        showlegend=True,
                        paper_bgcolor="rgba(0, 0, 0, 0)",
                        plot_bgcolor="rgba(0, 0, 0, 0)",
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"},
                        hovermode="x unified"
                    )
                    fig.update_traces(marker=dict(size=8))
                    st.plotly_chart(fig, use_container_width=True)

                with col2:
                    fig = px.line(
                        plot_data,
                        x='month',
                        y='backend_rate',
                        color='agent_full_name',
                        markers=True,
                        title='Backend Rate Progress',
                        labels={
                            'month': 'Month',
                            'backend_rate': 'Backend Repayment Rate (%)',
                            'agent_full_name': 'Loan Officer'
                        }
                    )
                    fig.update_yaxes(range=[0, 100])
                    fig.add_hline(y=100, line_dash="dot", line_color="red", opacity=0.5)
                    fig.update_layout(
                        height=500,
                        showlegend=True,
                        paper_bgcolor="rgba(0, 0, 0, 0)",
                        plot_bgcolor="rgba(0, 0, 0, 0)",
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"},
                        hovermode="x unified"
                    )
                    fig.update_traces(marker=dict(size=8))
                    st.plotly_chart(fig, use_container_width=True)

                with st.expander("Selected Repayment Rate Data"):
                    st.dataframe(plot_data)
                    st.success(f"Displaying {len(plot_data)} records.")
            else:
                st.info("No data matches the selected filters. Please adjust your selection.")

        st.markdown('---')

    ######################################################################### More customization using div class ####################################################################################

        # Copy dataframes
        savings_and_loans = staff_savings_trans.copy()
        login_count = loginactivity.copy()
        supervisor_agent_andbranch = sup_df.copy()
        open_loans = filtered_data.copy()

        # Group and get monthly expected repayments by agents
        monthly_expected_repayments = filtered_data.groupby(['agent_id', 'month'])['exp_repay_today'].sum().reset_index()
        # Calculate monthly repayment percentage
        monthly_expected_repayments['month'] = monthly_expected_repayments['month'].str.strip() #remove leading and trailling white spaces
        monthly_repayments['month'] = monthly_repayments['month'].str.strip()

        # Merge repayments and expected repayments to 1 df
        repayments_and_expected = monthly_expected_repayments.merge(monthly_repayments,
                                                                    on=['agent_id', 'month'])

        # Calculate repayment percentage
        repayments_and_expected['repayment_rate'] = round((repayments_and_expected['total_repayment_amount']/repayments_and_expected['exp_repay_today'])*100,2)
        repayments_and_expected.replace(np.inf, np.nan, inplace=True)
        avg_repayment_pct = repayments_and_expected.groupby('agent_id')[['repayment_rate']].mean().round(2).reset_index()

        merged_agent_perf = avg_repayment_pct.merge(disbursement_4_last_month, on = 'agent_id', how = 'outer')

        # Repayment commission
        repayment_commission = []
        for index, row in merged_agent_perf.iterrows():
            if row['repayment_rate'] >= 80 and row['repayment_rate'] < 85:
                commission = (0.5/100) * row['last_month']
                repayment_commission.append(commission)

            elif row['repayment_rate'] >= 85:
                commission = (1/100) * row['last_month']
                repayment_commission.append(commission)
            else:
                repayment_commission.append(0)
        merged_agent_perf['repayment_commission'] = repayment_commission

        # Merge datasets
        savings_loans_and_login =  staff_savings_trans.merge(login_count, on = 'email', how = 'left')
        # Add earnings
        plus_earnings = savings_loans_and_login.merge(earnings, on = 'user_id', how='left')
        # Merge datasets
        savings_loans_and_activity = plus_earnings.merge(supervisor_agent_andbranch, left_on='customer_user_id', right_on = 'team_user_id', how='right')
        # Fills 'NaN' with 'Null'
        savings_loans_and_activity[['sup_name', 'location', 'agent_name', 'email']].fillna('Null', inplace=True)
        # Fills 'NaN' with 0
        savings_loans_and_activity.fillna(0, inplace=True)
        # Change datayepe from float to int
        savings_loans_and_activity[["agent_age_days","savings","disbursed","trans_count","login_count"]] = savings_loans_and_activity[["agent_age_days","savings","disbursed","trans_count","login_count"]].astype("int")
        # Set activity tag
        savings_loans_and_activity['activity'] = np.where(
            (savings_loans_and_activity['agent_age_days'] > 30) & (savings_loans_and_activity['login_count'] > 160),
            'active_old',
            np.where(savings_loans_and_activity['agent_age_days'] <= 30,
                        np.where(savings_loans_and_activity['login_count'] > 80, 'active_new', 'inactive_new'),
                        'inactive_old'))

        # CHURN
        savings_loans_and_activity['churned'] = np.where(
            (savings_loans_and_activity['agent_age_days'] > 30)
            & (savings_loans_and_activity['activity'].isin(['inactive_new','inactive_old']))
            & (savings_loans_and_activity['savings'] == 0)
            & (savings_loans_and_activity['login_count'] < 38),'churned','not churned')

        avg_weekly_savings = 750_000
        # avg_weekly_trnx_count = 38
        avg_Weekly_loan	= 3_000_000

        # Loan and savings performance
        savings_loans_and_activity['savings_performance'] = (round((savings_loans_and_activity['savings']/avg_weekly_savings),3)*100).astype('str')+'%'
        savings_loans_and_activity['loans_performance'] = (round((savings_loans_and_activity['disbursed']/avg_Weekly_loan),3)*100).astype('str')+'%'

        # Cap performance to ensure it  dosent affect the overall
        savings_loans_and_activity['savings_performance_num'] = pd.to_numeric(savings_loans_and_activity['savings_performance'].str.strip('%'))
        savings_loans_and_activity['savings_performance_capped'] = ['100%' if x > 100 else str(x)+'%' for x in savings_loans_and_activity['savings_performance_num']]
        savings_loans_and_activity['loans_performance_num'] = pd.to_numeric(savings_loans_and_activity['loans_performance'].str.strip('%'))
        savings_loans_and_activity['loans_performance_capped'] = ['100%' if x > 100 else str(x)+'%' for x in savings_loans_and_activity['loans_performance_num']]

        savings_loans_and_activity['overall'] = round(
            (pd.to_numeric(savings_loans_and_activity['savings_performance_capped'].str.strip('%')) +
                pd.to_numeric(savings_loans_and_activity['loans_performance_capped'].str.strip('%'))) / 2, 3
        ).astype('str') + '%'

        # ADD BASE PAY
        base_pay = 80_000
        savings_loans_and_activity['base_pay'] = base_pay
        savings_loans_and_activity['total_earnings'] = base_pay + savings_loans_and_activity['earnings']

        merged_agent_perf = merged_agent_perf[['agent_id','repayment_rate','last_month','repayment_commission']]
        savings_loans_and_activity = savings_loans_and_activity.merge(merged_agent_perf, left_on='user_id', right_on='agent_id', how = 'left')

        performance_report = savings_loans_and_activity[
            ['sup_name', 'location', 'agent_name', 'email','agent_age_days',
                'savings','savings_performance','savings_performance_capped','trans_count',
                'disbursed','loans_performance','loans_performance_capped','repayment_rate','last_month','repayment_commission','earnings','base_pay',
                'total_earnings','interest','login_count','churned','activity','overall']]

        # Title-case specific string columns
        for col in ['sup_name', 'sub_location', 'agent_name']:
            if col in performance_report.columns:
                performance_report[col] = performance_report[col].astype(str).str.title()

        performance_report['status'] = performance_report['activity'].apply(
            lambda x: 'Inactive' if 'inactive' in x else 'Active'
        )

        report = performance_report.merge(filtered_data, how='right', left_on='agent_name', right_on='agent_full_name')
        # Active agents (those with 'active_old' OR 'active_new')
        active_agent = report[
            (report['activity'] == 'active_old') | (report['activity'] == 'active_new')
        ]

        # Inactive agents (those with 'inactive_old' OR 'inactive_new')
        inactive_agent = report[
            (report['activity'] == 'inactive_old') | (report['activity'] == 'inactive_new')
        ]

    ######################################################################################## Targets ##############################################################################

        try:
            T1, T2, T3 = st.tabs(['Loan Disbursement Target', 'Loan Repayment Target', 'Backend Repayment Target'])

            with T1:
                # Copy dataset
                quarterly_sum = new_filtered_data.copy()

                # Set quarter targets for each year
                quarter_targets = {
                    ('2024', 1): 6_000_000, ('2024', 2): 100_000_000, ('2024', 3): 344_000_000, ('2024', 4): 550_000_000,
                    ('2025', 1): 1_000_000_000, ('2025', 2): 1_000_000_000, ('2025', 3): 2_250_000_000, ('2025', 4): 2_250_000_000,
                }
                default_quarter_target = 5_000_000_000

                # Monthly allocation within each quarter
                quarter_month_percentages = {
                    1: [0.3333333333333333, 0.3333333333333333, 0.3333333333333333],
                    2: [0.3333333333333333, 0.3333333333333333, 0.3333333333333333],
                    3: [0.3333333333333333, 0.3333333333333333, 0.3333333333333333],
                    4: [0.3333333333333333, 0.3333333333333333, 0.3333333333333333],
                }

                # Set quarters
                def get_quarter(month):
                    return (month - 1) // 3 + 1

                # Add temporal columns
                quarterly_sum['Year'] = quarterly_sum['date_disbursed'].dt.year.astype(str)
                quarterly_sum['Month'] = quarterly_sum['date_disbursed'].dt.month
                quarterly_sum['Quarter'] = quarterly_sum['Month'].apply(get_quarter)
                quarterly_sum['month_period'] = quarterly_sum['date_disbursed'].dt.to_period('M')
                quarterly_sum['quarter_period'] = quarterly_sum['date_disbursed'].dt.to_period('Q')
                quarterly_sum['year_period'] = quarterly_sum['date_disbursed'].dt.to_period('Y')
                quarterly_sum['week_start'] = quarterly_sum['date_disbursed'] - pd.to_timedelta(quarterly_sum['date_disbursed'].dt.weekday, unit='d')
                quarterly_sum['week_of_month'] = ((quarterly_sum['date_disbursed'].dt.day - 1) // 7 + 1)

                # Extract available years from the data
                available_years = sorted(quarterly_sum['Year'].unique())

                # Get the current year (default value)
                current_year = str(pd.to_datetime('today').year)

                y1, y2, y3 = st.columns([1,2,1])
                with y2:
                    year_option = st.selectbox("Select Disbursement Year", available_years, index=available_years.index(current_year))

                # Filter data based on the selected year
                filtered_year_data = quarterly_sum[quarterly_sum['Year'] == year_option]

                # Set monthly aggregation
                monthly_grouped = filtered_year_data.groupby('month_period').agg(
                    actual=('amount', 'sum'),
                    year=('Year', 'first'),
                    month=('Month', 'first')
                ).reset_index()
                monthly_grouped['quarter'] = monthly_grouped['month'].apply(get_quarter)
                monthly_grouped['quarter_key'] = list(zip(monthly_grouped['year'], monthly_grouped['quarter']))
                monthly_grouped['month_idx_in_qtr'] = monthly_grouped['month'].map({
                    1: 0, 2: 1, 3: 2,
                    4: 0, 5: 1, 6: 2,
                    7: 0, 8: 1, 9: 2,
                    10: 0, 11: 1, 12: 2
                })
                monthly_grouped['monthly_target'] = monthly_grouped.apply(
                    lambda row: quarter_targets.get(row['quarter_key'], default_quarter_target) *
                                quarter_month_percentages[row['quarter']][row['month_idx_in_qtr']],
                    axis=1
                )

                # Set weekly aggregation
                weekly_grouped = filtered_year_data.groupby('week_start').agg(
                    actual=('amount', 'sum'),
                    year=('Year', 'first'),
                    month=('Month', 'first'),
                    week_of_month=('week_of_month', 'first')
                ).reset_index()
                weekly_grouped['month_period'] = weekly_grouped['week_start'].dt.to_period('M')
                weekly_grouped = weekly_grouped.merge(monthly_grouped[['month_period', 'monthly_target']], on='month_period', how='left')
                weekly_grouped['weekly_target'] = weekly_grouped['monthly_target'] * 0.25  # Equal 4-week split

                # Set quarterly aggregation
                quarterly_grouped = filtered_year_data.groupby('quarter_period').agg(
                    actual=('amount', 'sum')
                ).reset_index()
                quarterly_grouped['year'] = quarterly_grouped['quarter_period'].astype(str).str[:4]
                quarterly_grouped['quarter'] = quarterly_grouped['quarter_period'].astype(str).str[-1].astype(int)
                quarterly_grouped['quarter_key'] = list(zip(quarterly_grouped['year'], quarterly_grouped['quarter']))
                quarterly_grouped['target'] = quarterly_grouped['quarter_key'].apply(
                    lambda key: quarter_targets.get(key, default_quarter_target)
                )

                # Set yearly aggregation
                yearly_grouped = quarterly_sum.groupby('year_period').agg(
                    actual=('amount', 'sum')
                ).reset_index()
                yearly_grouped['year'] = yearly_grouped['year_period'].astype(str)
                yearly_grouped['target'] = yearly_grouped['year'].apply(
                    lambda y: sum(v for (yr, _), v in quarter_targets.items() if yr == y)
                    if any(yr == y for (yr, _) in quarter_targets)
                    else default_quarter_target * 4
                )

                v1, v2, v3 = st.columns([1,2,1])
                with v2:
                    view_option = st.selectbox("Select Disbursement View", ['Weekly', 'Monthly', 'Quarterly', 'Yearly'], index=1)

                # Get the target for the selected year using quarter_targets
                # Sum up the targets for all quarters in the selected year
                year_target = sum(
                    value for (year, _), value in quarter_targets.items() if year == year_option
                )

                # If no target is found for the year, use default_quarter_target * 4 (4 quarters in a year)
                if year_target == 0:
                    year_target = default_quarter_target * 4

                # Calculate cumulative sums and overall percentage
                total_actual = filtered_year_data['amount'].sum()

                # Calculate the percentage progress
                total_percentage = (total_actual / year_target) * 100 if year_target != 0 else 0

                st.write("")
                # Display Cumulative Information before the plot
                st.markdown(f"""
                    <div style="text-align: center;">
                        <h3>📊 {year_option} Disbursement Cumulative Summary</h3>
                    </div>
                """, unsafe_allow_html=True)
                st.write("")
                st.markdown(f"""
                    <div style="display: flex; justify-content: center; gap: 30px;">
                        <div style="text-align: center;">
                            <h5>🎯 Total Loan Target</h5>
                            <p style="font-size: 20px;">₦{year_target:,.0f}</p>
                        </div>
                        <div style="text-align: center;">
                            <h5>💰 Total Loan Disbursed</h5>
                            <p style="font-size: 20px;">₦{total_actual:,.0f}</p>
                        </div>
                        <div style="text-align: center;">
                            <h5>📈 Percentage Achieved</h5>
                            <p style="font-size: 20px;">{total_percentage:,.2f}%</p>
                        </div>
                    </div>
                """, unsafe_allow_html=True)
                st.write("")

                def show_progress_bar_chart(df, label_col, actual_col, target_col, title):
                    df.sort_values(label_col, inplace=True)

                    # Calculate percentage progress
                    df['progress_percent'] = df[actual_col] / df[target_col]

                    # Assign colors based on progress percentage
                    df['color'] = df['progress_percent'].apply(
                        lambda x: 'green' if x >= 1 else ('orange' if x >= 0.7 else 'red')
                    )

                    # Create dummy traces for the legend
                    legend_colors = {
                        'Completed ✅': 'green',
                        'Almost There 🚀': 'orange',
                        'Below Target 🔻': 'red'
                    }

                    fig = go.Figure()

                    # Add the progress bars (Actual relative to Target)
                    fig.add_trace(go.Bar(
                        x=df[label_col].astype(str),
                        y=df['progress_percent'],
                        name='Progress',
                        marker_color=df['color'],  # Use the color column for bar coloring
                        hovertemplate=(  # Add custom hover template
                            'Actual Amount: %{customdata[0]:,.2f}<br>'
                            'Target: %{customdata[1]:,.2f}<br>'
                            'Progress Rate: %{y:.2%}<br>'
                            '<extra></extra>'
                        ),
                        customdata=df[[actual_col, target_col]],  # Add actual amount, target, and date period to customdata
                        showlegend=False  # Disable legend for this trace
                    ))

                    # Add dummy traces for the legend
                    for label, color in legend_colors.items():
                        fig.add_trace(go.Scatter(
                            x=[None], y=[None],
                            mode='markers',
                            marker=dict(color=color, symbol='square', size=10),
                            name=label
                        ))

                    # Set X-axis ticks to show all values
                    fig.update_layout(
                        title=title,
                        xaxis_title=label_col.replace('_', ' ').title(),
                        yaxis_title='Progress (%)',
                        barmode='stack',
                        height=500,
                        yaxis=dict(tickformat='.0%', range=[0, 1]),
                        xaxis=dict(
                            tickmode='array',
                            tickvals=df[label_col].astype(str),  # Show all the X-axis values
                            ticktext=df[label_col].astype(str),  # Label each tick with the corresponding value
                        ),
                        template='plotly_dark',
                        showlegend=True,
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"},
                    )

                    st.plotly_chart(fig, use_container_width=True)

                # RENDER BASED ON SELECTION
                if view_option == 'Weekly':
                    show_progress_bar_chart(weekly_grouped, 'week_start', 'actual', 'weekly_target', 'Weekly Disbursement Progress')
                elif view_option == 'Monthly':
                    show_progress_bar_chart(monthly_grouped, 'month_period', 'actual', 'monthly_target', 'Monthly Disbursement Progress')
                elif view_option == 'Quarterly':
                    show_progress_bar_chart(quarterly_grouped, 'quarter_period', 'actual', 'target', 'Quarterly Disbursement Progress')
                else:
                    show_progress_bar_chart(yearly_grouped, 'year_period', 'actual', 'target', 'Yearly Disbursement Progress')

            with T2:

                # Year selection
                current_year = datetime.now().year
                year_list = sorted(schedule_df['date'].dt.year.unique(), reverse=True)
                y1,y2,y3 = st.columns([1,2,1])
                with y2:
                    selected_year = st.selectbox("Select Year", year_list, index=year_list.index(current_year))

                p1,p2,p3 = st.columns([1,2,1])
                with p2:
                    period_option = st.selectbox("Select Repayment Period", ["Weekly", "Monthly", "Quarterly", "Yearly"], index=1)

                # Aggregate expected repayment
                weekly_exp = schedule_df.groupby(pd.Grouper(key='date', freq='W')).agg({'exp_repay_today': 'sum'}).reset_index()
                monthly_exp = schedule_df.groupby(pd.Grouper(key='date', freq='M')).agg({'exp_repay_today': 'sum'}).reset_index()
                quarterly_exp = schedule_df.groupby(pd.Grouper(key='date', freq='Q')).agg({'exp_repay_today': 'sum'}).reset_index()
                yearly_exp = schedule_df.groupby(pd.Grouper(key='date', freq='Y')).agg({'exp_repay_today': 'sum'}).reset_index()

                # Aggregate actual repayment
                weekly_act = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='W')).agg({'repayment_by_date': 'sum'}).reset_index()
                monthly_act = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='M')).agg({'repayment_by_date': 'sum'}).reset_index()
                quarterly_act = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='Q')).agg({'repayment_by_date': 'sum'}).reset_index()
                yearly_act = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='Y')).agg({'repayment_by_date': 'sum'}).reset_index()

                # Calculate the total loan target and actual repayment for cumulative summary
                year_target = yearly_exp[yearly_exp['date'].dt.year == selected_year]['exp_repay_today'].sum()
                total_actual = yearly_act[yearly_act['paid_date'].dt.year == selected_year]['repayment_by_date'].sum()
                total_percentage = (total_actual / year_target) * 100 if year_target > 0 else 0

                # Display cumulative summary before the plot
                st.write("")
                st.markdown(f"""
                    <div style="text-align: center;">
                        <h3>📊 {selected_year} Expected Repayment Cumulative Summary</h3>
                    </div>
                """, unsafe_allow_html=True)
                st.write("")
                st.markdown(f"""
                    <div style="display: flex; justify-content: center; gap: 30px;">
                        <div style="text-align: center;">
                            <h5>🎯 Total Expected Repayment Target</h5>
                            <p style="font-size: 20px;">₦{year_target:,.0f}</p>
                        </div>
                        <div style="text-align: center;">
                            <h5>💰 Total Repayment Paid</h5>
                            <p style="font-size: 20px;">₦{total_actual:,.0f}</p>
                        </div>
                        <div style="text-align: center;">
                            <h5>📈 Collection Percentage Achieved</h5>
                            <p style="font-size: 20px;">{total_percentage:,.2f}%</p>
                        </div>
                    </div>
                """, unsafe_allow_html=True)
                st.write("")

                # Plotting function
                def plot_progress_bar_chart(expected_df, actual_df, period_label):
                    df = pd.merge(expected_df, actual_df, left_on='date', right_on='paid_date', how='outer').fillna(0)
                    df['date'] = df['date'].combine_first(df['paid_date'])  # ensure date exists

                    # If expected is 0 and actual > 0, treat rate as 100%
                    df['progress_percent'] = df.apply(
                        lambda row: 1 if row['exp_repay_today'] == 0 and row['repayment_by_date'] > 0
                        else (row['repayment_by_date'] / row['exp_repay_today'] if row['exp_repay_today'] > 0 else 0),
                        axis=1
                    ).round(2)

                    # Assign colors
                    df['color'] = df['progress_percent'].apply(
                        lambda x: 'green' if x >= 1 else ('orange' if x >= 0.7 else 'red')
                    )

                    # Format fields
                    df['Expected Amount'] = df['exp_repay_today'].apply(lambda x: f"₦{x:,.2f}")
                    df['Actual Amount'] = df['repayment_by_date'].apply(lambda x: f"₦{x:,.2f}")
                    df['Repayment Rate (%)'] = (df['progress_percent'] * 100).round(2)

                    df_display = df[['date', 'Expected Amount', 'Actual Amount', 'Repayment Rate (%)']].rename(columns={'date': 'Period'})

                    # Plotting
                    fig = go.Figure()

                    for _, row in df.iterrows():
                        fig.add_trace(go.Bar(
                            x=[row['date']],
                            y=[row['progress_percent'] * 100],
                            marker_color=row['color'],
                            hovertemplate=(
                                f"<b>Period:</b> {row['date'].strftime('%Y-%m-%d')}<br>" +
                                f"<b>Expected:</b> {row['Expected Amount']}<br>" +
                                f"<b>Actual:</b> {row['Actual Amount']}<br>" +
                                f"<b>Progress:</b> {row['Repayment Rate (%)']}%"
                            ),
                            showlegend=False
                        ))

                    # Legend
                    legend_colors = {
                        'Completed ✅': 'green',
                        'Almost There 🚀': 'orange',
                        'Below Target 🔻': 'red'
                    }

                    for name, color in legend_colors.items():
                        fig.add_trace(go.Bar(
                            x=[None], y=[None],
                            marker_color=color,
                            name=name,
                            showlegend=True
                        ))

                    fig.update_layout(
                        title=f"{selected_year} {period_label} Repayment Progress",
                        xaxis_title="Period",
                        yaxis_title="Repayment Rate (%)",
                        yaxis=dict(range=[0, 100]),
                        xaxis=dict(
                            tickmode='array',
                            tickvals=df['date'],
                            ticktext=[d.strftime('%Y-%m-%d') for d in df['date']],
                            tickangle=-45
                        ),
                        barmode='group',
                        legend_title="Performance",
                        height=500,
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig)

                # Apply year filter for all periods except yearly
                if period_option == "Weekly":
                    exp = weekly_exp[weekly_exp['date'].dt.year == selected_year]
                    act = weekly_act[weekly_act['paid_date'].dt.year == selected_year]
                    plot_progress_bar_chart(exp, act, "Weekly")

                elif period_option == "Monthly":
                    exp = monthly_exp[monthly_exp['date'].dt.year == selected_year]
                    act = monthly_act[monthly_act['paid_date'].dt.year == selected_year]
                    plot_progress_bar_chart(exp, act, "Monthly")

                elif period_option == "Quarterly":
                    exp = quarterly_exp[quarterly_exp['date'].dt.year == selected_year]
                    act = quarterly_act[quarterly_act['paid_date'].dt.year == selected_year]
                    plot_progress_bar_chart(exp, act, "Quarterly")

                elif period_option == "Yearly":
                    plot_progress_bar_chart(yearly_exp, yearly_act, "Yearly")

            with T3:

                # Year selection
                current_year = datetime.now().year
                year_list = sorted(backend_schedule_df['date'].dt.year.unique(), reverse=True)
                y1,y2,y3 = st.columns([1,2,1])
                with y2:
                    selected_year = st.selectbox("Select Year ", year_list, index=year_list.index(current_year))

                p1,p2,p3 = st.columns([1,2,1])
                with p2:
                    period_option = st.selectbox("Select Backend Repayment Period", ["Weekly", "Monthly", "Quarterly", "Yearly"], index=1)

                # Aggregate expected repayment
                backend_weekly_exp_repay = backend_schedule_df.groupby(pd.Grouper(key='date', freq='W')).agg({'backend_exp_repay_today': 'sum'}).reset_index()
                backend_monthly_exp_repay = backend_schedule_df.groupby(pd.Grouper(key='date', freq='M')).agg({'backend_exp_repay_today': 'sum'}).reset_index()
                backend_quarterly_exp_repay = backend_schedule_df.groupby(pd.Grouper(key='date', freq='Q')).agg({'backend_exp_repay_today': 'sum'}).reset_index()
                backend_yearly_exp_repay = backend_schedule_df.groupby(pd.Grouper(key='date', freq='Y')).agg({'backend_exp_repay_today': 'sum'}).reset_index()

                # Aggregate actual repayment
                weekly_repay = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='W')).agg({'repayment_by_date': 'sum'}).reset_index()
                monthly_repay = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='M')).agg({'repayment_by_date': 'sum'}).reset_index()
                quarterly_repay = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='Q')).agg({'repayment_by_date': 'sum'}).reset_index()
                yearly_repay = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='Y')).agg({'repayment_by_date': 'sum'}).reset_index()

                # Calculate the total loan target and actual repayment for cumulative summary
                year_target = backend_yearly_exp_repay[backend_yearly_exp_repay['date'].dt.year == selected_year]['backend_exp_repay_today'].sum()
                total_actual = yearly_repay[yearly_repay['paid_date'].dt.year == selected_year]['repayment_by_date'].sum()
                total_percentage = (total_actual / year_target) * 100 if year_target > 0 else 0

                # Display cumulative summary before the plot
                st.write("")
                st.markdown(f"""
                    <div style="text-align: center;">
                        <h3>📊 {selected_year} Backend Repayment Cumulative Summary</h3>
                    </div>
                """, unsafe_allow_html=True)
                st.write("")
                st.markdown(f"""
                    <div style="display: flex; justify-content: center; gap: 30px;">
                        <div style="text-align: center;">
                            <h5>🎯 Total Backend Repayment Target</h5>
                            <p style="font-size: 20px;">₦{year_target:,.0f}</p>
                        </div>
                        <div style="text-align: center;">
                            <h5>💰 Total Repayment Paid</h5>
                            <p style="font-size: 20px;">₦{total_actual:,.0f}</p>
                        </div>
                        <div style="text-align: center;">
                            <h5>📈 Collection Percentage Achieved</h5>
                            <p style="font-size: 20px;">{total_percentage:,.2f}%</p>
                        </div>
                    </div>
                """, unsafe_allow_html=True)
                st.write("")

                # Plotting function
                def plot_progress_bar_chart(expected_df, actual_df, period_label):
                    df = pd.merge(expected_df, actual_df, left_on='date', right_on='paid_date', how='outer').fillna(0)
                    df['date'] = df['date'].combine_first(df['paid_date'])  # ensure date exists

                    # If expected is 0 and actual > 0, treat rate as 100%
                    df['progress_percent'] = df.apply(
                        lambda row: 1 if row['backend_exp_repay_today'] == 0 and row['repayment_by_date'] > 0
                        else (row['repayment_by_date'] / row['backend_exp_repay_today'] if row['backend_exp_repay_today'] > 0 else 0),
                        axis=1
                    ).round(2)

                    # Assign colors
                    df['color'] = df['progress_percent'].apply(
                        lambda x: 'green' if x >= 1 else ('orange' if x >= 0.7 else 'red')
                    )

                    # Format fields
                    df['Backend Expected Amount'] = df['backend_exp_repay_today'].apply(lambda x: f"₦{x:,.2f}")
                    df['Actual Amount'] = df['repayment_by_date'].apply(lambda x: f"₦{x:,.2f}")
                    df['Repayment Rate (%)'] = (df['progress_percent'] * 100).round(2)

                    df_display = df[['date', 'Backend Expected Amount', 'Actual Amount', 'Repayment Rate (%)']].rename(columns={'date': 'Period'})

                    # Plotting
                    fig = go.Figure()

                    for _, row in df.iterrows():
                        fig.add_trace(go.Bar(
                            x=[row['date']],
                            y=[row['progress_percent'] * 100],
                            marker_color=row['color'],
                            hovertemplate=(
                                f"<b>Period:</b> {row['date'].strftime('%Y-%m-%d')}<br>" +
                                f"<b>Backend Expected:</b> {row['Backend Expected Amount']}<br>" +
                                f"<b>Actual:</b> {row['Actual Amount']}<br>" +
                                f"<b>Progress:</b> {row['Repayment Rate (%)']}%"
                            ),
                            showlegend=False
                        ))

                    # Legend
                    legend_colors = {
                        'Completed ✅': 'green',
                        'Almost There 🚀': 'orange',
                        'Below Target 🔻': 'red'
                    }

                    for name, color in legend_colors.items():
                        fig.add_trace(go.Bar(
                            x=[None], y=[None],
                            marker_color=color,
                            name=name,
                            showlegend=True
                        ))

                    fig.update_layout(
                        title=f"{selected_year} {period_label} Backend Repayment Progress",
                        xaxis_title="Period",
                        yaxis_title="Backend Repayment Rate (%)",
                        yaxis=dict(range=[0, 100]),
                        xaxis=dict(
                            tickmode='array',
                            tickvals=df['date'],
                            ticktext=[d.strftime('%Y-%m-%d') for d in df['date']],
                            tickangle=-45
                        ),
                        barmode='group',
                        legend_title="Performance",
                        height=500,
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig)

                # Apply year filter for all periods except yearly
                if period_option == "Weekly":
                    exp = backend_weekly_exp_repay[backend_weekly_exp_repay['date'].dt.year == selected_year]
                    act = weekly_repay[weekly_repay['paid_date'].dt.year == selected_year]
                    plot_progress_bar_chart(exp, act, "Weekly")

                elif period_option == "Monthly":
                    exp = backend_monthly_exp_repay[backend_monthly_exp_repay['date'].dt.year == selected_year]
                    act = monthly_repay[monthly_repay['paid_date'].dt.year == selected_year]
                    plot_progress_bar_chart(exp, act, "Monthly")

                elif period_option == "Quarterly":
                    exp = backend_quarterly_exp_repay[backend_quarterly_exp_repay['date'].dt.year == selected_year]
                    act = quarterly_repay[quarterly_repay['paid_date'].dt.year == selected_year]
                    plot_progress_bar_chart(exp, act, "Quarterly")

                elif period_option == "Yearly":
                    plot_progress_bar_chart(backend_yearly_exp_repay, yearly_repay, "Yearly")

            pass
        except (ValueError, IndexError, KeyError, AttributeError):
            st.warning("Error loading this, Kindly skip.")

        st.markdown('---')

    ######################################################################### Bar chart for repayments ####################################################################################

        if 'all_trend' in globals() or 'all_trend' in locals():
            try:
                # Fill na with 0
                all_trend = all_trend.fillna(0)

                # Calculate repayment rate (avoid division by zero)
                all_trend ["repayment_rate"] = (
                    all_trend ["repayment_by_date"] / all_trend ["exp_repay_today"]
                ).fillna(0)  # Fill NaN (in case exp_repay_today is 0)

                all_trend ["backend_repayment_rate"] = (
                    all_trend ["repayment_by_date"] / all_trend ["backend_exp_repay_today"]
                ).fillna(0)  # Fill NaN (in case backend_exp_repay_today is 0)

                # Convert rate to percentage
                all_trend ["repayment_rate"] = (all_trend ["repayment_rate"] * 100).round(2)
                all_trend ["backend_repayment_rate"] = (all_trend ["backend_repayment_rate"] * 100).round(2)

                st.markdown(
                    f"""
                    <div style='text-align: center;'>
                        <h2>Repayment Rates</h2>
                    </div>
                    """,
                    unsafe_allow_html=True
                )

                a1, a2 = st.columns(2)

                with a1:
                    # Create side-by-side bar chart
                    fig = px.bar(
                        all_trend ,
                        x="date_disbursed",
                        y=["repayment_by_date", "exp_repay_today", "backend_exp_repay_today"],
                        title="Repayment Rate Bar Chart",
                        barmode="group",  # Side-by-side bars
                        labels={"value": "Amount", "variable": "Repayment Type"},
                        hover_data={"repayment_rate": ":.2f", "backend_repayment_rate": ":.2f"},  # Show repayment rate as percentage
                    )

                    # Customize layout
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"},
                    )

                    # Display the chart
                    st.plotly_chart(fig)

                with a2:
                    # Create side-by-side bar chart
                    fig = px.line(
                        all_trend ,
                        x="date_disbursed",
                        y=["repayment_by_date", "exp_repay_today", "backend_exp_repay_today"],
                        title="Repayment Rate Line Chart",
                        labels={"value": "Amount", "variable": "Repayment Type"},
                        hover_data={"repayment_rate": ":.2f", "backend_repayment_rate": ":.2f"},  # Show repayment rate as percentage
                    )

                    # Customize layout
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"},
                    )

                    fig.update_traces(mode="lines+markers")

                    # Display the chart
                    st.plotly_chart(fig)

                st.markdown('---')

    ######################################################################### Checks agents, trades and branches for best repayemnt ####################################################################################

                # Filter loans from this month alone
                loans_per_month = new_filtered_data[new_filtered_data["date_disbursed"] >= start_of_month]

                # Filter loans from this week alone
                loans_per_week = new_filtered_data[new_filtered_data["date_disbursed"] >= start_of_week]

                # Drop 'agent_id' to mergfe successfully and avoid a clash with 'agent_id' in repayment table
                loan_repay_merge = new_filtered_data.drop(columns='agent_id')

                # merge repayments and loans by months
                merged_repay = loan_repay_merge.merge(repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

                # Filter repayments from this month alone
                repayment_per_month = merged_repay[merged_repay["paid_date"] >= start_of_month]

                # Filter repayments from this week alone
                repayment_per_week = merged_repay[merged_repay["paid_date"] >= start_of_week]

    ######################################################################### Top Agents Disbursement ####################################################################################

                st.markdown(
                    """
                    <div style='text-align: center;'>
                        <h3> 💸 Top 10 Agents By Disbursement 💸 </h3>
                    </div>
                    """, unsafe_allow_html=True
                )

                col1, col2, col3 = st.columns(3)

                with col1:
                    # Top 10 Agents By Disbursement (This Week)
                    top_agents_by_disbursement_weekly = (
                        loans_per_week.groupby(['agent_id', 'agent_full_name'])['amount']
                        .sum()
                        .reset_index()
                        .sort_values(by='amount', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_disbursement_weekly,
                                x='amount',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Disbursement (This Week)",
                                color='amount',
                                color_continuous_scale='blugrn')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col2:
                    # Top 10 Agents By Disbursement (This Month)
                    top_agents_by_disbursement_monthly = (
                        loans_per_month.groupby(['agent_id', 'agent_full_name'])['amount']
                        .sum()
                        .reset_index()
                        .sort_values(by='amount', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_disbursement_monthly,
                                x='amount',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Disbursement (This Month)",
                                color='amount',
                                color_continuous_scale='blugrn')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col3:
                    # Top 10 Agents By Disbursement (All Time)
                    top_agents_by_disbursement_all_time = (
                        new_filtered_data.groupby(['agent_id', 'agent_full_name'])['amount']
                        .sum()
                        .reset_index()
                        .sort_values(by='amount', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_disbursement_all_time,
                                x='amount',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Disbursement (All Time)",
                                color='amount',
                                color_continuous_scale='blugrn')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                st.markdown("---")

    ######################################################################### Top Agents Repayment ####################################################################################

                st.markdown(
                    """
                    <div style='text-align: center;'>
                        <h3> 💵 Top 10 Agents By Repayments 💵 </h3>
                    </div>
                    """, unsafe_allow_html=True
                )

                col4, col5, col6 = st.columns(3)

                with col4:
                    # Top 10 Agents By Repayments (This Week)
                    top_agents_by_repayment_weekly = (
                        repayment_per_week.groupby(['agent_id', 'agent_full_name'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_repayment_weekly,
                                x='repayment_by_date',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Repayments (This Week)",
                                color='repayment_by_date',
                                color_continuous_scale='blues')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col5:
                    # Top 10 Agents By Repayments (This Month)
                    top_agents_by_repayment_monthly = (
                        repayment_per_month.groupby(['agent_id', 'agent_full_name'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_repayment_monthly,
                                x='repayment_by_date',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Repayments (This Month)",
                                color='repayment_by_date',
                                color_continuous_scale='blues')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col6:
                    # Top 10 Agents By Repayments (All Time)
                    top_agents_by_repayment_all_time = (
                        merged_repay.groupby(['agent_id', 'agent_full_name'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_repayment_all_time,
                                x='repayment_by_date',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Repayments (All Time)",
                                color='repayment_by_date',
                                color_continuous_scale='blues')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                st.markdown("---")

    ######################################################################### Top Trade Repayment ####################################################################################

                st.markdown(
                    """
                    <div style='text-align: center;'>
                        <h3> 📈 Top 10 Trades By Repayments 📉 </h3>
                    </div>
                    """, unsafe_allow_html=True
                )

                col2, col3, col4 = st.columns(3)

                with col2:
                    # Top 10 Repaying Trades (This Week)
                    top_trades_by_repayment_weekly = (
                        repayment_per_week.groupby(['users_trade'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_trades_by_repayment_weekly,
                                x='users_trade',
                                y='repayment_by_date',
                                # orientation='h',
                                title="Top 10 Repaying Trades (This Week)",
                                color='repayment_by_date',
                                color_continuous_scale='peach')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col3:
                    # Top 10 Repaying Trades (This Month)
                    top_trades_by_repayment_monthly = (
                        repayment_per_month.groupby(['users_trade'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_trades_by_repayment_monthly,
                                x='users_trade',
                                y='repayment_by_date',
                                # orientation='h',
                                title="Top 10 Repaying Trades (This Month)",
                                color='repayment_by_date',
                                color_continuous_scale='peach')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col4:
                    # Top 10 Repaying Trades (All Time)
                    top_trades_by_repayment_all_time = (
                        merged_repay.groupby(['users_trade'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_trades_by_repayment_all_time,
                                x='users_trade',
                                y='repayment_by_date',
                                # orientation='h',
                                title="Top 10 Repaying Trades (All Time)",
                                color='repayment_by_date',
                                color_continuous_scale='peach')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                st.markdown("---")

    ######################################################################### Top Branch Repayment ####################################################################################

                st.markdown(
                    """
                    <div style='text-align: center;'>
                        <h3> 🏦 Top 10 Branches By Repayments 🏬 </h3>
                    </div>
                    """, unsafe_allow_html=True
                )

                col4, col5, col6 = st.columns(3)

                with col4:
                    # Top 10 Repaying Branches (This Week)
                    top_branches_by_repayment_weekly = (
                        repayment_per_week.groupby(['sub_location'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_branches_by_repayment_weekly,
                                x='repayment_by_date',
                                y='sub_location',
                                orientation='h',
                                title="Top 10 Repaying Branches (This Week)",
                                color='repayment_by_date',
                                color_continuous_scale='sunset')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col5:
                    # Top 10 Repaying Branches (This Month)
                    top_branches_by_repayment_monthly = (
                        repayment_per_month.groupby(['sub_location'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_branches_by_repayment_monthly,
                                x='repayment_by_date',
                                y='sub_location',
                                orientation='h',
                                title="Top 10 Repaying Branches (This Month)",
                                color='repayment_by_date',
                                color_continuous_scale='sunset')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col6:
                    # Top 10 Repaying Branches (All Time)
                    top_branches_by_repayment_all_time = (
                        merged_repay.groupby(['sub_location'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_branches_by_repayment_all_time,
                                x='repayment_by_date',
                                y='sub_location',
                                orientation='h',
                                title="Top 10 Repaying Branches (All Time)",
                                color='repayment_by_date',
                                color_continuous_scale='sunset')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                st.markdown("---")

    ########################################################################### Save Files ###################################################################

                # Gets daily repayments without holidays
                daily_repayment_by_agents_ = exp_till_today_.copy() # copy
                daily_repayment_by_agents__ = daily_repayment_by_agents_.merge(new_filtered_data, how='right', on='id')
                daily_repayment_by_agents__ = daily_repayment_by_agents__[(daily_repayment_by_agents__["status"] == "OPEN") & (daily_repayment_by_agents__["start_date"] <= cur_date) & (daily_repayment_by_agents__["end_date"] >= cur_date)]
                DailyRepayments = daily_repayment_by_agents__.groupby(['agent_id', 'agent_full_name', 'agent_email', 'agent_contact', 'users_full_name', 'users_phone_number'])['exp_repay_today'].sum().reset_index()

                # Gets daily repayments without holidays
                daily_repayment_by_agents = repayment_today.groupby(['agent_id', 'agent_full_name', 'agent_email', 'agent_contact', 'users_full_name', 'users_phone_number'])['daily_repayment_amount'].sum().reset_index()

                # Write Daily repayment to sidebar as a button
                with file5:
                    if daily_repayment_by_agents.empty:
                        st.warning("No data found for the selected filters. Please adjust your selections.")
                    else:
                        # Format: "15_Jan_2025"
                        today_str = datetime.now().strftime("%d_%b_%Y")
                        file_name = f"Daily_repayments_by_agents_till_{today_str}.csv"

                        csv = daily_repayment_by_agents.to_csv(index=False).encode("utf-8")
                        st.download_button(
                            label="⬇️ Download",
                            data=csv,
                            file_name=file_name,
                            mime="text/csv"
                        )

                # Write Expected collection by agents to sidebar as a button
                with file6:
                    if DailyRepayments.empty:
                        st.warning("No data found for the selected filters. Please adjust your selections.")
                    else:
                        # Format: "15_Jan_2025"
                        today_str = datetime.now().strftime("%d_%b_%Y")
                        file_name = f"Expected_collection_till_{today_str}.csv"

                        csv = DailyRepayments.to_csv(index=False).encode("utf-8")
                        st.download_button(
                            label="⬇️ Download",
                            data=csv,
                            file_name=file_name,
                            mime="text/csv"
                        )

                # Write Open portfolio to sidebar as a button
                with file7:
                    if active_portfolio.empty:
                        st.warning("No data found for the selected filters. Please adjust your selections.")
                    else:
                        # Format: "15_Jan_2025"
                        today_str = datetime.now().strftime("%d_%b_%Y")
                        file_name = f"Open_portfolio_till_{today_str}.csv"

                        csv = active_portfolio.to_csv(index=False).encode("utf-8")
                        st.download_button(
                            label="⬇️ Download",
                            data=csv,
                            file_name=file_name,
                            mime="text/csv"
                        )

                # Show duplicated rows based on 'id'
                duplicate_ids = filtered_data[filtered_data.duplicated(subset='id', keep=False)]
                # Display filtered data inside an expander
                with st.expander("🔁 Duplicate loan record"):
                    st.dataframe(duplicate_ids)
                    # Provide feedback for skipped filters
                    if duplicate_ids.empty:
                        st.success("No loan duplicate record. Dashboard is clean 👍")
                    else:
                        st.warning(f"Displaying {len(duplicate_ids)} records.")

                pass
            except (ValueError, IndexError, KeyError, AttributeError):
                st.warning("Not enough dataset to load this, Kindly skip.")

    ############################################################################### Tab 8 #################################################################################

    with tab9:

    ######################################################################### More customization using div class ####################################################################################

        # Custom CSS for bordered metrics
        st.markdown(
            """
            <style>
            .metric-containers {
                border: 4px solid #4b9ca5; /* Sea green border */
                border-radius: 20%; /* Rounded corners */
                padding: 10px;
                text-align: center;
                margin: 15px;
                width: 100%;
                box-shadow: 3px 3px 5px rgba(0, 151, 167, 0.5); /* shadow */
            }
            .metric-values {
                font-size: 18px;
                font-weight: bold;
            }
            .metric-labels {
                font-size: 16px;
                color: #555;
                font-weight: bold;
            }
            </style>
            """, unsafe_allow_html=True
        )

        # Custom metric component with delta
        def new_bordered_metric(label, value):
            st.markdown(
                f"""
                <div class="metric-containers">
                    <div class="metric-values">{value}</div>
                    <div class="metric-labels">{label}</div>
                </div>
                """, unsafe_allow_html=True
            )

        # Metrics with borders and dynamic deltas
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            new_bordered_metric("Total Agents", f"{pd.Series(new_filtered_data['agent_id'].unique()).count():,} Agents")
        with col2:
            new_bordered_metric("Active Agents", f"{pd.Series(active_agent['agent_id'].unique()).count():,} Agents")
        with col3:
            new_bordered_metric("Inactive Agents", f"{pd.Series(new_filtered_data['agent_id'].unique()).count() - pd.Series(active_agent['agent_id'].unique()).count():,} Agents")
        with col4:
            new_bordered_metric("Past maturity", f"N{past_maturity_total_amount.round(2):,}")

        # Metrics with borders and dynamic deltas
        col5, col6, col7, col8 = st.columns(4)
        with col5:
            new_bordered_metric("Bad Loans (count)", f"{bad_loans_count:,} loans")
        with col6:
            new_bordered_metric("Bad Loans (amount)", f"N{total_bad_loans:,}")
        with col7:
            new_bordered_metric("Late Repayment (count)", f"{late_repayment_count:,} loans")
        with col8:
            new_bordered_metric("Late Repayment (amount)", f"N{total_late_repayment:,.2f}")

        st.markdown('---')

    ######################################################################################## Days to maturity ##############################################################################

        # Copy dataset
        DaysLeft = new_filtered_data.copy()

        # Gets loans expiring in a month time
        DaysLeft = DaysLeft[(DaysLeft['open_portfolio'] > 0) & (DaysLeft['days_left_to_maturity'] > 0) & (DaysLeft['days_left_to_maturity'] <= 30)]
        # Sort by days left to maturity
        DaysLeft = DaysLeft.sort_values(by='days_left_to_maturity', ascending=True).reset_index()

        # Calculates principal balance
        DaysLeft['principal_balance'] = (DaysLeft['amount'] - DaysLeft['repayment']).clip(lower=0) ## Removing my principal first from the repayments as it's almost matured

        # Calculate what we are to collect each day to meet up repayment before it matures
        DaysLeft['Pre-Maturity Collection'] = (DaysLeft['open_portfolio'] / DaysLeft['days_left_to_maturity']).round(2)

        # Apply conditions to create 'Safe Zone' column
        def classify_risk(row):
            if (row['Pre-Maturity Collection'] >= 1.5 * row['daily_repayment_amount']) & (row['Pre-Maturity Collection'] > 10_000):
                return 'Extremely Risky'
            elif row['Pre-Maturity Collection'] > row['daily_repayment_amount']:
                return 'Risky'
            elif (row['daily_repayment_amount'] >= 3 * row['Pre-Maturity Collection']) | (row['Pre-Maturity Collection'] <= 1_000):
                return 'Safe'
            else:
                return 'Not Risky'

        DaysLeft['Safe Zone'] = DaysLeft.apply(classify_risk, axis=1)

        # Define bins (fixed up to 1.2M)
        bins = [0, 10_000, 50_000, 100_000, 200_000, 300_000, 400_000, 500_000, 600_000, 700_000, 800_000, 900_000, 1_000_000, 1_100_000, 1_200_000]

        # Create labels to match the bins
        labels = [
            '0 - 10K',
            '10K - 50K',
            '50K - 100K',
            '100K - 200K',
            '200K - 300K',
            '300K - 400K',
            '400K - 500K',
            '500K - 600K',
            '600K - 700K',
            '700K - 800K',
            '800K - 900K',
            '900K - 1M',
            '1M - 1.1M',
            '1.1M - 1.2M'
        ]

        # Categorize the 'open_portfolio' column
        DaysLeft['Pending Due Category'] = pd.cut(
            DaysLeft['open_portfolio'],
            bins=bins,
            labels=labels,
            include_lowest=True
        )

        # Calculate the sum of 'open_portfolio'
        total_open_portfolio = DaysLeft['open_portfolio'].sum()
        total_backend_open_port = DaysLeft['backend_open_port'].sum()
        plus_past_maturity = past_due_date['open_portfolio'].sum() + DaysLeft['open_portfolio'].sum()
        backend_plus_past_maturity = past_due_date['open_portfolio'].sum() + DaysLeft['backend_open_port'].sum()
        risky_loans = DaysLeft[(DaysLeft['Safe Zone']=='Extremely Risky') | (DaysLeft['Safe Zone']=='Risky')]
        risky_loans_ = risky_loans['open_portfolio'].sum() + past_due_date['open_portfolio'].sum()
        backend_risky_loans_ = risky_loans['backend_open_port'].sum() + past_due_date['open_portfolio'].sum()

        # Gets rate of each safe zone
        safe_rate = DaysLeft[DaysLeft['Safe Zone']=='Safe']
        safe_rate_ = (safe_rate['open_portfolio'].sum() / DaysLeft['open_portfolio'].sum()) * 100
        backend_safe_rate_ = (safe_rate['backend_open_port'].sum() / DaysLeft['backend_open_port'].sum()) * 100

        risky_rate = DaysLeft[DaysLeft['Safe Zone']=='Risky']
        risky_rate_ = (risky_rate['open_portfolio'].sum() / DaysLeft['open_portfolio'].sum()) * 100
        backend_risky_rate_ = (risky_rate['backend_open_port'].sum() / DaysLeft['backend_open_port'].sum()) * 100

        ext_risky_rate = DaysLeft[DaysLeft['Safe Zone']=='Extremely Risky']
        ext_risky_rate_ = (ext_risky_rate['open_portfolio'].sum() / DaysLeft['open_portfolio'].sum()) * 100
        backend_ext_risky_rate_ = (ext_risky_rate['backend_open_port'].sum() / DaysLeft['backend_open_port'].sum()) * 100

        not_risky_rate = DaysLeft[DaysLeft['Safe Zone']=='Not Risky']
        not_risky_rate_ = (not_risky_rate['open_portfolio'].sum() / DaysLeft['open_portfolio'].sum()) * 100
        backend_not_risky_rate_ = (not_risky_rate['backend_open_port'].sum() / DaysLeft['backend_open_port'].sum()) * 100

        # Display the sum in markdown
        st.markdown(
            f"""
            <div style='text-align: center;'>
                <h2>Maturing Loans within 30 Days</h2>
                <h6><strong>The outstanding portfolio for loans maturing within the next 30 days is: N{total_open_portfolio:,.2f}|| <span style="color: red;">N{total_backend_open_port:,.2f}</span></strong></h6>
                <h6><strong>Outstanding portfolio plus current past maturity is: N{plus_past_maturity:,.2f} || <span style="color: red;">N{backend_plus_past_maturity:,.2f}</span></strong></h6>
                <h6><strong>Loans past maturity plus all 'Risky' or 'Extremely Risky' loans within the next 30 days is: N{risky_loans_:,.2f}</span> || <span style="color: red;">N{backend_risky_loans_:,.2f}</strong></h6>
                <h6><strong>Portfolio outstanding amount ratios 👉 Safe rate: {safe_rate_:,.2f}% ... <span style="color: red;">{backend_safe_rate_:,.2f}%</span> || Not Risky rate: {not_risky_rate_:,.2f}% ... <span style="color: red;">{backend_not_risky_rate_:,.2f}%</span> || Risky rate: {risky_rate_:,.2f}% ... <span style="color: red;">{backend_risky_rate_:,.2f}%</span> || Extremely risky rate: {ext_risky_rate_:,.2f}% ... <span style="color: red;">{backend_ext_risky_rate_:,.2f}%</span> </strong></h6>
            </div>
            """,
            unsafe_allow_html=True
        )

        # Select and arrange all repayment data columns
        DaysLeft = DaysLeft[[
            'verticals', 'sup_name', 'date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name', 'agent_contact',
            'guarantor_phone_number', 'amount', 'principal_balance', 'repayment', 'expected_repayment', 'due_today_amount', 'open_portfolio', 'Pending Due Category',
            'repayment_rate', 'daily_repayment_amount', 'Pre-Maturity Collection', 'Safe Zone', 'missed_days', 'missed_repayment', 'days_left_to_maturity', 'end_date'
        ]]

        # Rename columns for clarity
        column_mappings_ = {
            "date_disbursed": "Date",
            "users_full_name": "Name",
            "users_phone_number": "Contact",
            "users_address": "Address",
            "users_trade": "Business",
            "agent_full_name": "Loan Officer",
            "agent_contact": "LO Contact",
            "sup_name": "Supervisor",
            "guarantor_phone_number": "Guarantor Contact",
            "amount": "Principal",
            "expected_repayment": "Total Due",
            "due_today_amount": "Due Today",
            "repayment_by_date": "Paid Amount",
            "open_portfolio": "Pending Due",
            "repayment_rate": 'Repayment Rate',
            "missed_days": "Days Missed",
            "missed_repayment": "Missed Amount",
            "paid_date": "Payment Date",
            "repayment": "Paid",
            "end_date": "Maturity Date",
            "daily_repayment_amount": "Daily Collection"
        }

        DaysLeft.rename(columns=column_mappings_, inplace=True)

        # Display filtered data inside an expander
        with st.expander("Maturing Loans within 30 days"):
            st.dataframe(DaysLeft)
            # Provide feedback for skipped filters
            if DaysLeft.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                st.success(f"Displaying {len(DaysLeft)} records.")

        st.markdown('---')

    ###################################################################################### Past Maturity Analysis ####################################################################################

        # Ensure datetime format
        past_due_date['end_date'] = pd.to_datetime(past_due_date['end_date'])
        filtered_repayment_df['paid_date'] = pd.to_datetime(filtered_repayment_df['paid_date'])
        # Gets all loans end date
        loan_ends = past_due_date[['id', 'end_date']]
        # Merge repayment data with due dates on loan ID
        merged_end_date_df = filtered_repayment_df.merge(loan_ends, left_on='ajo_loan_id', right_on='id', how='left')

        # Filter for repayments made after the loan maturity date
        past_maturity_repayments = merged_end_date_df[merged_end_date_df['paid_date'] > merged_end_date_df['end_date']]
        past_maturity_repayments = past_maturity_repayments.rename(columns={'repayment_by_date': 'is_paid_past_due'})

        # Group by loan or loan officer and sum
        is_id_repayment = past_maturity_repayments.groupby('id')['is_paid_past_due'].sum().reset_index()
        is_id_date_repayment = past_maturity_repayments.groupby(['id', 'paid_date'])['is_paid_past_due'].sum().reset_index()

        # Join with loan officer, vertical, etc. from `past_due_date`
        is_id_repay_merge = past_due_date.merge(is_id_repayment, on='id', how='left')
        is_id_date_repay_merge = past_due_date.merge(is_id_date_repayment, on='id', how='left')
        # Fill null values in 'is_paid_past_due' column
        is_id_repay_merge['is_paid_past_due'] = is_id_repay_merge['is_paid_past_due'].fillna(0)
        is_id_repay_merge['repayment'] = is_id_repay_merge['repayment'].fillna(0)
        is_id_date_repay_merge['is_paid_past_due'] = is_id_date_repay_merge['is_paid_past_due'].fillna(0)
        is_id_date_repay_merge['repayment'] = is_id_date_repay_merge['repayment'].fillna(0)

        # Gets actual amount paid during loan period only
        is_id_repay_merge['Paid (Loan Period)'] = (is_id_repay_merge['repayment'] - is_id_repay_merge['is_paid_past_due']).round(2)
        # Get amount left outside during loan period only
        is_id_repay_merge['past_due_outstanding'] = (is_id_repay_merge['expected_repayment'] - is_id_repay_merge['Paid (Loan Period)'])
        # Get repayment rate from money left outside to what we've collected after the loan was past due
        is_id_repay_merge['Paid P/M Rate'] = ((is_id_repay_merge['is_paid_past_due'] / is_id_repay_merge['past_due_outstanding'])*100).round(2)

        ## Copy dataset to get last date in repayment
        last_date_repayment = filtered_repayment_df.copy()
        ## Gets the last date by sorting and grouping
        last_date_repayment = last_date_repayment.sort_values(by=['ajo_loan_id', 'paid_date']).groupby('ajo_loan_id').last().reset_index()
        is_id_repay_merge = is_id_repay_merge.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
        # Calculate days left only for loans that haven't reached their end date
        is_id_repay_merge['Days After Last Payment'] = (today - is_id_repay_merge['paid_date']).dt.days
        # Sort by P/M_Outstanding
        is_id_repay_merge = is_id_repay_merge.sort_values(by='past_due_outstanding', ascending=False).reset_index()

        # Select and arrange all repayment data columns
        is_id_repay_merge = is_id_repay_merge[[
            'verticals', 'sup_name', 'agent_full_name', 'agent_contact', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade',
            'guarantor_phone_number', 'date_disbursed', 'end_date', 'past_maturity', 'Loss_Category', 'amount', 'repayment', 'expected_repayment',
            'Paid (Loan Period)', 'is_paid_past_due', 'past_due_outstanding', 'Paid P/M Rate', 'open_portfolio', 'paid_date', 'repayment_by_date',
            'Days After Last Payment'
        ]]

        # Rename columns for clarity
        column_mappings_ = {
            "date_disbursed": "Date Disbursed",
            "users_full_name": "Name",
            "users_phone_number": "Contact",
            "users_address": "Address",
            "users_trade": "Business",
            "agent_full_name": "Loan Officer",
            "agent_contact": "LO Contact",
            "verticals": "Vertical",
            "sup_name": "Supervisor",
            "guarantor_phone_number": "Guarantor Contact",
            "amount": "Principal",
            "expected_repayment": "Total Due",
            "is_paid_past_due": "Paid Past Maturity",
            "past_maturity": "Days Past Maturity",
            "open_portfolio": "To Balance",
            "past_due_outstanding": "P/M Outstanding",
            "repayment": "Total Paid",
            "end_date": "Maturity Date",
            "paid_date": "Last Payment Date",
            "repayment_by_date": "Last Payment"
        }

        is_id_repay_merge.rename(columns=column_mappings_, inplace=True)

        # Write loans past maturity to sidebar as a button
        with file8:
            if is_id_repay_merge.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                # Format: "15_Jan_2025"
                today_str = datetime.now().strftime("%d_%b_%Y")
                file_name = f"Past_maturity_till_{today_str}.csv"

                csv = is_id_repay_merge.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="⬇️ Download",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

    ########################################################################### Recovery Officers ###################################################################

        loan_recovery_repayment_df = (loan_recovery_repayment.groupby(pd.Grouper(key='paid_date', freq='M')).agg({'repayment_by_date' : 'sum'}).round(2).reset_index())
        loan_recovery_repayment_df['period'] = loan_recovery_repayment_df['paid_date'].dt.strftime('%b %Y')  # Format: "Jan 2025"

        with st.container():
            # Loans Trends
            fig = px.line(
                loan_recovery_repayment_df,
                x="period",
                y="repayment_by_date",
                title="Recovery Officers Progress",
            )

            fig.update_traces(mode="lines+markers")

            # Add target line at 4m
            fig.add_hline(
                y=4_000_000,
                line_dash="dash",
                line_color="red",
                annotation_text="Target: ₦4M",
                annotation_position="top right"
            )

            fig.update_layout(
                xaxis_title="Date",
                yaxis_title="Recovered Amount",
                legend_title="Amount",
                paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Transparent modebar
            )

            st.plotly_chart(fig)

        st.markdown('---')

    ########################################################################## Past Maturity ###################################################################

        is_id_repay_merge['Maturity Date'] = pd.to_datetime(is_id_repay_merge['Maturity Date'], errors='coerce')

        # Ensure Maturity Date is datetime
        is_id_repay_merge['Maturity Date'] = pd.to_datetime(is_id_repay_merge['Maturity Date'], errors='coerce')

        # Create Month column
        is_id_repay_merge['Month'] = is_id_repay_merge['Maturity Date'].dt.strftime('%b %Y')

        # Group by Month and sum numeric columns
        past_loan = (
            is_id_repay_merge
            .groupby('Month')[['Principal', 'Total Paid', 'Total Due', 'Paid (Loan Period)', 'Paid Past Maturity', 'To Balance', 'P/M Outstanding']]
            .sum()
            .reset_index()
        )

        # Sort by actual date order (not alphabetical)
        past_loan['Month_sort'] = pd.to_datetime(past_loan['Month'], format='%b %Y')
        past_loan = past_loan.sort_values('Month_sort').drop(columns=['Month_sort']).reset_index(drop=True)

        # Write loans past maturity to sidebar as a button
        with file9:
            if past_loan.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                # Format: "15_Jan_2025"
                today_str = datetime.now().strftime("%d_%b_%Y")
                file_name = f"Monthly_past_maturity_summary_till_{today_str}.csv"

                csv = past_loan.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="⬇️ Download",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

        # Plot charts
        with st.container():
            col1, col2 = st.columns(2)

            # To Balance vs P/M Outstanding
            with col1:
                fig1 = px.line(
                    past_loan,
                    x="Month",
                    y=["To Balance", "P/M Outstanding"],
                    title="To Balance vs P/M Outstanding"
                )
                fig1.update_traces(mode="lines+markers")
                fig1.update_layout(
                    xaxis_title="Month",
                    yaxis_title="Amount",
                    legend_title="Metric",
                    paper_bgcolor="rgba(0,0,0,0)",
                    plot_bgcolor="rgba(0,0,0,0)",
                    modebar={"bgcolor": "rgba(0,0,0,0)"}
                )
                st.plotly_chart(fig1, use_container_width=True)

            # Paid (Loan Period) vs Paid Past Maturity
            with col2:
                fig2 = px.line(
                    past_loan,
                    x="Month",
                    y=["Principal", "To Balance", "P/M Outstanding", "Total Paid", "Total Due"],
                    title="Paid (Loan Period) vs Paid Past Maturity"
                )
                fig2.update_traces(mode="lines+markers")
                fig2.update_layout(
                    xaxis_title="Month",
                    yaxis_title="Amount",
                    legend_title="Metric",
                    paper_bgcolor="rgba(0,0,0,0)",
                    plot_bgcolor="rgba(0,0,0,0)",
                    modebar={"bgcolor": "rgba(0,0,0,0)"}
                )
                st.plotly_chart(fig2, use_container_width=True)

            # New row for third chart
            col3, col4 = st.columns(2)

            with col3:
                # Create stacked bars for "Paid (Loan Period)" and "Paid Past Maturity"
                fig = go.Figure()

                fig.add_trace(go.Bar(
                    x=past_loan['Month'],
                    y=past_loan['Paid (Loan Period)'],
                    name='Paid (Loan Period)',
                    marker_color='steelblue',
                    hovertemplate='Month: %{x}<br>Paid (Loan Period): %{y}<extra></extra>'
                ))

                fig.add_trace(go.Bar(
                    x=past_loan['Month'],
                    y=past_loan['Paid Past Maturity'],
                    name='Paid Past Maturity',
                    marker_color='orange',
                    customdata=(past_loan['Paid (Loan Period)'] + past_loan['Paid Past Maturity']),
                    hovertemplate='Month: %{x}<br>Paid Past Maturity: %{y}<br>Total Paid: %{customdata}<extra></extra>'
                ))

                # Layout for stacked effect
                fig.update_layout(
                    barmode='stack',
                    xaxis=dict(title='Month'),
                    yaxis=dict(title='Amount'),
                    title='Loan Repayments (Stacked)',
                    bargap=0.3,
                    hovermode="x unified"
                )

                st.plotly_chart(fig, use_container_width=True)

            # Total Paid vs Total Due
            with col4:
                fig4 = px.line(
                    past_loan,
                    x="Month",
                    y=["Total Paid", "Total Due"],
                    title="Total Paid vs Total Due"
                )
                fig4.update_traces(mode="lines+markers")
                fig4.update_layout(
                    xaxis_title="Month",
                    yaxis_title="Amount",
                    legend_title="Metric",
                    paper_bgcolor="rgba(0,0,0,0)",
                    plot_bgcolor="rgba(0,0,0,0)",
                    modebar={"bgcolor": "rgba(0,0,0,0)"}
                )
                st.plotly_chart(fig4, use_container_width=True)





    #     st.write(past_loan)

    # ########################################################################### Customer reports ###################################################################

    #     @st.cache_data(show_spinner=False)
    #     def count_valid_days(start_date, end_date, exceptions):
    #         count = 0
    #         current = start_date + timedelta(days=1)
    #         while current <= end_date:
    #             if current.weekday() < 5 and current not in exceptions:
    #                 count += 1
    #             current += timedelta(days=1)
    #         return count

    #     @st.cache_data(show_spinner=False)
    #     def generate_customer_report(filtered_data, last_date_repayment, holiday):
    #         # Merge loans with last payment date
    #         customer_report = filtered_data.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')

    #         # Ensure 'paid_date' is datetime
    #         customer_report['paid_date'] = pd.to_datetime(customer_report['paid_date'], errors='coerce')

    #         # Ensure holiday['date'] is datetime and create a set of exception dates
    #         holiday['date'] = pd.to_datetime(holiday['date'], errors='coerce')
    #         exception_dates = set(holiday['date'].dropna().dt.date)

    #         # Today's date
    #         today = pd.to_datetime("today").normalize().date()

    #         # Apply business day count
    #         customer_report['Days Past'] = customer_report['paid_date'].apply(
    #             lambda x: count_valid_days(x.date(), today, exception_dates) if pd.notnull(x) else None
    #         )

    #         # Clean fields
    #         customer_report['repayment'] = customer_report['repayment'].fillna(0)
    #         customer_report['exp_repay_today'] = customer_report['exp_repay_today'].fillna(0)
    #         customer_report['status'] = customer_report['status'].astype(str).str.strip().str.upper()

    #         # Customer status classification
    #         conditions_status = [
    #             (customer_report['status'] != 'COMPLETED') & customer_report['Days Past'].isna() & (customer_report['repayment'] > customer_report['exp_repay_today']),
    #             (customer_report['status'] != 'COMPLETED') & customer_report['Days Past'].isna(),
    #             (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] >= 5) & (customer_report['repayment'] > customer_report['exp_repay_today']),
    #             (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] >= 5) & (customer_report['repayment'] == customer_report['exp_repay_today']),
    #             (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] >= 5),
    #             (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] < 5) & (customer_report['repayment'] < customer_report['exp_repay_today']),
    #             (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] < 5) & (customer_report['repayment'] == customer_report['exp_repay_today']),
    #             (customer_report['status'] != 'COMPLETED')
    #         ]
    #         choices_status = [
    #             'active_customer',
    #             'inactive_customer',
    #             'performing_customer',
    #             'partially_active',
    #             'inactive_customer',
    #             'underperforming_customer',
    #             'partially_active',
    #             'active_customer'
    #         ]
    #         customer_report['customer_status'] = np.select(conditions_status, choices_status, default='no-activity')

    #         # Customer performance
    #         conditions_perf = [
    #             (customer_report['repayment_rate'] == 100),
    #             (customer_report['repayment_rate'] >= 85),
    #             (customer_report['repayment_rate'] >= 50),
    #             (customer_report['repayment_rate'] < 50)
    #         ]
    #         choices_perf = ['safe', 'inactive', 'risky', 'highly_risky']
    #         customer_report['customer_performance'] = np.select(conditions_perf, choices_perf, default='unknown')

    #         # Days log categorization
    #         bins = [-np.inf, 0, 15, 30, 45, 60, 90, np.inf]
    #         labels = ['0 days', '1-15 days', '16-30 days', '31-45 days', '46-60 days', '61-90 days', '90+ days']
    #         customer_report['days_log'] = pd.cut(customer_report['Days Past'], bins=bins, labels=labels)

    #         # Customer grade
    #         conditions_grade = [
    #             (customer_report['customer_performance'] == 'highly_risky') & (customer_report['Days Past'] > 30),
    #             (customer_report['customer_performance'] == 'highly_risky') & (customer_report['Days Past'] <= 30),
    #             (customer_report['customer_performance'] == 'risky') & (customer_report['Days Past'] > 30),
    #             (customer_report['customer_performance'] == 'risky') & (customer_report['Days Past'] <= 30),
    #         ]
    #         choices_grade = ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4']
    #         customer_report['customer_grade'] = np.select(conditions_grade, choices_grade, default=None)

    #         return customer_report

    #     # Generate customer report
    #     customer_report = generate_customer_report(filtered_data, last_date_repayment, holiday)

    #     # Select and arrange all repayment data columns
    #     customer_report = customer_report[[
    #         'verticals', 'vertical_lead_name', 'vertical_lead_email', 'vertical_lead_contact',
    #         'sup_name', 'sup_email', 'sup_contact', 'location', 'sub_location', 'agent_full_name', 'agent_contact',
    #         'agent_email', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'guarantor_phone_number',
    #         'status', 'loan_type', 'date_disbursed', 'start_date', 'end_date', 'amount', 'interest_amount', 'daily_repayment_amount', 'repayment',
    #         'exp_repay_today', 'repayment_rate', 'expected_repayment', 'completion_rate', 'open_portfolio', 'missed_days',
    #         'missed_repayment', 'Days Past', 'customer_status', 'customer_performance', 'customer_grade'
    #     ]]

    #     # Rename columns for clarity
    #     data_column_mappings__ = {
    #         "verticals": "Vertical",
    #         "vertical_lead_name": "Vertical Lead",
    #         "vertical_lead_email": "VL email",
    #         "vertical_lead_contact": "VL contact",
    #         "sup_name": "Supervisor",
    #         "sup_email": "Supervisor email",
    #         "sup_contact": "Supervisor contact",
    #         "location": "Branch",
    #         "sub_location": "Sub-Branch",
    #         "agent_full_name": "Loan Officer",
    #         "agent_email": "LO email",
    #         "agent_contact": "LO contact",
    #         "users_full_name": "Name",
    #         "users_phone_number": "Contact",
    #         "users_address": "Address",
    #         "users_trade": "Business",
    #         "guarantor_phone_number": "Guarantor Contact",
    #         "status": "Status",
    #         "loan_type": "Loan Type",
    #         "date_disbursed": "Disbursed Date",
    #         "start_date": "Start Date",
    #         "end_date": "Maturity Date",
    #         "amount": "Principal",
    #         "interest_amount": "Interest",
    #         "daily_repayment_amount": "Daily Repayment",
    #         "repayment": "Collection",
    #         "exp_repay_today": "Expected Collection",
    #         "repayment_rate": "Collection rate (%)",
    #         "expected_repayment": "Total Due",
    #         "completion_rate": "Completion rate (%)",
    #         "open_portfolio": "Pending Due",
    #         "missed_days": "Days Missed",
    #         "missed_repayment": "Missed Amount",
    #         "past_maturity": "Days Past Maturity",
    #         "Loss_Category": "P/M Category",
    #         "loan_age(days)": "Loan Age (Days)",
    #         "loan_age(months)": "Loan Age (Months)",
    #         "num_of_month": "Duration (Months)"
    #     }

    #     customer_report.rename(columns=data_column_mappings__, inplace=True)

    #     # Write customer report to sidebar as a button
    #     with file2:
    #         if customer_report.empty:
    #             st.warning("No data found for the selected filters. Please adjust your selections.")
    #         else:
    #             # Format: "15_Jan_2025"
    #             today_str = datetime.now().strftime("%d_%b_%Y")
    #             file_name = f"Customer_activity_till_{today_str}.csv"

    #             csv = customer_report.to_csv(index=False).encode("utf-8")
    #             st.download_button(
    #                 label="⬇️ Download",
    #                 data=csv,
    #                 file_name=file_name,
    #                 mime="text/csv"
    #             )

    st.markdown(
        """
        <style>
        .sidebar-footer {
            position: fixed;
            left: 0;
            bottom: 0;
            width: 100%;
            opacity: 90%;
            background: linear-gradient(to bottom,rgba(32, 178, 170, 0.7), rgba(60, 179, 113, 0.7));
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.3); /* Added shadow */
            color: white;
            text-align: left; /* Align text to the left for moving effect */
            padding: 10px 0;
            font-weight: bold;
            font-size: 1.2em;
            letter-spacing: 6px;
            z-index: 1000;
            overflow: hidden; /* Hide overflowing text */
            white-space: nowrap; /* Prevent text from wrapping */
        }

        .moving-text {
            display: inline-block;
            padding-left: 100%; /* Start offscreen to the right */
            animation: marquee 95s linear infinite; /* Adjust speed as needed */
        }

        @keyframes marquee {
            0% {
                transform: translateX(0%);
            }
            100% {
                transform: translateX(-100%); /* Move offscreen to the left */
            }
        }
        </style>
        <div class="sidebar-footer">
            <span class="moving-text"> Liberty Assured - be liberated
            || Seeds By Liberty : Ajo, BNPL, Prosper Loans, Boosta, Boosta_2x, Boosta_2x_mini
            || This Dashboard Provides A Comprehensive Overview Of Loan, Helping Users Identify Trends, Relationship Between Variables, Performance Metrics, And Other Areas
            || Time-Series Analysis for Loan Overview, Past Maturity & Open Portfolio, Cohort/Cumulative Based Trends
            || Check repayment progress as agents make collections on a daily, weekly, or monthly basis
            || Refine by loan type, get summarized/breakdown record by supervisor/agent/merchant/borrower
            || Select Best Model And Check For The Best-Fit Equations, R² Values, And Generate Predictions For The Next nth Months
            || Insights Provided Into The Loan Data, Including Agent Performance, Bad Loans, Late Repayments, Trades Collections, And Branch Analysis For This Month And All Time Side By Side
            || Get Past Maturity, Customer Performance, Loans Expiring Within 30 days
            || Growing Futures, Enriching lives : Empowering The Underserved With Every Penny and Seed
            || Seeds and Pennies - Your Financial Partner </span>
        </div>
        """,
        unsafe_allow_html=True,
    )