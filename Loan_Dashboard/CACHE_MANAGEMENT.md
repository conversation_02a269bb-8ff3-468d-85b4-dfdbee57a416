# Automatic Cache Management

## Overview

The Loan Dashboard now includes automatic cache clearing functionality that runs every 5 minutes in the background. This eliminates the need for manual cache clearing and improves application performance by preventing memory buildup.

## Features

### ✅ Automatic Cache Clearing
- **Frequency**: Every 5 minutes (300 seconds)
- **Background Operation**: Non-blocking, doesn't interrupt user experience
- **Comprehensive**: Clears all Streamlit caches (`st.cache_data`, `st.cache_resource`) and custom query caches

### ✅ Visual Status Indicator
- **Real-time Status**: Shows time remaining until next cache clear
- **Status Messages**:
  - 🔄 Auto-clear: Xmin remaining (normal operation)
  - 🔄 Cache auto-cleared! (just cleared)
  - 🔄 Cache clearing due... (overdue for clearing)

### ✅ Manual Override
- **Manual Clear Button**: Still available for immediate cache clearing
- **Synchronized**: Manual clears reset the 5-minute timer

## Implementation

### Files Modified
- `admin.py` - Added auto-cache functionality to admin dashboard
- `main.py` - Added auto-cache functionality to user dashboard
- `cache_manager.py` - New utility module with cache management functions

### Key Functions

```python
# Initialize automatic cache clearing
initialize_auto_cache_clearing()

# Check and clear cache if 5 minutes have passed
check_and_clear_cache()

# Get current cache status
get_cache_status()

# Display cache status in UI
display_cache_status()

# Manual cache clearing
manual_cache_clear()
```

## How It Works

1. **Initialization**: When the dashboard loads, `initialize_auto_cache_clearing()` is called
2. **Time Tracking**: System tracks the last cache clear time in `st.session_state`
3. **Automatic Checking**: Every page interaction checks if 5 minutes have passed
4. **Cache Clearing**: If 5+ minutes have passed, automatically clears all caches
5. **Status Updates**: UI shows real-time status of cache clearing system

## Benefits

### 🚀 Performance Improvements
- **Prevents Memory Buildup**: Regular cache clearing prevents memory accumulation
- **Consistent Performance**: Maintains consistent app performance over time
- **No User Interruption**: Background operation doesn't disrupt user workflow

### 🔧 Maintenance Reduction
- **No Manual Intervention**: Eliminates need for users to remember to clear cache
- **Automatic Operation**: Runs without any user action required
- **Reliable**: Consistent 5-minute intervals ensure regular maintenance

### 📊 Better User Experience
- **Faster Load Times**: Fresh data every 5 minutes
- **Reduced Lag**: Prevents cache-related slowdowns
- **Transparent Operation**: Users can see when cache was last cleared

## Configuration

### Timing Adjustment
To change the cache clearing interval, modify the value in `cache_manager.py`:

```python
# Change 300 to desired seconds (e.g., 600 for 10 minutes)
if time_diff.total_seconds() >= 300:  # 5 minutes
```

### Disable Auto-Clearing
To disable automatic cache clearing, comment out the initialization call:

```python
# initialize_auto_cache_clearing()  # Disabled
```

## Testing

Use the test script to verify functionality:

```bash
streamlit run test_cache_manager.py
```

The test script provides:
- Current cache status display
- Manual cache clearing
- Simulation of time passage
- Auto-refresh functionality

## Troubleshooting

### Cache Not Clearing
- Check if `st.session_state.last_auto_cache_clear` exists
- Verify 5 minutes have actually passed
- Check for any errors in the console

### Status Not Updating
- Ensure page interactions are occurring (cache only checks on user interaction)
- Verify `cache_manager.py` is properly imported
- Check session state is not being cleared elsewhere

### Performance Issues
- Monitor memory usage to ensure cache clearing is effective
- Consider adjusting the 5-minute interval if needed
- Check if other caching mechanisms are interfering

## Migration Notes

### From Manual to Automatic
- Old manual "Clear Memory" button is now "Clear Memory Now"
- Status indicator shows automatic clearing progress
- No changes needed to existing functionality

### Backward Compatibility
- All existing cache clearing functions still work
- Manual clearing still available as backup
- No breaking changes to existing code
