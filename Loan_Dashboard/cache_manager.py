"""
Cache Management Utility for Streamlit Applications
Provides automatic cache clearing functionality every 5 minutes
"""

import streamlit as st
from datetime import datetime
import queries


def check_and_clear_cache():
    """Check if 5 minutes have passed and clear cache if needed"""
    current_time = datetime.now()
    
    # Initialize last clear time if not exists
    if 'last_auto_cache_clear' not in st.session_state:
        st.session_state.last_auto_cache_clear = current_time
        return False
    
    # Check if 5 minutes (300 seconds) have passed
    time_diff = current_time - st.session_state.last_auto_cache_clear
    if time_diff.total_seconds() >= 300:  # 5 minutes
        try:
            st.cache_data.clear()
            st.cache_resource.clear()
            queries.clear_cache()
            st.session_state.last_auto_cache_clear = current_time
            return True
        except Exception as e:
            # Handle errors silently
            return False
    return False


def initialize_auto_cache_clearing():
    """Initialize the automatic cache clearing system"""
    if 'cache_system_initialized' not in st.session_state:
        st.session_state.cache_system_initialized = True
        st.session_state.last_auto_cache_clear = datetime.now()
    
    # Check and clear cache if needed (non-blocking)
    cache_cleared = check_and_clear_cache()
    return cache_cleared


def get_cache_status():
    """Get the current cache status information"""
    if 'last_auto_cache_clear' not in st.session_state:
        return {"minutes_since": 0, "next_clear_in": 5, "status": "initializing"}
    
    current_time = datetime.now()
    time_since_clear = current_time - st.session_state.last_auto_cache_clear
    minutes_since = int(time_since_clear.total_seconds() / 60)
    next_clear_in = max(0, 5 - minutes_since)
    
    if next_clear_in == 0:
        status = "due"
    elif minutes_since < 1:
        status = "just_cleared"
    else:
        status = "active"
    
    return {
        "minutes_since": minutes_since,
        "next_clear_in": next_clear_in,
        "status": status
    }


def manual_cache_clear():
    """Manually clear cache and update timestamps"""
    try:
        st.cache_data.clear()
        st.cache_resource.clear()
        queries.clear_cache()
        st.session_state.last_auto_cache_clear = datetime.now()
        return True
    except Exception as e:
        return False


def display_cache_status(cache_just_cleared=False):
    """Display cache status in the UI"""
    cache_info = get_cache_status()
    
    if cache_just_cleared or cache_info["status"] == "just_cleared":
        st.success("🔄 Cache auto-cleared!")
    elif cache_info["status"] == "due":
        st.warning("🔄 Cache clearing due...")
    else:
        st.info(f"🔄 Auto-clear: {cache_info['next_clear_in']}min remaining")
