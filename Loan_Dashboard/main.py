import pandas as pd
import numpy as np
from datetime import timedelta, date, datetime
import warnings
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
import time
import importlib
import sys
import types
import os
import base64
import queries
from queries import holiday_, repayment_df_, escrow_df_, staff_savings_trans_, monthly_repayments_, disbursement_4_last_month_, earnings_, loginactivity_, sup_df_, loans_df_
from cache_manager import initialize_auto_cache_clearing

warnings.filterwarnings('ignore')

def show_user_dashboard(df):
    # Initialize automatic cache clearing (runs in background)
    initialize_auto_cache_clearing()

    # Gets dataframe
    filtered_data = df
    holiday = holiday_
    repayment_df = repayment_df_
    escrow_df = escrow_df_
    staff_savings_trans = staff_savings_trans_
    monthly_repayments = monthly_repayments_
    disbursement_4_last_month = disbursement_4_last_month_
    earnings = earnings_
    loginactivity = loginactivity_
    sup_df = sup_df_
    loans_df = loans_df_

    # sets logo
    st.logo("Seeds logo2.png", size="large")

    # Sets background image and color
    @st.cache_data(show_spinner=False)
    def set_background(image_path):
        if not os.path.exists(image_path):
            st.error(
                f"Image '{image_path}' not found. Ensure the file is in the correct directory."
            )
            return

        with open(image_path, "rb") as f:
            encoded_image = base64.b64encode(f.read()).decode()

        background_style = f"""
        <style>
        /* Darker background with solid dark blue */
        .stApp {{
            background: linear-gradient(to bottom, #00001, #000015); /* Darker blue */
        }}

        /* Sidebar styling - dark grey */
        [data-testid="stSidebar"] {{
            background-color: #33333 !important; /* dark grey */
        }}

        /* Centered transparent text image */
        .background-container {{
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40%; /* Adjust width if needed */
            text-align: center;
            z-index: 0; /* Ensure image is behind the dashboard and infront of the background color */
        }}

        .background-container img {{
            width: 100%;
            height: auto;
            opacity: 0.1; /* Set to minmal visibility */
        }}
        </style>

        <div class="background-container">
            <img src="data:image/png;base64,{encoded_image}">
        </div>
        """

        st.markdown(background_style, unsafe_allow_html=True)

    # Call the function with your image
    set_background("Seeds logo2.png")

    st.sidebar.markdown('---')

    ############################################################################ Create date range #######################################################################################

    # all_time button
    all_time = st.sidebar.checkbox("All Time", value=False)
    # start date
    start_date = st.sidebar.date_input("View Dashboard From ...", filtered_data['date_disbursed'].dropna().min())
    # end date
    end_date = st.sidebar.date_input("View Dasboard Till ...", date.today())

    # Checks for selected date range and if date range is valid
    if all_time:
        st.sidebar.success(f"All time from {start_date} to {end_date}")
    elif (start_date > end_date) and not all_time:
        st.sidebar.error("Error: Start Date must be before End Date.")
    else:
        st.sidebar.success(f"Date range: {start_date} to {end_date}")

    ############################################################################ Sets conditions for date filtering #######################################################################################

    # Override date filters if "All Time" is selected
    filtered_data['date_disbursed'] = pd.to_datetime(filtered_data['date_disbursed'])
    if all_time:
        start_date, end_date = pd.to_datetime(filtered_data["date_disbursed"].dropna().min()), pd.to_datetime(date.today())
    else:
        filtered_data = filtered_data[
            (filtered_data["date_disbursed"] >= pd.to_datetime(start_date)) &
            (filtered_data["date_disbursed"] <= pd.to_datetime(end_date))
        ]

    st.sidebar.markdown('---')

    ######################################################################### Set toggle for past maturity ####################################################################################

    # Past maturity toggle
    show_all_statuses = st.sidebar.toggle("Add Lost Loans (If any)", value=True)

    # Apply filter based on toggle
    if show_all_statuses:
        filtered_data = filtered_data[filtered_data["status"].isin(["OPEN", "COMPLETED", "PAST_MATURITY"])]
    else:
        filtered_data = filtered_data[filtered_data["status"].isin(["OPEN", "COMPLETED"])]

    st.sidebar.markdown('---')

    ######################################################################### Set toggle for loss ####################################################################################

    # Categorization function
    def categorize_loss(days):
        if days == 0:
            return 'No loss'
        elif 1 <= days <= 15:
            return '1-15 days'
        elif 16 <= days <= 30:
            return '16-30 days'
        elif 31 <= days <= 45:
            return '31-45 days'
        elif 46 <= days <= 60:
            return '46-60 days'
        elif 61 <= days <= 90:
            return '61-90 days'
        else:
            return '90+ days'

    # Apply the categorization
    filtered_data['Loss_Category'] = filtered_data['past_maturity'].apply(categorize_loss)

    ############################################################################ Filters multiselect #######################################################################################

    # Select loan_type
    selected_loan_type = st.sidebar.multiselect('Select Loan Type', filtered_data['loan_type'].unique(), default=None)

    # Select status
    filtered_status = filtered_data[filtered_data['loan_type'].isin(selected_loan_type)]['status'].unique() if selected_loan_type else filtered_data['status'].unique()
    selected_status = st.sidebar.multiselect('Select status', filtered_status, default=None)

    # Filter data based on selected loan_type and status
    if selected_loan_type:
        filtered_data = filtered_data[filtered_data['loan_type'].isin(selected_loan_type)]

    if selected_status:
        filtered_data = filtered_data[filtered_data['status'].isin(selected_status)]

    # Select verticals (based on selected loan_type and status)
    filtered_verticals = pd.Series(filtered_data[filtered_data['status'].isin(selected_status)]['verticals'].unique()).sort_values() if selected_status else pd.Series(filtered_data['verticals'].unique()).sort_values()
    selected_verticals = st.sidebar.multiselect('Select Vertical', filtered_verticals, default=None)

    # Filter data based on selected verticals
    if selected_verticals:
        filtered_data = filtered_data[filtered_data['verticals'].isin(selected_verticals)]

    # Select sup_name (based on selected loan_type, status and verticals)
    filtered_sup_name = pd.Series(filtered_data[filtered_data['verticals'].isin(selected_verticals)]['sup_name'].unique()).sort_values() if selected_verticals else pd.Series(filtered_data['sup_name'].unique()).sort_values()
    selected_sup_name = st.sidebar.multiselect('Select Supervisor', filtered_sup_name, default=None)

    # Filter data based on selected sup_name
    if selected_sup_name:
        filtered_data = filtered_data[filtered_data['sup_name'].isin(selected_sup_name)]

    # Select agent_full_name (based on selected sup_name, loan_type, status and vertical lead)
    filtered_agent_full_name = pd.Series(filtered_data[filtered_data['sup_name'].isin(selected_sup_name)]['agent_full_name'].unique()).sort_values() if selected_sup_name else pd.Series(filtered_data['agent_full_name'].unique()).sort_values()
    selected_agent_full_name = st.sidebar.multiselect('Select Agent', filtered_agent_full_name, default=None)

    # Filter data based on selected agent_full_name
    if selected_agent_full_name:
        filtered_data = filtered_data[filtered_data['agent_full_name'].isin(selected_agent_full_name)]

    # Filter repayment_df based on the filtered_data
    if not filtered_data.empty:  # Check if filtered_data is not empty
        loan_ids = filtered_data['id'].unique()
        filtered_repayment_df = repayment_df[repayment_df['ajo_loan_id'].isin(loan_ids)]
    else:
        filtered_repayment_df = pd.DataFrame() # If filtered_data is empty, then repayment_df is empty.

    st.sidebar.markdown("---")

    ########################################################################### set sidebar logout and reset buttons #####################################################################################

    # Define reload function to re-import from queries.py
    def reload_queries_functions():
        global holiday_, repayment_df_, sav_transaction_df_, loan_saving_df_, escrow_df_, staff_savings_trans_
        global monthly_repayments_, disbursement_4_last_month_, earnings_, loginactivity_, sup_df_, merchants_df_, loans_df_

        from queries import (
            holiday_, repayment_df_, sav_transaction_df_, loan_saving_df_, escrow_df_,
            staff_savings_trans_, monthly_repayments_, disbursement_4_last_month_, earnings_,
            loginactivity_, sup_df_, merchants_df_, loans_df_)

    # Clear all caches including queries.py
    def clear_all_caches():
        st.cache_data.clear()
        st.cache_resource.clear()

        try:
            queries.clear_cache() # Call function in queries.py
            importlib.reload(queries) # Reload the module
            reload_queries_functions() # Re-import all functions
        except Exception as e:
            st.warning(f"Failed to clear queries cache: {e}")

    # Sidebar Buttons
    c1, c2 = st.sidebar.columns([1, 1])

    with c1:
        st.markdown(
            """
            <style>
            div.stButton > button {
                width: 100%;
            }
            </style>
            """,
            unsafe_allow_html=True,
        )
        if st.button("Clear Memory"):
            clear_all_caches()
            st.success("All memory caches cleared successfully!")
            time.sleep(2)
            st.rerun()

    with c2:
        st.markdown(
            """
            <style>div.stButton > button {width: 100%;}</style>
            """,
            unsafe_allow_html=True,
        )
        if st.button("Logout"):
            st.session_state["logged_in"] = False
            st.session_state["role"] = None
            st.session_state["username"] = None
            st.session_state["page"] = "login"
            st.success("You have been logged out.")
            time.sleep(3)
            st.rerun()

    ########################################################################### Data Cleaning #####################################################################################

    try:
        # Gets all loans for exp_repay_today
        df_1 = filtered_data.copy()

        # Define dates
        today_ = pd.to_datetime(end_date).normalize()
        yesterday_ = (today_ - pd.DateOffset(days=1)).normalize()

        # This week
        start_of_current_week_ = (today_ - pd.DateOffset(days=(today_.weekday()))).normalize()

        # End of last month
        start_of_current_month_ = (today_.replace(day=1)).normalize()

        # Start of year
        start_of_year_ = today_.replace(month=1, day=1)

        # Convert loan date columns
        for col in ['date_disbursed', 'start_date', 'end_date']:
            df_1[col] = pd.to_datetime(df_1[col]).dt.normalize()

        # Validate date range
        first_date = df_1['start_date'].min()
        end_date__ = df_1['end_date'].max()
        if pd.isna(first_date) or pd.isna(end_date__):
            st.error("Invalid date range: Missing start_date or end_date.")
            st.stop()

        # Prepare holidays
        holiday['date'] = pd.to_datetime(holiday['date']).dt.normalize()

        @st.cache_data(show_spinner=False)
        def build_expected_schedule(loans_df, holidays_df, today):
            # Prepare holiday sets
            general_hols = set(holidays_df[holidays_df['agent_id'].isna()]['date'])
            agent_hols_map = {
                agent: set(sub['date'])
                for agent, sub in holidays_df[holidays_df['agent_id'].notna()].groupby('agent_id')
            }

            records = []
            for _, loan in loans_df.dropna(subset=['start_date','end_date']).iterrows():
                loan_id = loan['id']
                start = loan['start_date']
                end = min(loan['end_date'], today)
                daily_amt = loan['daily_repayment_amount']
                cap = loan['expected_repayment']
                agent = loan.get('agent_id', np.nan)

                # Business days between start and end
                dates = pd.date_range(start, end, freq='B')
                dates = [d for d in dates
                        if d not in general_hols
                        and d not in agent_hols_map.get(agent, set())]

                cum = 0.0
                for d in dates:
                    to_add = min(daily_amt, cap - cum)
                    if to_add <= 0:
                        break
                    cum += to_add
                    records.append({
                        'loan_id': loan_id,
                        'date': d,
                        'exp_repay_today': to_add,
                        'cumulative_expected': cum
                    })

            return pd.DataFrame(records)

        # Build schedule
        schedule_df = build_expected_schedule(df_1, holiday, today_)

        # Merge with original for context
        merged = df_1.merge(
            schedule_df,
            left_on='id', right_on='loan_id',
            how='right'
        ).sort_values(['date_disbursed','loan_id','date'])

        # Extract date only (no time)
        merged['date'] = merged['date'].dt.normalize()

        # Filter into periods
        exp_till_today = merged[merged['date'] == today_]
        exp_till_yesterday = merged[merged['date'] == yesterday_]
        exp_till_ThisWeek = merged[merged['date'] >= start_of_current_week_]
        exp_till_ThisMonth = merged[merged['date'] >= start_of_current_month_]
        exp_till_ThisYear = merged[merged['date'] >= start_of_year_]

        # Group by amount
        exp_till_today_ = exp_till_today.groupby(['id','date_disbursed'])[['exp_repay_today']].sum().sort_values(by='date_disbursed', ascending=True).reset_index()
        exp_till_yesterday_ = exp_till_yesterday.groupby(['id','date_disbursed'])[['exp_repay_today']].sum().sort_values(by='date_disbursed', ascending=True).reset_index()
        exp_till_ThisWeek_ = exp_till_ThisWeek.groupby(['id','date_disbursed'])[['exp_repay_today']].sum().sort_values(by='date_disbursed', ascending=True).reset_index()
        exp_till_ThisMonth_ = exp_till_ThisMonth.groupby(['id','date_disbursed'])[['exp_repay_today']].sum().sort_values(by='date_disbursed', ascending=True).reset_index()
        exp_till_ThisYear_ = exp_till_ThisYear.groupby(['id','date_disbursed'])[['exp_repay_today']].sum().sort_values(by='date_disbursed', ascending=True).reset_index()
        exp_all_time = merged.groupby(['id','date_disbursed'])[['exp_repay_today']].sum().sort_values(by='date_disbursed', ascending=True).reset_index()

        pass
    except (ValueError, IndexError, KeyError, AttributeError, NameError):
        st.warning("An error occurred while processing the amount due.")

    # Further data cleaning
    @st.cache_data(show_spinner=False)
    def process_filtered_data(filtered_data):
        """
        Processes the loans DataFrame by performing various operations:

        - Adjusts 'due_today_amount' to ensure it does not exceed 'expected_repayment'.
        - Adjusts 'exp_repay_today' to ensure it does not exceed 'expected_repayment'.
        - Calculates missed repayments.
        - Computes repayment and completion rates.
        - Determines open portfolio values.
        - Converts 'date_disbursed' to datetime format and extracts month names.
        - Converts 'date_disbursed' to datetime format and extracts Year.
        - Converts 'date_disbursed' to datetime format and extracts Quarters.
        - Extracts the numeric value from 'tenor' to determine the number of months.
        - Sorts the DataFrame by 'date_disbursed'.
        - Calculates monthly repayment values.
        - Determines interest and principal element percentages.
        - Calculates interest and principal amounts collected.

        Args:
            filtered_data (pd.DataFrame): The input DataFrame containing loan data.

        Returns:
            pd.DataFrame: The processed DataFrame.
        """
        # merge to get exp_repay_today
        filtered_data = filtered_data.merge(exp_all_time,how='left', on=['id', 'date_disbursed'])

        # Adjust 'due_today_amount' and 'exp_repay_today' column directly
        filtered_data['due_today_amount'] = np.minimum(filtered_data['due_today_amount'], filtered_data['expected_repayment'])
        filtered_data['exp_repay_today'] = np.minimum(filtered_data['exp_repay_today'], filtered_data['expected_repayment'])

        # Expected repayment count
        filtered_data['exp_repay_count'] = (filtered_data['exp_repay_today'] / filtered_data['daily_repayment_amount']).round(2)

        # Missed amount using due_today
        filtered_data['missed_repayment'] = filtered_data.apply(
            lambda row: max(0, row['exp_repay_today'] - row['repayment'] if pd.notna(row['repayment']) else 0),
            axis=1) # This gets only the missed repayments and it gives all the overpayment a cap at 0 instead of a negative value

        # Missed days
        filtered_data['missed_days'] = np.ceil(filtered_data['missed_repayment'] / filtered_data['daily_repayment_amount'])

        # Apply condition: If missed_days is 1 and missed_repayment is less than 200, set it to 0
        filtered_data['missed_days'] = np.where((filtered_data['missed_days'] == 1) & (filtered_data['missed_repayment'] < 200), 0, filtered_data['missed_days'])

        # Calculate Repayment Rate (%)
        filtered_data['repayment_rate'] = np.where(
            filtered_data['exp_repay_today'] == 0,
            0,
            (filtered_data['repayment'] / filtered_data['exp_repay_today'] * 100).round()
        )
        filtered_data['repayment_rate'] = filtered_data['repayment_rate'].clip(upper=100)

        # Calculate Completion Rate (%)
        filtered_data['completion_rate'] = np.where(
            (filtered_data['expected_repayment'] == 0) | (filtered_data['status'] == 'OPEN'),
            0,
            (filtered_data['repayment'] / filtered_data['expected_repayment'] * 100).round()
        )
        filtered_data['completion_rate'] = filtered_data['completion_rate'].clip(upper=100)

        # Open portfolio
        filtered_data['open_portfolio'] = filtered_data.apply(
            lambda row: max(0, row['expected_repayment'] - row['repayment'] if pd.notna(row['repayment']) else row['expected_repayment']),
            axis=1) # This gets only the total defaults and it gives all the overpayment a cap at 0 instead of a negative value

        # Get current date and ensure it's a Timestamp
        TodayDate = pd.Timestamp.today().normalize()

        # Calculate days left only for loans that haven't reached their end date
        filtered_data['days_left_to_maturity'] = (filtered_data['end_date'] - TodayDate).dt.days

        # If 'end_date' is already passed, set 'days_left_to_maturity' to 0
        filtered_data.loc[filtered_data['days_left_to_maturity'] < 0, 'days_left_to_maturity'] = 0

        # Extract month names
        filtered_data['month'] = filtered_data['date_disbursed'].dt.strftime('%B')  # Use '%b' for abbreviated month names

        # Extract Year
        filtered_data['Year'] = filtered_data['date_disbursed'].dt.strftime('%Y')

        # Calculate Loan Duration
        filtered_data['loan_age(days)'] = (filtered_data['end_date'].clip(upper=TodayDate) - (filtered_data['start_date'] - pd.Timedelta(days=4))).dt.days

        # Gets loan duration in months
        filtered_data['loan_age(months)'] = (filtered_data['end_date'].clip(upper=TodayDate) - (filtered_data['start_date'] - pd.Timedelta(days=4)))

        # Extracts number of month from 'tenor'
        filtered_data['num_of_month'] = filtered_data['tenor'].str.extract(r'(\d+)').astype(int)

        # Sort by 'date_disbursed'
        filtered_data = filtered_data.sort_values(by='date_disbursed')

        # Gets monthly repayment for loan
        filtered_data['monthly_repayment'] = filtered_data['expected_repayment'] / filtered_data['num_of_month']

        # Gets interest monthly repayment
        filtered_data['int_monthly_repayment'] = filtered_data['interest_amount'] / filtered_data['num_of_month']

        # Gets principal monthly repayment
        filtered_data['prin_monthly_repayment'] = filtered_data['amount'] / filtered_data['num_of_month']

        # Gets interest element percentage
        filtered_data['interest_element_pct'] = (filtered_data['int_monthly_repayment'] / filtered_data['monthly_repayment']).round(4)

        # Gets principal element percentage
        filtered_data['principal_element_pct'] = (filtered_data['prin_monthly_repayment'] / filtered_data['monthly_repayment']).round(4)

        # Interest element
        filtered_data['interest_element'] = (filtered_data['interest_element_pct'] * filtered_data['repayment']).round(2)

        # Principal collected
        filtered_data['principal_element'] = (filtered_data['principal_element_pct'] * filtered_data['repayment']).round(2)

        return filtered_data

    # Loads data
    filtered_data = process_filtered_data(filtered_data)

    ############################################################################ Loans past due date #######################################################################################

    # Convert end_date to datetime
    filtered_data["end_date"] = pd.to_datetime(filtered_data["end_date"])

    # Filter loans with status 'OPEN' and end_date greater than today
    cur_date = pd.to_datetime(end_date)

    # Filter for past_due date when ststua is not completed
    past_due_date = filtered_data[(filtered_data["status"] != "COMPLETED") & (filtered_data["end_date"] < cur_date)]

    # Calculate the count and total amount for today
    past_maturity_total_amount = past_due_date["open_portfolio"].sum().round(2) # Gets total outstanding amount

    # Portfolio At Risk by number of days
    past_maturity_7_days = past_due_date[past_due_date['past_maturity']<=7]
    past_maturity_15_days = past_due_date[(past_due_date['past_maturity']>7) & (past_due_date['past_maturity']<=15)]
    past_maturity_30_days = past_due_date[(past_due_date['past_maturity']>15) & (past_due_date['past_maturity']<=30)]
    past_maturity_45_days = past_due_date[(past_due_date['past_maturity']>30) & (past_due_date['past_maturity']<=45)]
    past_maturity_60_days = past_due_date[(past_due_date['past_maturity']>45) & (past_due_date['past_maturity']<=60)]
    past_maturity_90_days = past_due_date[(past_due_date['past_maturity']>60) & (past_due_date['past_maturity']<=90)]
    past_maturity_90_plus_days = past_due_date[past_due_date['past_maturity']>90]

    # Portfolio At Risk count
    past_maturity_7_days_count = past_maturity_7_days['id'].count().round(2)
    past_maturity_15_days_count = past_maturity_15_days['id'].count().round(2)
    past_maturity_30_days_count = past_maturity_30_days['id'].count().round(2)
    past_maturity_45_days_count = past_maturity_45_days['id'].count().round(2)
    past_maturity_60_days_count = past_maturity_60_days['id'].count().round(2)
    past_maturity_90_days_count = past_maturity_90_days['id'].count().round(2)
    past_maturity_90_plus_days_count = past_maturity_90_plus_days['id'].count().round(2)

    # Portfolio At Risk sum
    past_maturity_7_days_sum = past_maturity_7_days['open_portfolio'].sum().round(2)
    past_maturity_15_days_sum = past_maturity_15_days['open_portfolio'].sum().round(2)
    past_maturity_30_days_sum = past_maturity_30_days['open_portfolio'].sum().round(2)
    past_maturity_45_days_sum = past_maturity_45_days['open_portfolio'].sum().round(2)
    past_maturity_60_days_sum = past_maturity_60_days['open_portfolio'].sum().round(2)
    past_maturity_90_days_sum = past_maturity_90_days['open_portfolio'].sum().round(2)
    past_maturity_90_plus_days_sum = past_maturity_90_plus_days['open_portfolio'].sum().round(2)

    total_bad_loans = (past_maturity_45_days_sum + past_maturity_60_days_sum + past_maturity_90_days_sum + past_maturity_90_plus_days_sum).round(2)
    bad_loans_count = (past_maturity_45_days_count + past_maturity_60_days_count + past_maturity_90_days_count + past_maturity_90_plus_days_count).round(2)
    total_late_repayment = (past_maturity_7_days_sum + past_maturity_15_days_sum + past_maturity_30_days_sum)
    late_repayment_count = (past_maturity_7_days_count + past_maturity_15_days_count + past_maturity_30_days_count)

    ######################################################################### Active Portfolio ####################################################################################

    # Filter for past_due date
    active_portfolio = filtered_data[(filtered_data["status"] == "OPEN") & (filtered_data["end_date"] >= cur_date)]

    ######################################################################### Create data for yesterday ####################################################################################

    # Convert 'date_disbursed' to datetime format for filtering
    filtered_data['date_disbursed'] = pd.to_datetime(filtered_data['date_disbursed'])

    # previous date
    yest_date = cur_date - timedelta(days=1)

    # Filter rows where 'date_disbursed' is up until yesterday
    previous_data = filtered_data[filtered_data['date_disbursed'] <= yest_date]
    # Reset the index if needed
    previous_data = previous_data.reset_index(drop=True)

    ######################################################################### Today's expected payments for all 'OPEN' loans ####################################################################################

    filtered_data['start_date'] = pd.to_datetime(filtered_data['start_date'])
    filtered_data['end_date'] = pd.to_datetime(filtered_data['end_date'])

    # Filter loans with status 'OPEN' for today
    repayment_today = filtered_data[
        (filtered_data["status"] == "OPEN") &
        (filtered_data["start_date"] <= cur_date) &
        (filtered_data["end_date"] >= cur_date)
    ]

    ############################################################################ Getting all users that come back to take a loan #######################################################################################

    # Convert start_date to datetime and extract Month and Year
    loans_df['start_date'] = pd.to_datetime(loans_df['start_date'])
    loans_df['Month_and_Year'] = loans_df['start_date'].dt.strftime("%b %Y")

    # Copy df
    grouped_df = loans_df.copy()

    # Mark the first loan occurrence for each user
    grouped_df['is_first_loan'] = ~grouped_df.duplicated(subset=['users_full_name'], keep='first')

    # Returning Users (Excluding First Occurrence)
    repeat_loans_df = grouped_df[~grouped_df['is_first_loan']]  # Remove first occurrence

    # Count unique returning users per month
    monthly_returning_users = repeat_loans_df.groupby('Month_and_Year')['users_full_name'].nunique().reset_index()
    monthly_returning_users = monthly_returning_users.rename(columns={'users_full_name': 'returning_users'})

    # Total Repeat Users (Including First Occurrence)
    all_repeat_users = grouped_df[grouped_df['users_full_name'].duplicated(keep=False)]  # Keep all repeat borrowers

    # Count total repeat users per month (counting first occurrence)
    monthly_total_repeat_users = all_repeat_users.groupby('Month_and_Year')['users_full_name'].nunique().reset_index()
    monthly_total_repeat_users = monthly_total_repeat_users.rename(columns={'users_full_name': 'total_repeat_users'})

    # Get Total Users Per Month
    monthly_total_users = grouped_df.groupby('Month_and_Year')['users_full_name'].nunique().reset_index()
    monthly_total_users = monthly_total_users.rename(columns={'users_full_name': 'total_users'})

    # Merge all DataFrames
    users_retake_df = monthly_total_users.merge(monthly_returning_users, on='Month_and_Year', how='left').fillna(0)
    users_retake_df = users_retake_df.merge(monthly_total_repeat_users, on='Month_and_Year', how='left').fillna(0)

    # Calculate percentage of returning users (excluding first occurrence)
    users_retake_df['returning_pct'] = round((users_retake_df['returning_users'] / users_retake_df['total_users']) * 100, 2)

    # Calculate percentage of total repeat users (including first occurrence)
    users_retake_df['total_repeat_pct'] = round((users_retake_df['total_repeat_users'] / users_retake_df['total_users']) * 100, 2)

    ############################################################################ Repayment By Paid date #######################################################################################

    # Convert datetime and make 'cur_date' able to read on repayment dataset by...
    # ...altering the date and creating a new date column so it doesn't affect the line chart
    repayment_df['paid_date'] = pd.to_datetime(repayment_df['paid_date']).dt.normalize().dt.tz_localize(None)
    filtered_repayment_df['paid_date'] = pd.to_datetime(filtered_repayment_df['paid_date']).dt.normalize().dt.tz_localize(None)

    ############################################################################ Dashboard graphs and plots #######################################################################################

    # splits dashboard into tabs
    tab1, tab2, tab3, tab4 = st.tabs(["Portfolio Overview", "Time-Series Analysis", "Supervisor Portfolio", "Product Analysis"])

    ############################################################################ Tab 1 #######################################################################################

    with tab1:

    ######################################################################### Set Date Filters for metrics ####################################################################################

        # Get today's date
        today = pd.to_datetime(cur_date)
        # Calculate yesterday's date
        yesterday = today - pd.DateOffset(days=1)
        # Calculate the day before yesterday's date
        day_before_yesterday = today - pd.DateOffset(days=2)
        # Calculate the start of this week
        start_of_week = today - pd.DateOffset(days=today.weekday())  # Monday as start of week
        # Calculate the start of last week (Monday)
        start_of_last_week = start_of_week - pd.DateOffset(weeks=1)
        # Calculate the start of two weeks ago (Monday)
        start_of_last_2_weeks = start_of_week - pd.DateOffset(weeks=2)
        # Calculate the start of this month
        start_of_month = today.replace(day=1)
        # Calculate the start of last month
        start_of_last_month = (start_of_month - pd.DateOffset(days=1)).replace(day=1)
        # Calculate the start of two months ago
        start_of_last_2_months = (start_of_last_month - pd.DateOffset(days=1)).replace(day=1)
        # Calculate the start of this year
        start_of_year = today.replace(month=1, day=1)
        # Calculate the start of last year
        start_of_last_year = start_of_year - pd.DateOffset(years=1)
        # Calculate the start of two years ago
        start_of_last_2_years = start_of_last_year - pd.DateOffset(years=1)

    ######################################################################### Set Parameters for Dashboard ####################################################################################

        # Filter datasets for repayment

        ## Note that we are using the filtered_repayment_df cause we want it to be filtered based...
        ## ...on the filters that are already defined by the sidebar
        ### 'filtered_repayment_df' = filtered repayments || 'repayment_df' = repayment dataset

        repay_df_today = filtered_repayment_df[filtered_repayment_df['paid_date'] == today]
        repay_df_yesterday = filtered_repayment_df[filtered_repayment_df['paid_date'] == yesterday]
        repay_df_day_before_yesterday = filtered_repayment_df[filtered_repayment_df['paid_date'] == day_before_yesterday]
        repay_df_week = filtered_repayment_df[filtered_repayment_df['paid_date'] >= start_of_week]
        repay_df_last_week = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_week) &
            (filtered_repayment_df['paid_date'] < start_of_week)
        ]
        repay_df_last_2_weeks = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_2_weeks) &
            (filtered_repayment_df['paid_date'] < start_of_last_week)
        ]
        repay_df_month = filtered_repayment_df[filtered_repayment_df['paid_date'] >= start_of_month]
        repay_df_last_month = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_month) &
            (filtered_repayment_df['paid_date'] < start_of_month)
        ]
        repay_df_last_2_months = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_2_months) &
            (filtered_repayment_df['paid_date'] < start_of_last_month)
        ]
        repay_df_year = filtered_repayment_df[filtered_repayment_df['paid_date'] >= start_of_year]
        repay_df_last_year = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_year) &
            (filtered_repayment_df['paid_date'] < start_of_year)
        ]
        repay_df_last_2_years = filtered_repayment_df[
            (filtered_repayment_df['paid_date'] >= start_of_last_2_years) &
            (filtered_repayment_df['paid_date'] < start_of_last_year)
        ]

        # Aggregate data for repayment
        repay_today_total = repay_df_today['repayment_by_date'].sum()
        repay_yesterday_total = repay_df_yesterday['repayment_by_date'].sum()
        repay_day_before_yesterday_total = repay_df_day_before_yesterday['repayment_by_date'].sum()
        repay_week_total = repay_df_week['repayment_by_date'].sum()
        repay_last_week_total = repay_df_last_week['repayment_by_date'].sum()
        repay_last_2_week_total = repay_df_last_2_weeks['repayment_by_date'].sum()
        repay_month_total = repay_df_month['repayment_by_date'].sum()
        repay_last_month_total = repay_df_last_month['repayment_by_date'].sum()
        repay_last_2_month_total = repay_df_last_2_months['repayment_by_date'].sum()
        repay_year_total = repay_df_year['repayment_by_date'].sum()
        repay_last_year_total = repay_df_last_year['repayment_by_date'].sum()
        repay_last_2_year_total = repay_df_last_2_years['repayment_by_date'].sum()
        repay_all_time_total = filtered_repayment_df['repayment_by_date'].sum()

        # Filter datasets for loans by date_disbursed
        df_today = filtered_data[filtered_data['date_disbursed'] == today]
        df_yesterday = filtered_data[filtered_data['date_disbursed'] == yesterday]
        df_day_before_yesterday = filtered_data[filtered_data['date_disbursed'] == day_before_yesterday]
        df_week = filtered_data[filtered_data['date_disbursed'] >= start_of_week]
        df_last_week = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_week) &
            (filtered_data['date_disbursed'] < start_of_week)
        ]
        df_last_2_weeks = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_2_weeks) &
            (filtered_data['date_disbursed'] < start_of_last_week)
        ]
        df_month = filtered_data[filtered_data['date_disbursed'] >= start_of_month]
        df_last_month = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_month) &
            (filtered_data['date_disbursed'] < start_of_month)
        ]
        df_last_2_months = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_2_months) &
            (filtered_data['date_disbursed'] < start_of_last_month)
        ]
        df_year = filtered_data[filtered_data['date_disbursed'] >= start_of_year]
        df_last_year = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_year) &
            (filtered_data['date_disbursed'] < start_of_year)
        ]
        df_last_2_years = filtered_data[
            (filtered_data['date_disbursed'] >= start_of_last_2_years) &
            (filtered_data['date_disbursed'] < start_of_last_year)
        ]

        # Aggregate data for IDs
        user_today_total = df_today['id'].count()
        user_yesterday_total = df_yesterday['id'].count()
        user_day_before_yesterday_total = df_day_before_yesterday['id'].count()
        user_week_total = df_week['id'].count()
        user_last_week_total = df_last_week['id'].count()
        user_last_2_weeks_total = df_last_2_weeks['id'].count()
        user_month_total = df_month['id'].count()
        user_last_month_total = df_last_month['id'].count()
        user_last_2_months_total = df_last_2_months['id'].count()
        user_year_total = df_year['id'].count()
        user_last_year_total = df_last_year['id'].count()
        user_last_2_years_total = df_last_2_years['id'].count()
        user_all_time_total = filtered_data['id'].count()

        # Aggregate data for disbursement
        disbursed_today_total = df_today['amount'].sum()
        disbursed_yesterday_total = df_yesterday['amount'].sum()
        disbursed_day_before_yesterday_total = df_day_before_yesterday['amount'].sum()
        disbursed_week_total = df_week['amount'].sum()
        disbursed_last_week_total = df_last_week['amount'].sum()
        disbursed_last_2_weeks_total = df_last_2_weeks['amount'].sum()
        disbursed_month_total = df_month['amount'].sum()
        disbursed_last_month_total = df_last_month['amount'].sum()
        disbursed_last_2_months_total = df_last_2_months['amount'].sum()
        disbursed_year_total = df_year['amount'].sum()
        disbursed_last_year_total = df_last_year['amount'].sum()
        disbursed_last_2_years_total = df_last_2_years['amount'].sum()
        disbursed_all_time_total = filtered_data['amount'].sum()

        # Aggregate data for portfolio outstanding
        open_port_today_total = df_today['open_portfolio'].sum()
        open_port_yesterday_total = df_yesterday['open_portfolio'].sum()
        open_port_day_before_yesterday_total = df_day_before_yesterday['open_portfolio'].sum()
        open_port_week_total = df_week['open_portfolio'].sum()
        open_port_last_week_total = df_last_week['open_portfolio'].sum()
        open_port_last_2_weeks_total = df_last_2_weeks['open_portfolio'].sum()
        open_port_month_total = df_month['open_portfolio'].sum()
        open_port_last_month_total = df_last_month['open_portfolio'].sum()
        open_port_last_2_months_total = df_last_2_months['open_portfolio'].sum()
        open_port_year_total = df_year['open_portfolio'].sum()
        open_port_last_year_total = df_last_year['open_portfolio'].sum()
        open_port_last_2_years_total = df_last_2_years['open_portfolio'].sum()
        open_port_all_time_total = filtered_data['open_portfolio'].sum()

        # Amount Due each period
        exp_till_today_total = exp_till_today_['exp_repay_today'].sum()
        exp_till_yesterday_total = exp_till_yesterday_['exp_repay_today'].sum()
        exp_till_ThisWeek_total = exp_till_ThisWeek_['exp_repay_today'].sum()
        exp_till_ThisMonth_total = exp_till_ThisMonth_['exp_repay_today'].sum()
        exp_till_ThisYear_total = exp_till_ThisYear_['exp_repay_today'].sum()
        exp_all_time_total = exp_all_time['exp_repay_today'].sum()

        # Missed Repayment each period
        missed_repayment_today = max(0, exp_till_today_total-repay_today_total)
        missed_repayment_yesterday = max(0, exp_till_yesterday_total-repay_yesterday_total)
        missed_repayment_this_week = max(0, exp_till_ThisWeek_total-repay_week_total)
        missed_repayment_this_month = max(0, exp_till_ThisMonth_total-repay_month_total)
        missed_repayment_this_year = max(0, exp_till_ThisYear_total-repay_year_total)
        missed_repayment_all_time = max(0, exp_all_time_total-repay_all_time_total)

    ######################################################################### Create function to give growth rate ####################################################################################

        # Function to calculate growth rate
        def calculate_growth_rate(current, previous):
            if previous == 0:  # Avoid division by zero
                return 0
            return ((current - previous) / previous) * 100

        # Compute growth rates for Repayments
        repay_growth_today = calculate_growth_rate(repay_today_total, repay_yesterday_total)
        repay_growth_yesterday = calculate_growth_rate(repay_yesterday_total, repay_day_before_yesterday_total)
        repay_growth_week = calculate_growth_rate(repay_week_total, repay_last_week_total)
        repay_growth_last_week = calculate_growth_rate(repay_last_week_total, repay_last_2_week_total)
        repay_growth_month = calculate_growth_rate(repay_month_total, repay_last_month_total)
        repay_growth_last_month = calculate_growth_rate(repay_last_month_total, repay_last_2_month_total)
        repay_growth_year = calculate_growth_rate(repay_year_total, repay_last_year_total)
        repay_growth_last_year = calculate_growth_rate(repay_last_year_total, repay_last_2_year_total)

        # Compute growth rates for Users
        user_growth_today = calculate_growth_rate(user_today_total, user_yesterday_total)
        user_growth_yesterday = calculate_growth_rate(user_yesterday_total, user_day_before_yesterday_total)
        user_growth_week = calculate_growth_rate(user_week_total, user_last_week_total)
        user_growth_last_week = calculate_growth_rate(user_last_week_total, user_last_2_weeks_total)
        user_growth_month = calculate_growth_rate(user_month_total, user_last_month_total)
        user_growth_last_month = calculate_growth_rate(user_last_month_total, user_last_2_months_total)
        user_growth_year = calculate_growth_rate(user_year_total, user_last_year_total)
        user_growth_last_year = calculate_growth_rate(user_last_year_total, user_last_2_years_total)

        # Compute growth rates for Disbursement
        disbursed_growth_today = calculate_growth_rate(disbursed_today_total, disbursed_yesterday_total)
        disbursed_growth_yesterday = calculate_growth_rate(disbursed_yesterday_total, disbursed_day_before_yesterday_total)
        disbursed_growth_week = calculate_growth_rate(disbursed_week_total, disbursed_last_week_total)
        disbursed_growth_last_week = calculate_growth_rate(disbursed_last_week_total, disbursed_last_2_weeks_total)
        disbursed_growth_month = calculate_growth_rate(disbursed_month_total, disbursed_last_month_total)
        disbursed_growth_last_month = calculate_growth_rate(disbursed_last_month_total, disbursed_last_2_months_total)
        disbursed_growth_year = calculate_growth_rate(disbursed_year_total, disbursed_last_year_total)
        disbursed_growth_last_year = calculate_growth_rate(disbursed_last_year_total, disbursed_last_2_years_total)

        # Compute growth rates for Open Portfolio
        open_port_growth_today = calculate_growth_rate(open_port_today_total, open_port_yesterday_total)
        open_port_growth_yesterday = calculate_growth_rate(open_port_yesterday_total, open_port_day_before_yesterday_total)
        open_port_growth_week = calculate_growth_rate(open_port_week_total, open_port_last_week_total)
        open_port_growth_last_week = calculate_growth_rate(open_port_last_week_total, open_port_last_2_weeks_total)
        open_port_growth_month = calculate_growth_rate(open_port_month_total, open_port_last_month_total)
        open_port_growth_last_month = calculate_growth_rate(open_port_last_month_total, open_port_last_2_months_total)
        open_port_growth_year = calculate_growth_rate(open_port_year_total, open_port_last_year_total)
        open_port_growth_last_year = calculate_growth_rate(open_port_last_year_total, open_port_last_2_years_total)

    ######################################################################### More customization using div class ####################################################################################

        # Custom CSS for bordered metrics
        st.markdown(
            """
            <style>
            .metric-container {
                border: 4px solid #4b9ca5; /* Sea green border */
                border-radius: 20%; /* Rounded corners */
                padding: 10px;
                text-align: center;
                margin: 15px;
                width: 100%;
                box-shadow: 3px 3px 5px rgba(0, 151, 167, 0.5); /* shadow */
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
            .metric-value {
                font-size: 14px;
                font-weight: bold;
                white-space: normal;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .metric-delta {
                font-size: 12px;
                font-weight: bold;
                margin-top: 5px;
                margin-bottom: 5px;
            }
            .metric-delta-positive {
                color: #4CAF50; /* Green for positive deltas */
            }
            .metric-delta-negative {
                color: #FF5252; /* Red for negative deltas */
            }
            .metric-title {
                font-size: 17px;
                color: #777;
                font-weight: bold;
                margin-bottom: 10px;
                white-space: normal;
                text-align: center;
            }
            .metric-label {
                font-size: 15px;
                color: #555;
                font-weight: lighter;
                white-space: normal;
                text-align: center;
            }
            </style>
            """, unsafe_allow_html=True
        )

        # Custom metric display function for entire category
        def bordered_category(title, metrics):
            st.markdown(f'<div class="metric-container"><div class="metric-title">{title}</div>', unsafe_allow_html=True)
            for label1, value1, delta1, label2, value2, delta2 in metrics:
                st.markdown(
                    f"""
                    <div style="display: flex; justify-content: space-between; width: 100%;">
                        <div style="text-align: center; flex: 1; margin-left:7%; padding: 10px;">
                            <div class="metric-label">{label1}</div>
                            <div class="metric-value">{value1}</div>
                            <div class='metric-delta {"metric-delta-positive" if delta1 >= 0 else "metric-delta-negative"}'>{delta1:+.2f}%</div>
                        </div>
                        <div style="text-align: center; flex: 1; padding: 10px;">
                            <div class="metric-label">{label2}</div>
                            <div class="metric-value">{value2}</div>
                            <div class='metric-delta {"metric-delta-positive" if delta2 >= 0 else "metric-delta-negative"}'>{delta2:+.2f}%</div>
                        </div>
                    </div>
                    <hr style="margin:0; width:90%; display:block; margin-left:10%; margin-right:0%;">
                    """, unsafe_allow_html=True
                )
            st.markdown("</div>", unsafe_allow_html=True)  # Close container

        # Create columns for each metric category
        col1, col2, col3  = st.columns(3)
        col4, col5, col6, col7  = st.columns(4)

        # USERS
        with col1:
            bordered_category("Users", [
                ("Today", f"{user_today_total:,} users", user_growth_today, "Yesterday", f"{user_yesterday_total:,} users", user_growth_yesterday),
                ("This Week", f"{user_week_total:,} users", user_growth_last_week, "This Month", f"{user_month_total:,} users", user_growth_last_month),
                ("This Year", f"{user_year_total:,} users", user_growth_last_year, "All-Time", f"{user_all_time_total:,} users", 0),
            ])

        # DISBURSEMENT
        with col2:
            bordered_category("Disbursement", [
                ("Today", f"N{disbursed_today_total:,.2f}", disbursed_growth_today, "Yesterday", f"N{disbursed_yesterday_total:,.2f}", disbursed_growth_yesterday),
                ("This Week", f"N{disbursed_week_total:,.2f}", disbursed_growth_last_week, "This Month", f"N{disbursed_month_total:,.2f}", disbursed_growth_last_month),
                ("This Year", f"N{disbursed_year_total:,.2f}", disbursed_growth_last_year, "All-Time", f"N{disbursed_all_time_total:,.2f}", 0),
            ])

        # PORTFOLIO OUTSTANDING
        with col3:
            bordered_category("Portfolio Outstanding", [
                ("Today", f"N{open_port_today_total:,.2f}", open_port_growth_today, "Yesterday", f"N{open_port_yesterday_total:,.2f}", open_port_growth_yesterday),
                ("This Week", f"N{open_port_week_total:,.2f}", open_port_growth_last_week, "This Month", f"N{open_port_month_total:,.2f}", open_port_growth_last_month),
                ("This Year", f"N{open_port_year_total:,.2f}", open_port_growth_last_year, "All-Time", f"N{open_port_all_time_total:,.2f}", 0),
            ])

        # REPAYMENT
        with col4:
            bordered_category("Repayment", [
                ("Today", f"N{repay_today_total:,.2f}", repay_growth_today, "Yesterday", f"N{repay_yesterday_total:,.2f}", repay_growth_yesterday),
                ("This Week", f"N{repay_week_total:,.2f}", repay_growth_last_week, "This Month", f"N{repay_month_total:,.2f}", repay_growth_last_month),
                ("This Year", f"N{repay_year_total:,.2f}", repay_growth_last_year, "All-Time", f"N{repay_all_time_total:,.2f}", 0),
            ])

        # EXPECTED COLLECTION
        with col5:
            bordered_category("Expected Collection", [
                ("Today", f"N{exp_till_today_total:,.2f}", ((repay_today_total/exp_till_today_total)*100).round(2), "Yesterday", f"N{exp_till_yesterday_total:,.2f}", ((repay_yesterday_total/exp_till_yesterday_total)*100).round(2)),
                ("This Week", f"N{exp_till_ThisWeek_total:,.2f}", ((repay_week_total/exp_till_ThisWeek_total)*100).round(2), "This Month", f"N{exp_till_ThisMonth_total:,.2f}", ((repay_month_total/exp_till_ThisMonth_total)*100).round(2)),
                ("This Year", f"N{exp_till_ThisYear_total:,.2f}", ((repay_year_total/exp_till_ThisYear_total)*100).round(2), "All-Time", f"N{exp_all_time_total:,.2f}", ((repay_all_time_total/exp_all_time_total)*100).round(2)),
            ])

        # MISSED REPAYMENT
        with col6:
            bordered_category("Missed Repayment", [
                ("Today", f"N{missed_repayment_today:,.2f}", ((missed_repayment_today/exp_till_today_total)*100).round(2), "Yesterday", f"N{missed_repayment_yesterday:,.2f}", ((missed_repayment_yesterday/exp_till_yesterday_total)*100).round(2)),
                ("This Week", f"N{missed_repayment_this_week:,.2f}", ((missed_repayment_this_week/exp_till_ThisWeek_total)*100).round(2), "This Month", f"N{missed_repayment_this_month:,.2f}", ((missed_repayment_this_month/exp_till_ThisMonth_total)*100).round(2)),
                ("This Year", f"N{missed_repayment_this_year:,.2f}", ((missed_repayment_this_year/exp_till_ThisYear_total)*100).round(2), "All-Time", f"N{missed_repayment_all_time:,.2f}", ((missed_repayment_all_time/exp_all_time_total)*100).round(2)),
            ])

        # PAST MATURITY
        with col7:
            bordered_category("Past Maturity", [
                ("Late Repayment", f"N{(past_maturity_7_days_sum + past_maturity_15_days_sum + past_maturity_30_days_sum):,.2f}", 0, "31 - 45 Days", f"N{past_maturity_45_days_sum:,.2f}", 0),
                ("46 - 60 Days", f"N{past_maturity_60_days_sum:,.2f}", 0, "61 - 90 Days", f"N{past_maturity_90_days_sum:,.2f}", 0),
                ("91 Days & Above", f"N{past_maturity_90_plus_days_sum:,.2f}", 0, "All-Time", f"N{(past_due_date["open_portfolio"].sum().round(2)):,.2f}", 0),
            ])

        st.markdown("---")


    ################################################################################ Repayment Progress ##########################################################################################

        # Filter today's repayment data
        filtered_repayment_df_today = filtered_repayment_df[filtered_repayment_df['paid_date'] == today]

        # Group by date and get sum of repayments
        repayment_made = filtered_repayment_df_today.groupby('paid_date', as_index=False)['repayment_by_date'].sum()

        # Get the total expected repayment (sum over all days in exp_till_today_)
        total_expected = exp_till_today_['exp_repay_today'].sum()

        # Add the expected repayment column
        repayment_made['exp_repay_today'] = total_expected

        # Progress in %
        repayment_made['progress_%'] = (repayment_made['repayment_by_date'] / repayment_made['exp_repay_today']) * 100

        # Display section
        st.markdown(f"### 💰 Repayment Progress for {today.strftime('%Y-%m-%d')}")

        # Create a nice progress display
        for _, row in repayment_made.iterrows():
            actual = row['repayment_by_date']
            expected = row['exp_repay_today']
            progress = row['progress_%']
            unpaid = 100 - progress

            # Display with better layout
            st.markdown(f"**Expected Repayment:** ₦{expected:,.2f} | **Actual Repayment:** ₦{actual:,.2f}")

            # Stylish progress bar with both paid & unpaid
            paid_color = "#28a745" if progress >= 100 else "#ffc107" if progress >= 70 else "#dc3545"
            unpaid_color = "#e0e0e0"

            st.markdown(
                f"""
                <div style="background-color:#f0f0f0; border-radius:20px; height:30px; overflow:hidden; border: 1px solid #ddd; display:flex;">
                    <div style="width:{min(progress, 100):.2f}%; background-color:{paid_color}; height:100%;
                                display:flex; align-items:center; justify-content:center; color:white; font-weight:bold;">
                        {progress:.2f}% Collected
                    </div>
                    <div style="width:{max(unpaid, 0):.2f}%; background-color:{unpaid_color}; height:100%;
                                display:flex; align-items:center; justify-content:center; color:black; font-weight:bold;">
                        {unpaid:.2f}% Not Collected
                    </div>
                </div>
                """,
                unsafe_allow_html=True
            )

        st.markdown("---")

    ################################################################################ Charts and Plots ##########################################################################################

        # Cached Chart Generators
        @st.cache_data(show_spinner=False)
        def generate_loan_status_chart(filtered_df):
            status_counts = filtered_df['status'].value_counts()
            fig = px.pie(
                names=status_counts.index,
                values=status_counts.values,
                title="Loan Status Distribution",
                color_discrete_sequence=px.colors.sequential.Viridis
            )
            fig.update_traces(hoverinfo="label+percent+value")
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",
                plot_bgcolor="rgba(0, 0, 0, 0)",
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}
            )
            return fig

        @st.cache_data(show_spinner=False)
        def generate_loan_type_chart(filtered_df):
            loan_type_counts = filtered_df['loan_type'].value_counts()
            fig = px.pie(
                names=loan_type_counts.index,
                values=loan_type_counts.values,
                title="Loan Type Distribution",
                color_discrete_sequence=px.colors.sequential.Cividis,
                hole=0.5
            )
            fig.update_traces(hoverinfo="label+percent+value")
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",
                plot_bgcolor="rgba(0, 0, 0, 0)",
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}
            )
            return fig

        # Loan Status and Performance Distribution
        col1, col2 = st.columns(2)

        with col1:
            st.plotly_chart(generate_loan_status_chart(filtered_data), use_container_width=True)

        with col2:
            st.plotly_chart(generate_loan_type_chart(filtered_data), use_container_width=True)

        st.markdown("---")

        # Copy dataset to a new variable to summarize outlook
        filtered_dataset = filtered_data.copy()

        # Select and arrange all repayment data columns
        filtered_dataset = filtered_dataset[[
            'verticals', 'vertical_lead_name', 'vertical_lead_email', 'vertical_lead_contact',
            'sup_name', 'sup_email', 'sup_contact', 'location', 'sub_location', 'agent_full_name', 'agent_contact',
            'agent_email', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'guarantor_phone_number',
            'status', 'loan_type', 'date_disbursed', 'start_date', 'end_date', 'amount', 'interest_amount', 'daily_repayment_amount', 'repayment',
            'exp_repay_today', 'repayment_rate', 'expected_repayment', 'completion_rate', 'open_portfolio', 'missed_days',
            'missed_repayment', 'days_left_to_maturity', 'loan_age(days)', 'loan_age(months)', 'num_of_month'

        ]]

        # Rename columns for clarity
        data_column_mappings = {
            "verticals": "Vertical",
            "vertical_lead_name": "Vertical Lead",
            "vertical_lead_email": "VL email",
            "vertical_lead_contact": "VL contact",
            "sup_name": "Supervisor",
            "sup_email": "Supervisor email",
            "sup_contact": "Supervisor contact",
            "location": "Branch",
            "sub_location": "Sub-Branch",
            "agent_full_name": "Loan Officer",
            "agent_email": "LO email",
            "agent_contact": "LO contact",
            "users_full_name": "Name",
            "users_phone_number": "Contact",
            "users_address": "Address",
            "users_trade": "Business",
            "guarantor_phone_number": "Guarantor Contact",
            "status": "Status",
            "loan_type": "Loan Type",
            "date_disbursed": "Disbursed Date",
            "start_date": "Start Date",
            "end_date": "Maturity Date",
            "amount": "Principal",
            "interest_amount": "Interest",
            "daily_repayment_amount": "Daily Repayment",
            "repayment": "Collection",
            "exp_repay_today": "Expected Collection",
            "repayment_rate": "Collection rate (%)",
            "expected_repayment": "Total Due",
            "completion_rate": "Completion rate (%)",
            "open_portfolio": "Pending Due",
            "missed_days": "Days Missed",
            "missed_repayment": "Missed Amount",
            "days_left_to_maturity": "Days Left",
            "loan_age(days)": "Loan Age (Days)",
            "loan_age(months)": "Loan Age (Months)",
            "num_of_month": "Duration (Months)"
        }

        filtered_dataset.rename(columns=data_column_mappings, inplace=True)

        with st.expander("Click to see all loans"):
            st.dataframe(filtered_dataset)
            if filtered_dataset.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                st.success(f"Displaying {len(filtered_dataset)} records.")

    ############################################################################ Tab 2 #######################################################################################

    with tab2:

    ############################################################## Gets 'exp_repay_column' for progress checker #################################################################################

        try:
            # Drop 'exp_repay_today'
            new_filtered_data = filtered_data.drop(columns=['exp_repay_today'])

            # Convert date column to datetime if not already
            schedule_df['date'] = pd.to_datetime(schedule_df['date'])

            # Aggregate exp_repay_today by month
            monthly_exp_repay = (schedule_df.groupby(pd.Grouper(key='date', freq='M')).agg({'exp_repay_today' : 'sum'}).round(2).reset_index())

            monthly_exp_repay['date'] = monthly_exp_repay['date'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            sales_trend = new_filtered_data.groupby(pd.Grouper(key='date_disbursed', freq='M')).agg({
                'amount': 'sum',
                'interest_amount': 'sum',
                'num_of_month': 'sum',
                'repayment': 'sum',
                'expected_repayment': 'sum',
                'due_today_amount': 'sum',
                'open_portfolio': 'sum',
            }).round(2).reset_index()

            sales_trend['date_disbursed'] = sales_trend['date_disbursed'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            active_trend = active_portfolio.groupby(pd.Grouper(key='date_disbursed', freq='M')).agg({
                'amount': 'sum',
                'interest_amount': 'sum',
                'repayment': 'sum',
                'expected_repayment': 'sum',
                'due_today_amount': 'sum',
                'open_portfolio': 'sum'
            }).round(2).reset_index()

            active_trend['date_disbursed'] = active_trend['date_disbursed'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            # Repayments (Grouping Repaymenrts by months)
            repayment_trend = filtered_repayment_df.groupby(pd.Grouper(key='paid_date', freq='M'))[['repayment_by_date']].sum().reset_index()
            repayment_trend['paid_date'] = repayment_trend['paid_date'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            all_trend = sales_trend.merge(repayment_trend, left_on = 'date_disbursed', right_on = 'paid_date', how='left')

            # Merge data with exp_repay_today
            all_trend = all_trend.merge(monthly_exp_repay, left_on="date_disbursed", right_on='date', how="left")

            # Cummulative exp-repayment
            all_trend["cumulative_exp_repay_today"] = all_trend["exp_repay_today"].cumsum()  # Compute cumulative sum

            # Gets monthly repayment for loan
            all_trend['monthly_repayment'] = all_trend['expected_repayment'] / all_trend['num_of_month']

            # Gets interest monthly repayment
            all_trend['int_monthly_repayment'] = all_trend['interest_amount'] / all_trend['num_of_month']

            # Gets principal monthly repayment
            all_trend['prin_monthly_repayment'] = all_trend['amount'] / all_trend['num_of_month']

            # Gets interest element percentage
            all_trend['interest_element_pct'] = (all_trend['int_monthly_repayment'] / all_trend['monthly_repayment']).round(2)

            # Gets principal element percentage
            all_trend['principal_element_pct'] = (all_trend['prin_monthly_repayment'] / all_trend['monthly_repayment']).round(2)

            # Interest element
            all_trend['interest_element'] = (all_trend['interest_element_pct'] * all_trend['repayment_by_date']).round(2)

            # Principal collected
            all_trend['principal_element'] = (all_trend['principal_element_pct'] * all_trend['repayment_by_date']).round(2)

            # Selecting relevant columns and converting date_disbursed to datetime
            df_selected = past_due_date[['date_disbursed', 'open_portfolio', 'Loss_Category']].copy()
            df_selected['date_disbursed'] = pd.to_datetime(df_selected['date_disbursed'], errors='coerce')

            # Dropping rows with NaT values in date_disbursed
            df_selected = df_selected.dropna(subset=['date_disbursed'])

            # Grouping by month and Loss_Category, then summing open_portfolio
            past_due_date_trend_2 = df_selected.groupby(
                [pd.Grouper(key='date_disbursed', freq='M'), 'Loss_Category']
            )[['open_portfolio']].sum().reset_index()

            # Compute cumulative sum for each Loss_Category
            past_due_date_trend_2['cumulative_open_portfolio'] = (
                past_due_date_trend_2.groupby('Loss_Category')['open_portfolio'].cumsum()
            )

            # Formatting date to show 'Month Year'
            past_due_date_trend_2['date_disbursed'] = past_due_date_trend_2['date_disbursed'].dt.strftime('%b %Y')

            # Past maturity by month (cohort)
            ## Loans past due date
            past_due_date_trend = past_due_date.groupby(
                [pd.Grouper(key='date_disbursed', freq='M'), 'Loss_Category']
            )[['open_portfolio']].sum().reset_index()
            past_due_date_trend['date_disbursed'] = past_due_date_trend['date_disbursed'].dt.strftime('%b %Y')
            loans_groups = new_filtered_data.groupby(pd.Grouper(key='date_disbursed', freq='M'))[['amount']].sum().reset_index()
            loans_groups['date_disbursed'] = loans_groups['date_disbursed'].dt.strftime('%b %Y')  # Format: "Jan 2025"
            past_due_date_trend = past_due_date_trend.merge(loans_groups, on = 'date_disbursed')

            all_trend["cumulative_open_portfolio"] = all_trend["open_portfolio"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_amount"] = all_trend["amount"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_due_today_amount"] = all_trend["due_today_amount"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_expected_repayment"] = all_trend["expected_repayment"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_repayment"] = all_trend["repayment_by_date"].cumsum()  # Compute cumulative sum
            all_trend['cum_open_portfolio'] = all_trend['cumulative_expected_repayment'] - all_trend['cumulative_repayment'] # cum open portfolio
            all_trend["cumulative_interest"] = all_trend["interest_amount"].cumsum()  # Compute cumulative sum
            all_trend["cumulative_exp_repay"] = all_trend["expected_repayment"].cumsum()  # Compute cumulative sum
            active_trend["cumulative_open_portfolio"] = active_trend["open_portfolio"].cumsum()  # Compute cumulative sum
            active_trend["cumulative_amount"] = active_trend["amount"].cumsum()  # Compute cumulative sum
            active_trend["cumulative_repayment"] = active_trend["repayment"].cumsum()  # Compute cumulative sum
            past_due_date_trend["cumulative_open_portfolio"] = past_due_date_trend["open_portfolio"].cumsum()  # Compute cumulative sum
            past_due_date_trend["cumulative_amount"] = past_due_date_trend["amount"].cumsum()  # Compute cumulative sum

            new_filtered_data['month'] = new_filtered_data['date_disbursed'].dt.strftime('%b %Y')
            grouped_monthly = new_filtered_data.groupby('month').agg(
                    repayment_amount=('repayment', 'sum'),
                    due_today=('due_today_amount', 'sum')).reset_index()

            grouped_by_month = all_trend.merge(grouped_monthly, how='left', left_on= 'date_disbursed', right_on = 'month')

            escrow_df['month'] = escrow_df['date_created'].dt.strftime('%b %Y')
            escrow_bal_by_month = escrow_df.groupby('month').agg(
                    available_bal=('available_balance', 'sum')).reset_index()
            escrow_bal_by_month['month'] = escrow_bal_by_month['month'].str.strip()

            # Generate a list of all months from Jan 2024 to Dec 2030 (or further as needed)
            start_date = pd.to_datetime("January 2024")
            end_date_ = pd.to_datetime("December 2030")
            date_range = pd.date_range(start=start_date, end=end_date_, freq='MS')  # 'MS' = month start frequency
            date_order = [date.strftime('%b %Y') for date in date_range]

            # Ensure the 'Month' column follows the desired order
            escrow_bal_by_month['month'] = pd.to_datetime(escrow_bal_by_month['month'], format='%b %Y')
            escrow_bal_by_month['month'] = pd.Categorical(escrow_bal_by_month['month'].dt.strftime('%b %Y'), categories=date_order, ordered=True)

            # Ensure the 'Month' column follows the desired order
            grouped_by_month['month'] = pd.to_datetime(grouped_by_month['month'], format='%b %Y')
            grouped_by_month['month'] = pd.Categorical(grouped_by_month['month'].dt.strftime('%b %Y'), categories=date_order, ordered=True)

            df1_sorted = grouped_by_month.sort_values("month")
            df2_sorted = escrow_bal_by_month.sort_values("month")

            escrow_merged_df = pd.merge(df1_sorted, df2_sorted, on = 'month')
            escrow_merged_df['plus_escrow'] = escrow_merged_df['repayment_by_date'] + escrow_merged_df['available_bal']

            # Adds cumulative repayment plus escrow
            escrow_merged_df["cumulative_plus_escrow"] = escrow_merged_df["plus_escrow"].cumsum()  # Compute cumulative sum

            # Sees the portfolio at the end of that current month
            open_port_repay = repayment_df.groupby(
                [pd.Grouper(key='paid_date', freq='M'), 'ajo_loan_id']
            )[['repayment_by_date']].sum().reset_index()
            open_port_repay['paid_date'] = open_port_repay['paid_date'].dt.strftime('%b %Y')

            # Gets open portfolio for that specified month alone
            open_port_filtered_df = new_filtered_data.groupby([pd.Grouper(key='date_disbursed', freq='M'), 'id'])[['amount', 'expected_repayment']].sum().reset_index()
            open_port_filtered_df['date_disbursed'] = open_port_filtered_df['date_disbursed'].dt.strftime('%b %Y')  # Format: "Jan 2025"

            # Merges both loans and repayment to see open portfolio for that poarticular month alonre
            monthly_open_portfolio = open_port_filtered_df.merge(open_port_repay, left_on=['date_disbursed', 'id'], right_on=['paid_date', 'ajo_loan_id'], how='left')

            # Groups by date_disbursed and get open portfolio for that particular month alone
            grp_mth_open_port = monthly_open_portfolio.groupby('date_disbursed')[['amount', 'repayment_by_date','expected_repayment']].sum().reset_index()

            # Calculate open portfolio
            grp_mth_open_port['open_portfolio'] = grp_mth_open_port['expected_repayment'] - grp_mth_open_port['repayment_by_date']

            # Generate a list of all months from Feb 2024 to Dec 2030 (or further as needed)
            start_date = pd.to_datetime("Feb 2024")
            end_date_ = pd.to_datetime("Dec 2030")
            date_range = pd.date_range(start=start_date, end=end_date_, freq='MS')  # 'MS' = month start frequency
            month_year_order = [date.strftime('%b %Y') for date in date_range]

            # Ensure the 'Month' column follows the desired order
            grp_mth_open_port['date_disbursed'] = pd.to_datetime(grp_mth_open_port['date_disbursed'], format='%b %Y')
            grp_mth_open_port['date_disbursed'] = pd.Categorical(grp_mth_open_port['date_disbursed'].dt.strftime('%b %Y'), categories=month_year_order, ordered=True)

            # Sort the DataFrame by the ordered 'Month' column
            grp_mth_open_port = grp_mth_open_port.sort_values('date_disbursed').reset_index(drop=True)

            grp_mth_open_port['cumulative_open_portfolio'] = grp_mth_open_port['open_portfolio'].cumsum()

            # Create two columns for the layout
            col9, col10 = st.columns(2)

            with col9:
                with st.container():
                    # Loans Trends
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y="amount",
                        title="Disbursement vs Outstanding Portfolio (Cohort)",
                    )

                    fig9.update_traces(mode="lines+markers")

                    # Add a separate trace for 'open_portfolio' (not affected by repayment of other months)
                    fig9.add_trace(
                        px.line(
                            grp_mth_open_port,
                            x="date_disbursed",
                            y="open_portfolio"
                        ).data[0]  # Extract the trace from the new figure
                    )

                    # Update the separate 'open_portfolio' trace to make it visually distinct
                    fig9.data[-1].name = "Total open_portfolio"  # Rename in legend
                    fig9.data[-1].line.color = "red"

                    fig9.update_traces(mode="lines+markers")

                    fig9.update_layout(
                        xaxis_title="Date",
                        yaxis_title="Disbursement / Oustanding Portfolio",
                        legend_title="open_portfolio",
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This plot illustrates the relationship between loan disbursements and the outstanding portfolio month on month.<br>
                        - Disbursement: Represents the amount of loans issued at end end of each month.<br>
                        - Outstanding Portfolio: Indicates the monthly total outstanding loan (money not collected yet from loans disbursed that month).
                        </p>
                        """, unsafe_allow_html=True)

            with col10:
                with st.container():
                    # Loans Trends
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["cumulative_amount", "cum_open_portfolio"],
                        title="Disbursement vs Open Portfolio (Cumulative)",
                    )

                    fig9.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig9.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This plot presents the cumulative trend of loan disbursements and the open portfolio over time.<br>
                        - Cumulative Amount: The total amount of loans disbursed up to date.<br>
                        - Cumulative Open Portfolio: The total outstanding loan balance up to date.<br>
                        This helps visualize long-term loan growth and portfolio changes and possible drift from loaned amount as we keep disbursing.
                        </p>
                        """, unsafe_allow_html=True)

            col3, col4 = st.columns(2)

            with col3:
                # Expected Repayment Trends
                with st.container():
                    fig5 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["repayment", "expected_repayment", "due_today_amount"],
                        title="Repayment Trends (Cohort)",
                        labels={"expected_repayment": "Expected Repayment"},
                    )
                    fig5.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig5.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig5)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This cohort plot displays the repayment trends for loans disbursed that month alone <br>
                        It displays the loan repayment amount 'at as the end_date' vs the expected collection 'as at today' vs total amount paid as of today <br>
                        - <b>Total Paid:</b> Amount repaid for each loan disbursed that month over time <br>
                        - <b>Expected Repayment:</b> The expected amount to be repaid by the end date/maturity date <br>
                        - <b>Due Today:</b> The amount due for repayment today for the loans disbursed that month alone <br>
                        This plot show a straight line for previous months that have all/almost all their loans fully paid indicating that those loans expectations are met <br>
                        The comparison helps in tracking repayment progress and identifying any shortfalls.
                        </p>
                        """, unsafe_allow_html=True)

            with col4:
                # Principal and interest element Trends
                with st.container():
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["principal_element","interest_element"],
                        title="Repayment (Principal vs Interest Element) Trends",
                    )

                    fig9.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig9.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This plot highlights the repayment breakdown between principal and interest elements:<br>
                        - <b>Principal Element:</b> The portion of repayment allocated to amount disbursed.<br>
                        - <b>Interest Element:</b> The portion of repayment allocated to interest on those loans.
                        </p>
                        """, unsafe_allow_html=True)

            # Create two columns for the layout
            col7, col8 = st.columns(2)

            with col7:
                with st.container():
                    fig6 = px.line(
                        escrow_merged_df,
                        x="month",
                        y=["repayment_by_date", "plus_escrow", "exp_repay_today"],
                        title="Repayment plus escrow",
                        labels={"returning_users": "Returning_borrowers"},
                    )

                    fig6.update_traces(mode="lines+markers")

                    # Update layout to reduce background opacity
                    fig6.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig6)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This plot tracks repayment trends alongside escrow of loans repayment<br>
                        - <b>Repayment:</b> Amount paid each month.<br>
                        - <b>Plus Escrow:</b> Amount paid each month plus their respective escrow amount.<br>
                        - <b>exp_repay_today:</b> The amount we should collect each month.
                        </p>
                        """, unsafe_allow_html=True)

            with col8:
                with st.container():
                    # Loans Trends
                    fig9 = px.line(
                        all_trend,
                        x="date_disbursed",
                        y=["amount", "repayment_by_date", "expected_repayment"],
                        title="Loans Trends (Cohort)",
                    )

                    # Update layout to reduce background opacity
                    fig9.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    # Customizing hover text
                    fig9.update_traces(
                        mode="lines+markers",
                        hovertemplate="<b>Date:</b> %{x}<br>" +
                                    "<b>Amount:</b> %{y:,.2f}<br>" +
                                    "<b>Repayment by Date:</b> %{customdata[0]:,.2f}<br>" +
                                    "<b>Expected Repayment:</b> %{customdata[1]:,.2f}<br>" +
                                    "<b>Interest Rate:</b> %{customdata[2]:.2%}<br>" +
                                    "<b>Completion Rate:</b> %{customdata[3]:.2%}<extra></extra>",
                        customdata=np.column_stack([
                            all_trend["repayment_by_date"],
                            all_trend["expected_repayment"],
                            (all_trend["expected_repayment"] / all_trend["amount"]) - 1.0,
                            all_trend["repayment_by_date"] / all_trend["expected_repayment"]
                        ])
                    )

                    # Add a separate trace for 'open_portfolio' (not affected by repayment of other months)
                    fig9.add_trace(
                        px.line(
                            grp_mth_open_port,
                            x="date_disbursed",
                            y="open_portfolio"
                        ).data[0]  # Extract the trace from the new figure
                    )

                    # Update the separate 'open_portfolio' trace to make it visually distinct
                    fig9.data[-1].name = "Total open_portfolio"  # Rename in legend
                    fig9.data[-1].line.color = "red"

                    fig9.update_traces(mode="lines+markers")

                    fig9.update_layout(
                        xaxis_title="Date",
                        yaxis_title="Disbursement / Open Portfolio",
                        legend_title="open_portfolio category",
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig9)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart tracks loan disbursement trends along with repayment progress:<br>
                        - <b>Amount:</b> The total loans disbursed for each month.<br>
                        - <b>Repayment:</b> Actual repayments made that month.<br>
                        - <b>Expected Repayment:</b> The scheduled repayments for issued loans.<br>
                        - <b>Open Portfolio:</b> The amount of loans still outstanding.<br>
                        </p>
                        """, unsafe_allow_html=True)

            # Create two columns for the layout
            col11, col12 = st.columns(2)

            with col11:
                with st.container():
                    fig10 = px.line(
                        past_due_date_trend,
                        x="date_disbursed",
                        y="open_portfolio",
                        color="Loss_Category",  # Only open_portfolio is affected by hue
                        title="Past Maturity Trends (Cohort)",
                        markers=True
                    )

                    # Update layout to reduce background opacity
                    fig10.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig10)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart tracks past maturity trends by showing:<br>
                        - <b>Open Portfolio:</b> Active loans that remain unpaid after the due date.<br>
                        - <b>PAR (Portfolio At Risk) Category:</b> Loans grouped by risk levels (days after end date).<br>
                        <br>
                        Each line represents a different risk category, showing the open portfolio trend over time.
                        </p>
                        """, unsafe_allow_html=True)

            with col12:
                with st.container():
                    # Open Port folio
                    fig10 = px.line(
                        past_due_date_trend_2,
                        x="date_disbursed",
                        y="cumulative_open_portfolio",
                        color="Loss_Category",  # Only open_portfolio is affected by hue
                        title="Past Maturity Trends (Cumulative)",
                        markers=True
                    )

                    # Update layout to reduce background opacity
                    fig10.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig10)

                    # Hidden description inside an expander
                    with st.expander("ℹ️ Click to see description"):
                        st.markdown("""
                        <p style="font-size: 14px;">
                        This chart visualizes the cumulative open portfolio of loans past their maturity date, categorized by risk levels:<br>
                        - <b>Cumulative Open Portfolio:</b> The cumulative outstanding loan balance that has exceeded its due date.<br>
                        - <b>PAR (Portfolio At Risk) Category:</b> Loans grouped based on risk classification.<br>
                        </p>
                        """, unsafe_allow_html=True)
            pass
        except (ValueError, IndexError, KeyError, AttributeError):
            st.warning("Not enough dataset to load this, Kindly skip.")

    ################################################################################### Tab 3 #################################################################################

    with tab3:

    ################################################################################### Copy Datasets #################################################################################

        # Copy fiiltered_data as dataframe
        loan_disk_df = new_filtered_data.copy()

        # Copy filtered_repayment_df as dataframe
        loan_disk_repay_df = filtered_repayment_df.copy()

        # Copy past_due_date as dataframe
        loan_disk_past_mat_df = past_due_date.copy()

        # Copy fiiltered_data as dataframe
        loan_disk_missed_repay_df = new_filtered_data.copy()
        # Filters only missed repayment
        loan_disk_missed_repay_df = loan_disk_missed_repay_df[loan_disk_missed_repay_df['missed_days'] > 0]

        # Completed_loan_disk as dataframe
        completed_loan_disk = new_filtered_data.copy()
        # Filters only completed loans
        completed_loan_disk = completed_loan_disk[completed_loan_disk['status'] == 'COMPLETED']

        # Select Supervisor from unique list
        selected_supervisor = st.selectbox("Select Supervisor", pd.Series(loan_disk_df['sup_name'].unique()).sort_values(), placeholder="Select or Enter Supervisor To Begin...", index=None)

        if selected_supervisor:
            # Filter loans based on selected supervisor
            filtered_loan_disk_loans = loan_disk_df[loan_disk_df['sup_name'] == selected_supervisor]

            # Get unique agents under the selected supervisor
            unique_agents = pd.Series(filtered_loan_disk_loans['agent_full_name'].unique()).sort_values()

            # Select Agent (Conditional: Only appears when a supervisor is selected)
            selected_agent = st.selectbox("Select Agent", ["All Agents"] + list(unique_agents), index=0)

            # Further filter by agent (if an agent is selected and not 'All Agents')
            if selected_agent != "All Agents":
                filtered_loan_disk_loans = filtered_loan_disk_loans[filtered_loan_disk_loans['agent_full_name'] == selected_agent]

            # Get loan IDs after filtering by supervisor (and agent if selected)
            loan_ids = filtered_loan_disk_loans['id'].unique()

            # Filter other datasets using loan IDs
            filtered_loan_disk_repayment_df = loan_disk_repay_df[loan_disk_repay_df['ajo_loan_id'].isin(loan_ids)]
            filtered_loan_disk_past_mat_df = loan_disk_past_mat_df[loan_disk_past_mat_df['id'].isin(loan_ids)]
            filtered_loan_disk_missed_repay_df = loan_disk_missed_repay_df[loan_disk_missed_repay_df['id'].isin(loan_ids)]
            filtered_loan_disk_completed_df = completed_loan_disk[completed_loan_disk['id'].isin(loan_ids)]
            filtered_exp_till_today_ = exp_till_today_[exp_till_today_['id'].isin(loan_ids)]
            filtered_exp_till_yesterday_ = exp_till_yesterday_[exp_till_yesterday_['id'].isin(loan_ids)]
            filtered_exp_till_ThisWeek_ = exp_till_ThisWeek_[exp_till_ThisWeek_['id'].isin(loan_ids)]
            filtered_exp_till_ThisMonth_ = exp_till_ThisMonth_[exp_till_ThisMonth_['id'].isin(loan_ids)]
            filtered_exp_till_ThisYear_ = exp_till_ThisYear_[exp_till_ThisYear_['id'].isin(loan_ids)]
            filtered_exp_all_time = exp_all_time[exp_all_time['id'].isin(loan_ids)]

            st.markdown("---")

    ######################################################################### Loan summarised metrics ####################################################################################

            # Further filter by agent (if an agent is selected and not 'All Agents')
            if selected_agent != "All Agents":
                # Custom CSS for bordered metrics
                st.markdown(
                    """
                    <style>
                    ._metric-container {
                        border: 4px solid #4b9ca5; /* Sea green border */
                        border-radius: 0px;
                        padding: 10px;
                        text-align: center;
                        margin: 15px;
                        width: 100%;
                        box-shadow: 3px 3px 5px rgba(0, 151, 167, 0.5); /* shadow */
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        word-wrap: break-word;
                        overflow-wrap: break-word;
                    }
                    ._metric-value {
                        font-size: 20px;
                        font-weight: bold;
                        white-space: normal;
                        max-width: 100%;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    ._metric-title {
                        font-size: 30px;
                        color: #777;
                        font-weight: bold;
                        margin-bottom: 10px;
                        white-space: normal;
                        text-align: center;
                    }
                    ._metric-label {
                        font-size: 17px;
                        color: #555;
                        font-weight: bold;
                        white-space: normal;
                        text-align: center;
                    }
                    ._metric-name {
                        font-size: 17px;
                        font-weight: lighter;
                        white-space: normal;
                        text-align: center;
                    }
                    </style>
                    """, unsafe_allow_html=True
                )

                # Custom metric display function for entire category
                def bordered_category_(metrics):
                    st.markdown(
                        """
                        <div style='text-align:center; font-size:15px;'>
                            <h1 style='text-shadow: 3px 3px 6px rgba(32, 178, 170, 0.8);'>
                                <strong>Seeds Metrics</strong>
                            </h1>
                        </div>
                        """,
                        unsafe_allow_html=True
                    )
                    for label1, name1, value1, label2, name2, value2, label3, name3, value3, label4, name4, value4 in metrics:
                        st.markdown(
                            f"""
                            <div style="display: flex; justify-content: space-between; width: 100%;">
                                <div style="text-align: center; flex: 1; padding: 10px;">
                                <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                    <div class="_metric-label">{label1}</div>
                                    <div class="_metric-name">{name1}</div>
                                    <div class="_metric-value">{value1}</div>
                                <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                                </div>
                                <div style="text-align: center; flex: 1; padding: 10px;">
                                <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                <div class="_metric-label">{label2}</div>
                                    <div class="_metric-name">{name2}</div>
                                    <div class="_metric-value">{value2}</div>
                                <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                                </div>
                                <div style="text-align: center; flex: 1; padding: 10px;">
                                <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                    <div class="_metric-label">{label3}</div>
                                    <div class="_metric-name">{name3}</div>
                                    <div class="_metric-value">{value3}</div>
                                <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                                </div>
                                <div style="text-align: center; flex: 1; padding: 10px;">
                                <hr style="margin:10px; width:100%; display:block; padding: 10px;">
                                    <div class="_metric-label">{label4}</div>
                                    <div class="_metric-name">{name4}</div>
                                    <div class="_metric-value">{value4}</div>
                                <hr style="margin:10px; width:100%; display:block; padding: 0px;">
                                </div>
                            </div>
                            """, unsafe_allow_html=True
                        )
                    st.markdown("</div>", unsafe_allow_html=True)  # Close container

                # Filter datasets for loans by paid_date
                LD_repay_df_today = filtered_loan_disk_repayment_df[filtered_loan_disk_repayment_df['paid_date'] == today]
                LD_repay_df_yesterday = filtered_loan_disk_repayment_df[filtered_loan_disk_repayment_df['paid_date'] == yesterday]
                LD_repay_df_month = filtered_loan_disk_repayment_df[filtered_loan_disk_repayment_df['paid_date'] >= start_of_month]

                # Aggregate data for repayment
                LD_repay_today_total = LD_repay_df_today['repayment_by_date'].sum()
                LD_repay_yesterday_total = LD_repay_df_yesterday['repayment_by_date'].sum()
                LD_repay_month_total = LD_repay_df_month['repayment_by_date'].sum()
                LD_repay_all_time_total = filtered_loan_disk_repayment_df['repayment_by_date'].sum()

                # Filter datasets for loans by date_disbursed
                LD_df_today = filtered_loan_disk_loans[filtered_loan_disk_loans['date_disbursed'] == today]
                LD_df_yesterday = filtered_loan_disk_loans[filtered_loan_disk_loans['date_disbursed'] == yesterday]
                LD_df_month = filtered_loan_disk_loans[filtered_loan_disk_loans['date_disbursed'] >= start_of_month]

                # Aggregate data for disbursement
                LD_disbursed_today_total = LD_df_today['amount'].sum()
                LD_disbursed_yesterday_total = LD_df_yesterday['amount'].sum()
                LD_disbursed_month_total = LD_df_month['amount'].sum()
                LD_disbursed_all_time_total = filtered_loan_disk_loans['amount'].sum()

                LD_active_users = filtered_loan_disk_loans[filtered_loan_disk_loans['status']=='OPEN'] # Filter data for active users
                LD_active_users = LD_active_users['id'].count() # Gets count of active users
                LD_outstanding_loans = filtered_loan_disk_loans['open_portfolio'].sum() # Aggregate data for outstanding loans
                LD_past_mat_all_time_total = filtered_loan_disk_past_mat_df['open_portfolio'].sum() # Aggregate data for past maturity
                LD_missed_repay_all_time_total = filtered_loan_disk_missed_repay_df['missed_repayment'].sum() # Aggregate data for missed repayment
                filtered_exp_till_today_ = filtered_exp_till_today_['exp_repay_today'].sum()
                filtered_exp_till_yesterday_ = filtered_exp_till_yesterday_['exp_repay_today'].sum()
                filtered_exp_till_ThisWeek_ = filtered_exp_till_ThisWeek_['exp_repay_today'].sum()
                filtered_exp_till_ThisMonth_ = filtered_exp_till_ThisMonth_['exp_repay_today'].sum()
                filtered_exp_till_ThisYear_ = filtered_exp_till_ThisYear_['exp_repay_today'].sum()
                filtered_exp_all_time = filtered_exp_all_time['exp_repay_today'].sum()

                bordered_category_([
                    ("Today", "Loan Disbursed", f"N{LD_disbursed_today_total:,.2f}", "Yesterday", "Loan Disbursed", f"N{LD_disbursed_yesterday_total:,.2f}", "This Month", "Loan Disbursed", f"N{LD_disbursed_month_total:,.2f}", "All-Time", "Loan Disbursed", f"N{LD_disbursed_all_time_total:,.2f}"),
                    ("Today", "Loan Paid", f"N{LD_repay_today_total:,.2f}", "Yesterday", "Loan Paid", f"N{LD_repay_yesterday_total:,.2f}", "This Month", "Loan Paid", f"N{LD_repay_month_total:,.2f}", "All-Time", "Loan Paid", f"N{LD_repay_all_time_total:,.2f}"),
                    ("Today", "Due Collections", f"N{filtered_exp_till_today_:,.2f}", "Yesterday", "Due Collections", f"N{filtered_exp_till_yesterday_:,.2f}", "This Month", "Due Collections", f"N{filtered_exp_till_ThisMonth_:,.2f}", "All-Time", "Due Collections", f"N{filtered_exp_all_time:,.2f}"),
                    ("All-Time", "Total Active Borrowers", f"{LD_active_users:,.0f} users", "All-time", "Missed Repayment", f"N{LD_missed_repay_all_time_total:,.2f}", "All-time", "Outstanding Loan", f"N{LD_outstanding_loans:,.2f}", "All-Time", "Loans Past Maturity", f"N{LD_past_mat_all_time_total:,.2f}"),
                ])

    ######################################################################### Gets dataframe by Date filters ####################################################################################

            else:
                # Filter loans for today alone
                loans_disk_df_today = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] == today]

                # Filter loans for yesterday alone
                loans_disk_df_yesterday = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] == yesterday]

                # Filter loans from this week alone
                loans_disk_df_this_week = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] >= start_of_week]

                # Filter loans from this month alone
                loans_disk_df_this_month = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] >= start_of_month]

                # Filter loans from this year alone
                loans_disk_df_this_year = filtered_loan_disk_loans[filtered_loan_disk_loans["date_disbursed"] >= start_of_year]

                # Filter loans all time
                loans_disk_df_all_time = filtered_loan_disk_loans.copy()

    ######################################################################### Agents Disbursement ####################################################################################

                @st.cache_data(show_spinner=False)
                def create_disbursement_chart(df, title):
                    # Group by 'agent_full_name' to calculate total amount per agent
                    agent_summary = df.groupby('agent_full_name', as_index=False).agg(
                        total_amount=('amount', 'sum')  # Summing the amount per agent
                    )

                    # Format amount with commas
                    agent_summary['total_amount'] = agent_summary['total_amount'].round(2)
                    agent_summary['formatted_total_amount'] = agent_summary['total_amount'].apply(lambda x: f"{x:,.0f}")

                    # Sort the main dataset by total_amount
                    df = agent_summary.sort_values(by='total_amount', ascending=False)

                    # Create a bar chart with amount and total amount
                    fig = px.bar(
                        df,
                        x='total_amount',
                        y='agent_full_name',
                        orientation='h',
                        title=title,
                        color='total_amount',
                        color_continuous_scale='blues',
                        hover_data={'total_amount': False, 'formatted_total_amount': True}  # Show formatted value
                    )

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    return fig

    ######################################################################### Gets repayment dataframe by Date filters ####################################################################################

                # Drop 'agent_id' to mergfe successfully and avoid a clash with 'agent_id' in repayment table
                loan_disk_repay_merge = filtered_loan_disk_loans.drop(columns='agent_id')

                # merge repayments and loans by months
                merged_loan_disk_with_repay = loan_disk_repay_merge.merge(filtered_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

                # Filter loans for today alone
                loans_disk_repayment_df_today = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] == today]

                # Filter loans for yesterday alone
                loans_disk_repayment_df_yesterday = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] == yesterday]

                # Filter loans from this week alone
                loans_disk_repayment_df_this_week = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] >= start_of_week]

                # Filter loans from this month alone
                loans_disk_repayment_df_this_month = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] >= start_of_month]

                # Filter loans from this year alone
                loans_disk_repayment_df_this_year = merged_loan_disk_with_repay[merged_loan_disk_with_repay["paid_date"] >= start_of_year]

                # Filter loans from this year alone
                loans_disk_repayment_df_all_time = merged_loan_disk_with_repay.copy()

    ######################################################################### Agents Repayment ####################################################################################

                @st.cache_data(show_spinner=False)
                def create_repayment_chart(df, exp_repay_df, title):
                    # Merge actual repayments with expected repayments
                    df = df.groupby(['agent_id', 'agent_full_name'])['repayment_by_date'].sum().reset_index()
                    exp_repay_df = exp_repay_df.groupby(['agent_id'])['exp_repay_today'].sum().reset_index()
                    df = df.merge(exp_repay_df, on='agent_id', how='left')

                    # Summarize by agent
                    df_summary = df.groupby('agent_full_name', as_index=False).agg(
                        total_repayment=('repayment_by_date', 'sum'),
                        total_expected=('exp_repay_today', 'sum')
                    )

                    # Compute repayment rate, treating zero expected as full completion
                    df_summary['repayment_rate'] = np.where(
                        df_summary['total_expected'] == 0,
                        1.0,
                        df_summary['total_repayment'] / df_summary['total_expected']
                    )

                    # Cap at 100%
                    df_summary['repayment_rate'] = df_summary['repayment_rate'].clip(upper=1.0)

                    # Categorize for coloring
                    df_summary['progress_category'] = np.where(
                        df_summary['repayment_rate'] >= 1.0,
                        "Fully Met ✅",
                        "Below Target ❌"
                    )

                    # Format for display
                    df_summary['formatted_repayment'] = df_summary['total_repayment'].map("{:,.2f}".format)
                    df_summary['formatted_expected'] = df_summary['total_expected'].map("{:,.2f}".format)
                    df_summary['display_rate'] = (df_summary['repayment_rate'] * 100).round(0).astype(int).astype(str) + "%"

                    # Sort by rate descending
                    df_summary = df_summary.sort_values('repayment_rate', ascending=False)

                    # Build bar chart
                    fig = px.bar(
                        df_summary,
                        x='repayment_rate',
                        y='agent_full_name',
                        orientation='h',
                        title=title,
                        color='progress_category',
                        color_discrete_map={"Fully Met ✅": "green", "Below Target ❌": "red"},
                        text='display_rate',
                        hover_data={
                            'formatted_repayment': True,
                            'formatted_expected': True,
                            'display_rate': True,
                            'progress_category': False,
                            'repayment_rate': False
                        }
                    )

                    # Tweak hover and layout
                    fig.update_traces(
                        hovertemplate=(
                            "Repayment: %{customdata[0]}<br>"
                            "Expected: %{customdata[1]}<br>"
                            "Rate: %{text}"
                        ),
                        textposition='inside'
                    )
                    fig.update_layout(
                        xaxis=dict(title='Repayment Progress (%)', tickformat=".0%"),
                        yaxis=dict(title='Agent'),
                        paper_bgcolor="rgba(0,0,0,0)",
                        plot_bgcolor="rgba(0,0,0,0)",
                        modebar={"bgcolor": "rgba(0,0,0,0)"}
                    )

                    st.plotly_chart(fig, use_container_width=True)

    ############################################################## Make plots based on progress and disbursements #########################################################################

                loan_id_ = filtered_loan_disk_loans[['id', 'agent_id']]
                filtered_exp_till_today_ = filtered_exp_till_today_.merge(loan_id_, how='left', on='id')
                filtered_exp_till_yesterday_ = filtered_exp_till_yesterday_.merge(loan_id_, how='left', on='id')
                filtered_exp_till_ThisWeek_ = filtered_exp_till_ThisWeek_.merge(loan_id_, how='left', on='id')
                filtered_exp_till_ThisMonth_ = filtered_exp_till_ThisMonth_.merge(loan_id_, how='left', on='id')
                filtered_exp_till_ThisYear_ = filtered_exp_till_ThisYear_.merge(loan_id_, how='left', on='id')
                filtered_exp_all_time = filtered_exp_all_time.merge(loan_id_, how='left', on='id')

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_today, "Disbursement By Agents (Today)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_today, filtered_exp_till_today_, "Collections By Agents (Today)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_yesterday, "Disbursement By Agents (Yesterday)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_yesterday, filtered_exp_till_yesterday_, "Collections By Agents (Yesterday)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_this_week, "Disbursement By Agents (This Week)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_this_week, filtered_exp_till_ThisWeek_, "Collections By Agents (This Week)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_this_month, "Disbursement By Agents (This Month)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_this_month, filtered_exp_till_ThisMonth_, "Collections By Agents (This Month)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_this_year, "Disbursement By Agents (This Year)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_this_year, filtered_exp_till_ThisYear_, "Collections By Agents (This Year)")

                st.markdown("---")

                col1, col2 = st.columns(2)
                with col1:
                    st.plotly_chart(create_disbursement_chart(loans_disk_df_all_time, "Disbursement By Agents (All Time)"), use_container_width=True)
                with col2:
                    create_repayment_chart(loans_disk_repayment_df_all_time, filtered_exp_all_time, "Collections By Agents (All Time)")

            st.markdown("---")

    ############################################################### Dropdown to select between Open Loans and Completed Loans #########################################################################

            ## PAST MATURITY
            summary_past_mat_loan_disk = filtered_loan_disk_past_mat_df.copy()
            ### Copy dataset to get last date in repayment
            last_date_repayment = filtered_loan_disk_repayment_df.copy()
            ### Gets the last date by sorting and grouping
            last_date_repayment = last_date_repayment.sort_values(by=['ajo_loan_id', 'paid_date']).groupby('ajo_loan_id').last().reset_index()
            #### Merge past maturity with repayments
            all_past_due_loan_disk = filtered_loan_disk_past_mat_df.merge(filtered_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')
            all_past_due_loan_disk = all_past_due_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True]).reset_index()

            # Convert to datetime
            summary_past_mat_loan_disk['start_date'] = pd.to_datetime(summary_past_mat_loan_disk['start_date'])  # Convert to datetime
            all_past_due_loan_disk['start_date'] = pd.to_datetime(all_past_due_loan_disk['start_date'])  # Convert to datetime
            # Calculate number of days from start_date till today
            summary_past_mat_loan_disk['age_in_days'] = (datetime.today() - summary_past_mat_loan_disk['start_date']).dt.days
            all_past_due_loan_disk['age_in_days'] = (datetime.today() - all_past_due_loan_disk['start_date']).dt.days
            # # Convert days to months (assuming 30 days per month for approximation)
            # summary_past_mat_loan_disk['age_in_months'] = (summary_past_mat_loan_disk['age_in_days'] / 30).apply(lambda x: round(x))
            # Gets duration
            summary_past_mat_loan_disk['Duration'] = summary_past_mat_loan_disk['start_date'] - datetime.today()
            all_past_due_loan_disk['Duration'] = all_past_due_loan_disk['start_date'] - datetime.today()
            # Compute cumulative sum per 'id'
            all_past_due_loan_disk['cumulative_repayment'] = all_past_due_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_past_mat_loan_disk['principal_balance'] = (summary_past_mat_loan_disk['principal_element'] - summary_past_mat_loan_disk['repayment']).clip(lower=0)
            all_past_due_loan_disk['principal_paid'] = (all_past_due_loan_disk['principal_element_pct'] * all_past_due_loan_disk['cumulative_repayment']).round(2)
            all_past_due_loan_disk['principal_balance'] = (all_past_due_loan_disk['amount'] - all_past_due_loan_disk['principal_paid']).clip(lower=0)
            # Subtract from initial amount in the same row
            all_past_due_loan_disk['Bal_per_paid_date'] = all_past_due_loan_disk['expected_repayment'] - all_past_due_loan_disk['cumulative_repayment']
            # Get few columnsin repayment before merging
            last_date_repayment = last_date_repayment[['ajo_loan_id', 'paid_date', 'repayment_by_date']]
            # merge summary with repayment to get the last date of payment
            summary_past_mat_loan_disk = summary_past_mat_loan_disk.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
            # sort by dates
            summary_past_mat_loan_disk = summary_past_mat_loan_disk.sort_values(by=['past_maturity'], ascending=False).reset_index()
            all_past_due_loan_disk = all_past_due_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()
            # Arranges dataset for summary
            summary_past_mat_loan_disk = summary_past_mat_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'expected_repayment', 'repayment', 'due_today_amount', 'open_portfolio', 'end_date',
            'past_maturity', 'paid_date', 'repayment_by_date', 'age_in_days', 'Duration']]
            # Arranges dataset for all past due
            all_past_due_loan_disk = all_past_due_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio', 'end_date',
            'past_maturity', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date', 'due_today_amount']]
            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "agent_full_name": "Loan Officer",
                "agent_contact": "LO Contact",
                "guarantor_phone_number": "Guarantor Contact",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment_by_date": "Repayment",
                "open_portfolio": "Pending Due",
                "past_maturity": "Days Past",
                "end_date": "Maturity Date",
                "paid_date": "Payment Date",
                "repayment": "Paid",
            }

            summary_past_mat_loan_disk.rename(columns=column_mappings, inplace=True)
            all_past_due_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Missed Repayment Summary ####################################################################################

            ## MISSED REPAYMENT
            summary_completed_loan_disk = filtered_loan_disk_completed_df.copy()
            #### Merge missed repayments
            all_completed_loan_disk = filtered_loan_disk_completed_df.merge(filtered_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

            # Sort by 'missed_days'
            summary_completed_loan_disk = summary_completed_loan_disk.sort_values(by=['date_disbursed', 'users_full_name'], ascending=True)
            all_completed_loan_disk = all_completed_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True])
            # Calculate number of days from start_date till today
            summary_completed_loan_disk['age_in_days'] = (summary_completed_loan_disk['end_date'] - summary_completed_loan_disk['start_date']).dt.days
            all_completed_loan_disk['age_in_days'] = (all_completed_loan_disk['end_date'] - all_completed_loan_disk['start_date']).dt.days
            # Gets duration
            summary_completed_loan_disk['Duration'] = summary_completed_loan_disk['start_date'] - summary_completed_loan_disk['end_date']
            all_completed_loan_disk['Duration'] = all_completed_loan_disk['start_date'] - all_completed_loan_disk['end_date']
            # merge summary with repayment to get the last date of payment
            summary_completed_loan_disk = summary_completed_loan_disk.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
            # Compute cumulative sum per 'id'
            all_completed_loan_disk['cumulative_repayment'] = all_completed_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_completed_loan_disk['principal_balance'] = (summary_completed_loan_disk['principal_element'] - summary_completed_loan_disk['repayment']).clip(lower=0)
            all_completed_loan_disk['principal_paid'] = (all_completed_loan_disk['principal_element_pct'] * all_completed_loan_disk['cumulative_repayment']).round(2)
            all_completed_loan_disk['principal_balance'] = (all_completed_loan_disk['amount'] - all_completed_loan_disk['principal_paid']).clip(lower=0)
            # Subtract from initial amount in the same row
            all_completed_loan_disk['Bal_per_paid_date'] = all_completed_loan_disk['expected_repayment'] - all_completed_loan_disk['cumulative_repayment']
            # sort by dates
            summary_completed_loan_disk = summary_completed_loan_disk.sort_values(by=['date_disbursed', 'users_full_name'], ascending=True).reset_index()
            all_completed_loan_disk = all_completed_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()
            # Gets columns and arragnes
            summary_completed_loan_disk = summary_completed_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'principal_element', 'expected_repayment', 'repayment', 'due_today_amount', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'age_in_days', 'Duration']]
            # Arranges dataset for all past due
            all_completed_loan_disk = all_completed_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date']]
            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "agent_full_name": "Loan Officer",
                "agent_contact": "LO Contact",
                "guarantor_phone_number": "Guarantor Contact",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment": "Paid",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "paid_date": "Last Payment Date",
                "repayment_by_date": "Last Payment"
            }

            summary_completed_loan_disk.rename(columns=column_mappings, inplace=True)
            all_completed_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Missed Repayment Summary ####################################################################################

            ## MISSED REPAYMENT
            summary_missed_repayment_loan_disk = filtered_loan_disk_missed_repay_df.copy()
            #### Merge missed repayments
            all_missed_repay_loan_disk = filtered_loan_disk_missed_repay_df.merge(filtered_loan_disk_repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

            # Sort by 'missed_days'
            summary_missed_repayment_loan_disk = summary_missed_repayment_loan_disk.sort_values(by='missed_days', ascending=False)
            all_missed_repay_loan_disk = all_missed_repay_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True])
            # Convert to datetime
            summary_missed_repayment_loan_disk['start_date'] = pd.to_datetime(summary_missed_repayment_loan_disk['start_date'])
            all_missed_repay_loan_disk['start_date'] = pd.to_datetime(all_missed_repay_loan_disk['start_date'])

            # Get current date and ensure it's a Timestamp
            current_date = pd.Timestamp.today().normalize()
            # Calculate Loan Duration
            summary_missed_repayment_loan_disk['age_in_days'] = (summary_missed_repayment_loan_disk['end_date'].clip(upper=current_date) - (summary_missed_repayment_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            all_missed_repay_loan_disk['age_in_days'] = (all_missed_repay_loan_disk['end_date'].clip(upper=current_date) - (all_missed_repay_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            # Gets loan duration in months
            summary_missed_repayment_loan_disk['Duration'] = (summary_missed_repayment_loan_disk['end_date'].clip(upper=current_date) - (summary_missed_repayment_loan_disk['start_date'] - pd.Timedelta(days=4)))
            all_missed_repay_loan_disk['Duration'] = (all_missed_repay_loan_disk['end_date'].clip(upper=current_date) - (all_missed_repay_loan_disk['start_date'] - pd.Timedelta(days=4)))

            # merge summary with repayment to get the last date of payment
            summary_missed_repayment_loan_disk = summary_missed_repayment_loan_disk.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')
            # Compute cumulative sum per 'id'
            all_missed_repay_loan_disk['cumulative_repayment'] = all_missed_repay_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_missed_repayment_loan_disk['principal_balance'] = (summary_missed_repayment_loan_disk['principal_element'] - summary_missed_repayment_loan_disk['repayment']).clip(lower=0)
            all_missed_repay_loan_disk['principal_paid'] = (all_missed_repay_loan_disk['principal_element_pct'] * all_missed_repay_loan_disk['cumulative_repayment']).round(2)
            all_missed_repay_loan_disk['principal_balance'] = (all_missed_repay_loan_disk['amount'] - all_missed_repay_loan_disk['principal_paid']).clip(lower=0)
            # Subtract from initial amount in the same row
            all_missed_repay_loan_disk['Bal_per_paid_date'] = all_missed_repay_loan_disk['expected_repayment'] - all_missed_repay_loan_disk['cumulative_repayment']
            # sort by dates
            summary_missed_repayment_loan_disk = summary_missed_repayment_loan_disk.sort_values(by=['missed_days', 'users_full_name'], ascending=[False, True]).reset_index()
            all_missed_repay_loan_disk = all_missed_repay_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()
            # Gets columns and arragnes
            summary_missed_repayment_loan_disk = summary_missed_repayment_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'expected_repayment', 'repayment', 'due_today_amount', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'age_in_days', 'Duration']]
            # Arranges dataset for all past due
            all_missed_repay_loan_disk = all_missed_repay_loan_disk[['date_disbursed', 'users_full_name', 'users_phone_number', 'agent_full_name',
            'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio',
            'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date', 'due_today_amount']]
            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "agent_full_name": "Loan Officer",
                "agent_contact": "LO Contact",
                "guarantor_phone_number": "Guarantor Contact",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment": "Paid",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "paid_date": "Last Payment Date",
                "repayment_by_date": "Last Payment"
            }

            summary_missed_repayment_loan_disk.rename(columns=column_mappings, inplace=True)
            all_missed_repay_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Repayment summary ####################################################################################

            # Copy filtered data
            summary_repayment_loan_disk = filtered_loan_disk_repayment_df.copy()
            LoanDisk_df = filtered_loan_disk_loans.copy()

            # Summarize repayments by loan ID
            summary_repayment_loan_disk = summary_repayment_loan_disk.groupby('ajo_loan_id')['repayment_by_date'].sum().reset_index()

            # Merge with loan details
            summary_repayment_loan_disk = summary_repayment_loan_disk.merge(LoanDisk_df, how='left', left_on='ajo_loan_id', right_on='id')
            all_repayment_loan_disk = filtered_loan_disk_repayment_df.merge(LoanDisk_df, how='left', left_on='ajo_loan_id', right_on='id')
            all_repayment_loan_disk = all_repayment_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date'], ascending=[True,True,True])

            # Calculate principal balance
            summary_repayment_loan_disk['principal_balance'] = (summary_repayment_loan_disk['amount'] - summary_repayment_loan_disk['repayment']).clip(lower=0)
            all_repayment_loan_disk['principal_balance'] = (all_repayment_loan_disk['amount'] - all_repayment_loan_disk['repayment']).clip(lower=0)

            # Get current date and ensure it's a Timestamp
            current_date = pd.Timestamp.today().normalize()
            # Calculate Loan Duration
            summary_repayment_loan_disk['age_in_days'] = (summary_repayment_loan_disk['end_date'].clip(upper=current_date) - (summary_repayment_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            all_repayment_loan_disk['age_in_days'] = (all_repayment_loan_disk['end_date'].clip(upper=current_date) - (all_repayment_loan_disk['start_date'] - pd.Timedelta(days=4))).dt.days
            # Gets loan duration in months
            summary_repayment_loan_disk['Duration'] = (summary_repayment_loan_disk['end_date'].clip(upper=current_date) - (summary_repayment_loan_disk['start_date'] - pd.Timedelta(days=4)))
            all_repayment_loan_disk['Duration'] = (all_repayment_loan_disk['end_date'].clip(upper=current_date) - (all_repayment_loan_disk['start_date'] - pd.Timedelta(days=4)))
            # Calculate days left only for loans that haven't reached their end date
            summary_repayment_loan_disk['days_to_maturity'] = (summary_repayment_loan_disk['end_date'] - current_date).dt.days
            all_repayment_loan_disk['days_to_maturity'] = (all_repayment_loan_disk['end_date'] - current_date).dt.days
            # If 'end_date' is already passed, set 'days_to_maturity' to 0
            summary_repayment_loan_disk.loc[summary_repayment_loan_disk['days_to_maturity'] < 0, 'days_to_maturity'] = 0
            all_repayment_loan_disk.loc[all_repayment_loan_disk['days_to_maturity'] < 0, 'days_to_maturity'] = 0
            # Compute cumulative repayment and balance per paid date
            all_repayment_loan_disk['cumulative_repayment'] = all_repayment_loan_disk.groupby('id')['repayment_by_date'].cumsum()
            # Calculates principal balance
            summary_repayment_loan_disk['principal_balance'] = (summary_repayment_loan_disk['principal_element'] - summary_repayment_loan_disk['repayment']).clip(lower=0)
            all_repayment_loan_disk['principal_paid'] = (all_repayment_loan_disk['principal_element_pct'] * all_repayment_loan_disk['cumulative_repayment']).round(2)
            all_repayment_loan_disk['principal_balance'] = (all_repayment_loan_disk['amount'] - all_repayment_loan_disk['principal_paid']).clip(lower=0)
            all_repayment_loan_disk['Bal_per_paid_date'] = all_repayment_loan_disk['expected_repayment'] - all_repayment_loan_disk['cumulative_repayment']

            # sort by dates
            summary_repayment_loan_disk = summary_repayment_loan_disk.sort_values(by=['date_disbursed', 'users_full_name'], ascending=True).reset_index()
            all_repayment_loan_disk = all_repayment_loan_disk.sort_values(by=['date_disbursed', 'users_full_name', 'paid_date', 'cumulative_repayment'], ascending=[True,True,False,False]).reset_index()

            # Select and arrange summary columns
            summary_repayment_loan_disk = summary_repayment_loan_disk[[
                'date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name',
                'agent_contact', 'guarantor_phone_number', 'amount', 'principal_balance', 'expected_repayment',
                'repayment_by_date', 'open_portfolio', 'missed_days', 'missed_repayment', 'age_in_days', 'Duration', 'days_to_maturity', 'end_date'
            ]]

            # Select and arrange all repayment data columns
            all_repayment_loan_disk = all_repayment_loan_disk[[
                'date_disbursed', 'users_full_name', 'users_phone_number', 'agent_full_name', 'agent_contact',
                'guarantor_phone_number', 'amount', 'principal_balance', 'principal_paid', 'expected_repayment', 'repayment', 'open_portfolio',
                'missed_days', 'missed_repayment', 'paid_date', 'repayment_by_date', 'cumulative_repayment', 'Bal_per_paid_date', 'age_in_days', 'Duration', 'days_to_maturity', 'end_date'
            ]]

            # Rename columns for clarity
            column_mappings = {
                "date_disbursed": "Date",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "agent_full_name": "Loan Officer",
                "agent_contact": "LO Contact",
                "guarantor_phone_number": "Guarantor Contact",
                "amount": "Principal",
                "expected_repayment": "Total Due",
                "repayment_by_date": "Paid Amount",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "paid_date": "Payment Date",
                "repayment": "Paid",
                "end_date": "Maturity Date"
            }

            summary_repayment_loan_disk.rename(columns=column_mappings, inplace=True)
            all_repayment_loan_disk.rename(columns=column_mappings, inplace=True)

    ###################################################################################### Selectbox to get options ####################################################################################

            # Select box for Loan Status
            loan_category = st.selectbox("Loan Status", ["Open", "Completed"], placeholder="Select Loan Status", index=None)

            # If 'Open Loans' is selected, show another dropdown for specific loan types
            if loan_category == "Open":
                open_loan_option = st.selectbox(
                    "Loan Sub-Status",
                    ["Past Maturity", "Missed Repayment", "Loan Repayment"],
                    placeholder="Select Loan Type",
                    index=None
                )

                if open_loan_option:
                    show_full_data = st.toggle("Show Expanded Dataset")

                    if show_full_data:
                        if open_loan_option == "Past Maturity":
                            dataset = all_past_due_loan_disk.copy()
                        elif open_loan_option == "Missed Repayment":
                            dataset = all_missed_repay_loan_disk.copy()
                        elif open_loan_option == "Loan Repayment":
                            dataset = all_repayment_loan_disk.copy()

                        unique_users = pd.Series(dataset['Name'].unique()).sort_values()
                        selected_user = st.selectbox("Select User", ["All Users"] + list(unique_users), index=0)

                        if selected_user != "All Users":
                            dataset = dataset[dataset['Name'] == selected_user]

                            show_reappearing = st.toggle("Show Individual Transactions (if user has other loans)")

                            if show_reappearing:
                                dataset['Date'] = pd.to_datetime(dataset['Date'])
                                # dataset = dataset.sort_values(['Name', 'Date'])

                                # Identify separate transactions by checking when a user reappears after completing a previous transaction
                                dataset['Transaction_Group'] = (dataset['Name'] != dataset['Name'].shift()) | (dataset['Date'].diff().dt.days > 1)
                                dataset['Transaction_Group'] = dataset['Transaction_Group'].cumsum()

                                # Split dataset into separate DataFrames per transaction occurrence
                                for group_num, transaction_data in dataset.groupby("Transaction_Group"):
                                    st.write(f"### Transaction {group_num} for {selected_user}")
                                    st.write(transaction_data.drop(columns=['Transaction_Group']))
                            else:
                                st.write(f"### Transaction for {selected_user}")
                                st.write(dataset)
                        else:
                            st.write(f"### Transaction for {selected_user}")
                            st.write(dataset)
                    else:
                        st.write("### Summarized Dataset")
                        st.write(summary_past_mat_loan_disk if open_loan_option == "Past Maturity" else
                                summary_missed_repayment_loan_disk if open_loan_option == "Missed Repayment" else
                                summary_repayment_loan_disk)

            # If 'Completed Loans' is selected
            elif loan_category == "Completed":
                completed_loan_option = st.selectbox("Loan Sub-Status", ["Loans Completed"], placeholder="Select Loan Type", index=None)

                if completed_loan_option:
                    show_full_data = st.toggle("Show Expanded Dataset")

                    if show_full_data:
                        dataset = all_completed_loan_disk.copy()
                        unique_users = pd.Series(dataset['Name'].unique()).sort_values()
                        selected_user = st.selectbox("Select User", ["All Users"] + list(unique_users), index=0)

                        if selected_user != "All Users":
                            dataset = dataset[dataset['Name'] == selected_user]

                            show_reappearing = st.toggle("Show Individual Transactions (if user has other loans)")

                            if show_reappearing:
                                dataset['Date'] = pd.to_datetime(dataset['Date'])
                                # dataset = dataset.sort_values(['Name', 'Date'])
                                dataset['Transaction_Group'] = (dataset['Name'] != dataset['Name'].shift()) | (dataset['Date'].diff().dt.days > 1)
                                dataset['Transaction_Group'] = dataset['Transaction_Group'].cumsum()

                                for group_num, transaction_data in dataset.groupby("Transaction_Group"):
                                    st.write(f"### Completed Loan Transaction {group_num} for {selected_user}")
                                    st.write(transaction_data.drop(columns=['Transaction_Group']))
                            else:
                                st.write(f"### Completed Loan Transaction for {selected_user}")
                                st.write(dataset)
                        else:
                            st.write(f"### Completed Loan Transaction for {selected_user}")
                            st.write(dataset)
                    else:
                        st.write("### Summarized Dataset")
                        st.write(summary_completed_loan_disk)

    ################################################################################### Tab 4 #################################################################################

    with tab4:

    ######################################################################### More customization using div class ####################################################################################

        # Copy dataframes
        savings_and_loans = staff_savings_trans.copy()
        login_count = loginactivity.copy()
        supervisor_agent_andbranch = sup_df.copy()
        open_loans = filtered_data.copy()

        # Group and get monthly expected repayments by agents
        monthly_expected_repayments = filtered_data.groupby(['agent_id', 'month'])['exp_repay_today'].sum().reset_index()
        # Calculate monthly repayment percentage
        monthly_expected_repayments['month'] = monthly_expected_repayments['month'].str.strip() #remove leading and trailling white spaces
        monthly_repayments['month'] = monthly_repayments['month'].str.strip()

        # Merge repayments and expected repayments to 1 df
        repayments_and_expected = monthly_expected_repayments.merge(monthly_repayments,
                                                                    on=['agent_id', 'month'])

        # Calculate repayment percentage
        repayments_and_expected['repayment_rate'] = round((repayments_and_expected['total_repayment_amount']/repayments_and_expected['exp_repay_today'])*100,2)
        repayments_and_expected.replace(np.inf, np.nan, inplace=True)
        avg_repayment_pct = repayments_and_expected.groupby('agent_id')[['repayment_rate']].mean().round(2).reset_index()

        merged_agent_perf = avg_repayment_pct.merge(disbursement_4_last_month, on = 'agent_id', how = 'outer')

        # Repayment commission
        repayment_commission = []
        for index, row in merged_agent_perf.iterrows():
            if row['repayment_rate'] >= 80 and row['repayment_rate'] < 85:
                commission = (0.5/100) * row['last_month']
                repayment_commission.append(commission)

            elif row['repayment_rate'] >= 85:
                commission = (1/100) * row['last_month']
                repayment_commission.append(commission)
            else:
                repayment_commission.append(0)
        merged_agent_perf['repayment_commission'] = repayment_commission

        # Merge datasets
        savings_loans_and_login =  staff_savings_trans.merge(login_count, on = 'email', how = 'left')
        # Add earnings
        plus_earnings = savings_loans_and_login.merge(earnings, on = 'user_id', how='left')
        # Merge datasets
        savings_loans_and_activity = plus_earnings.merge(supervisor_agent_andbranch, left_on='customer_user_id', right_on = 'team_user_id', how='right')
        # Fills 'NaN' with 'Null'
        savings_loans_and_activity[['sup_name', 'location', 'agent_name', 'email']].fillna('Null', inplace=True)
        # Fills 'NaN' with 0
        savings_loans_and_activity.fillna(0, inplace=True)
        # Change datayepe from float to int
        savings_loans_and_activity[["agent_age_days","savings","disbursed","trans_count","login_count"]] = savings_loans_and_activity[["agent_age_days","savings","disbursed","trans_count","login_count"]].astype("int")
        # Set activity tag
        savings_loans_and_activity['activity'] = np.where(
            (savings_loans_and_activity['agent_age_days'] > 30) & (savings_loans_and_activity['login_count'] > 160),
            'active_old',
            np.where(savings_loans_and_activity['agent_age_days'] <= 30,
                        np.where(savings_loans_and_activity['login_count'] > 80, 'active_new', 'inactive_new'),
                        'inactive_old'))

        # CHURN
        savings_loans_and_activity['churned'] = np.where(
            (savings_loans_and_activity['agent_age_days'] > 30)
            & (savings_loans_and_activity['activity'].isin(['inactive_new','inactive_old']))
            & (savings_loans_and_activity['savings'] == 0)
            & (savings_loans_and_activity['login_count'] < 38),'churned','not churned')

        avg_weekly_savings = 750_000
        # avg_weekly_trnx_count = 38
        avg_Weekly_loan	= 3_000_000

        # Loan and savings performance
        savings_loans_and_activity['savings_performance'] = (round((savings_loans_and_activity['savings']/avg_weekly_savings),3)*100).astype('str')+'%'
        savings_loans_and_activity['loans_performance'] = (round((savings_loans_and_activity['disbursed']/avg_Weekly_loan),3)*100).astype('str')+'%'

        # Cap performance to ensure it  dosent affect the overall
        savings_loans_and_activity['savings_performance_num'] = pd.to_numeric(savings_loans_and_activity['savings_performance'].str.strip('%'))
        savings_loans_and_activity['savings_performance_capped'] = ['100%' if x > 100 else str(x)+'%' for x in savings_loans_and_activity['savings_performance_num']]
        savings_loans_and_activity['loans_performance_num'] = pd.to_numeric(savings_loans_and_activity['loans_performance'].str.strip('%'))
        savings_loans_and_activity['loans_performance_capped'] = ['100%' if x > 100 else str(x)+'%' for x in savings_loans_and_activity['loans_performance_num']]

        savings_loans_and_activity['overall'] = round(
            (pd.to_numeric(savings_loans_and_activity['savings_performance_capped'].str.strip('%')) +
                pd.to_numeric(savings_loans_and_activity['loans_performance_capped'].str.strip('%'))) / 2, 3
        ).astype('str') + '%'

        # ADD BASE PAY
        base_pay = 80_000
        savings_loans_and_activity['base_pay'] = base_pay
        savings_loans_and_activity['total_earnings'] = base_pay + savings_loans_and_activity['earnings']

        merged_agent_perf = merged_agent_perf[['agent_id','repayment_rate','last_month','repayment_commission']]
        savings_loans_and_activity = savings_loans_and_activity.merge(merged_agent_perf, left_on='user_id', right_on='agent_id', how = 'left')

        performance_report = savings_loans_and_activity[
            ['sup_name', 'location', 'agent_name', 'email','agent_age_days',
                'savings','savings_performance','savings_performance_capped','trans_count',
                'disbursed','loans_performance','loans_performance_capped','repayment_rate','last_month','repayment_commission','earnings','base_pay',
                'total_earnings','interest','login_count','churned','activity','overall']]

        # Title-case specific string columns
        for col in ['sup_name', 'sub_location', 'agent_name']:
            if col in performance_report.columns:
                performance_report[col] = performance_report[col].astype(str).str.title()

        performance_report['status'] = performance_report['activity'].apply(
            lambda x: 'Active' if 'active' in x else 'Inactive'
        )

        report = performance_report.merge(filtered_data, how='right', left_on='agent_name', right_on='agent_full_name')
        # Active agents (those with 'active_old' OR 'active_new')
        active_agent = report[
            (report['activity'] == 'active_old') | (report['activity'] == 'active_new')
        ]

        # Inactive agents (those with 'inactive_old' OR 'inactive_new')
        inactive_agent = report[
            (report['activity'] == 'inactive_old') | (report['activity'] == 'inactive_new')
        ]

    ######################################################################### More customization using div class ####################################################################################

        # Custom CSS for bordered metrics
        st.markdown(
            """
            <style>
            .metric-containers {
                border: 4px solid #4b9ca5; /* Sea green border */
                border-radius: 20%; /* Rounded corners */
                padding: 10px;
                text-align: center;
                margin: 15px;
                width: 100%;
                box-shadow: 3px 3px 5px rgba(0, 151, 167, 0.5); /* shadow */
            }
            .metric-values {
                font-size: 18px;
                font-weight: bold;
            }
            .metric-labels {
                font-size: 16px;
                color: #555;
                font-weight: bold;
            }
            </style>
            """, unsafe_allow_html=True
        )

        # Custom metric component with delta
        def new_bordered_metric(label, value):
            st.markdown(
                f"""
                <div class="metric-containers">
                    <div class="metric-values">{value}</div>
                    <div class="metric-labels">{label}</div>
                </div>
                """, unsafe_allow_html=True
            )

        # Metrics with borders and dynamic deltas
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            new_bordered_metric("Total Agents", f"{pd.Series(new_filtered_data['agent_id'].unique()).count():,} Agents")
        with col2:
            new_bordered_metric("Active Agents", f"{pd.Series(active_agent['agent_id'].unique()).count():,} Agents")
        with col3:
            new_bordered_metric("Inactive Agents", f"{pd.Series(new_filtered_data['agent_id'].unique()).count() - pd.Series(active_agent['agent_id'].unique()).count():,} Agents")
        with col4:
            new_bordered_metric("Past maturity", f"N{past_maturity_total_amount.round(2):,}")

        # Metrics with borders and dynamic deltas
        col5, col6, col7, col8 = st.columns(4)
        with col5:
            new_bordered_metric("Bad Loans (count)", f"{bad_loans_count:,} loans")
        with col6:
            new_bordered_metric("Bad Loans (amount)", f"N{total_bad_loans:,}")
        with col7:
            new_bordered_metric("Late Repayment (count)", f"{late_repayment_count:,} loans")
        with col8:
            new_bordered_metric("Late Repayment (amount)", f"N{total_late_repayment:,.2f}")

        st.markdown("---")

    ######################################################################################## Days to maturity ##############################################################################

        # Copy dataset
        DaysLeft = new_filtered_data.copy()

        # Gets loans expiring in a month time
        DaysLeft = DaysLeft[(DaysLeft['open_portfolio'] > 0) & (DaysLeft['days_left_to_maturity'] > 0) & (DaysLeft['days_left_to_maturity'] <= 30)]
        # Sort by days left to maturity
        DaysLeft = DaysLeft.sort_values(by='days_left_to_maturity', ascending=True).reset_index()

        # Calculates principal balance
        DaysLeft['principal_balance'] = (DaysLeft['amount'] - DaysLeft['repayment']).clip(lower=0) ## Removing my principal first from the repayments as it's almost matured

        # Calculate what we are to collect each day to meet up repayment before it matures
        DaysLeft['Pre-Maturity Collection'] = (DaysLeft['open_portfolio'] / DaysLeft['days_left_to_maturity']).round(2)

        # Apply conditions to create 'Safe Zone' column
        def classify_risk(row):
            if (row['Pre-Maturity Collection'] >= 1.5 * row['daily_repayment_amount']) & (row['Pre-Maturity Collection'] > 10_000):
                return 'Extremely Risky'
            elif row['Pre-Maturity Collection'] > row['daily_repayment_amount']:
                return 'Risky'
            elif (row['daily_repayment_amount'] >= 3 * row['Pre-Maturity Collection']) | (row['Pre-Maturity Collection'] <= 1_000):
                return 'Safe'
            else:
                return 'Not Risky'

        DaysLeft['Safe Zone'] = DaysLeft.apply(classify_risk, axis=1)

        # Define bins (fixed up to 1.2M)
        bins = [0, 10_000, 50_000, 100_000, 200_000, 300_000, 400_000, 500_000, 600_000, 700_000, 800_000, 900_000, 1_000_000, 1_100_000, 1_200_000]

        # Create labels to match the bins
        labels = [
            '0 - 10K',
            '10K - 50K',
            '50K - 100K',
            '100K - 200K',
            '200K - 300K',
            '300K - 400K',
            '400K - 500K',
            '500K - 600K',
            '600K - 700K',
            '700K - 800K',
            '800K - 900K',
            '900K - 1M',
            '1M - 1.1M',
            '1.1M - 1.2M'
        ]

        # Categorize the 'open_portfolio' column
        DaysLeft['Pending Due Category'] = pd.cut(
            DaysLeft['open_portfolio'],
            bins=bins,
            labels=labels,
            include_lowest=True
        )

        # Calculate the sum of 'open_portfolio'
        total_open_portfolio = DaysLeft['open_portfolio'].sum()
        plus_past_maturity = past_due_date['open_portfolio'].sum() + DaysLeft['open_portfolio'].sum()
        risky_loans = DaysLeft[(DaysLeft['Safe Zone']=='Extremely Risky') | (DaysLeft['Safe Zone']=='Risky')]
        risky_loans_ = risky_loans['open_portfolio'].sum() + past_due_date['open_portfolio'].sum()

        # Gets rate of each safe zone
        safe_rate = DaysLeft[DaysLeft['Safe Zone']=='Safe']
        safe_rate_ = (safe_rate['open_portfolio'].sum() / DaysLeft['open_portfolio'].sum()) * 100

        risky_rate = DaysLeft[DaysLeft['Safe Zone']=='Risky']
        risky_rate_ = (risky_rate['open_portfolio'].sum() / DaysLeft['open_portfolio'].sum()) * 100

        ext_risky_rate = DaysLeft[DaysLeft['Safe Zone']=='Extremely Risky']
        ext_risky_rate_ = (ext_risky_rate['open_portfolio'].sum() / DaysLeft['open_portfolio'].sum()) * 100

        not_risky_rate = DaysLeft[DaysLeft['Safe Zone']=='Not Risky']
        not_risky_rate_ = (not_risky_rate['open_portfolio'].sum() / DaysLeft['open_portfolio'].sum()) * 100

        # Display the sum in markdown
        st.markdown(
            f"""
            <div style='text-align: center;'>
                <h2>Maturing Loans within 30 Days</h2>
                <h6><strong>The outstanding portfolio for loans maturing within the next 30 days is: N{total_open_portfolio:,.2f}</strong></h6>
                <h6><strong>Outstanding portfolio plus current past maturity is: N{plus_past_maturity:,.2f}</strong></h6>
                <h6><strong>Loans past maturity plus all 'Risky' or 'Extremely Risky' loans within the next 30 days is: N{risky_loans_:,.2f}</span></strong></h6>
                <h6><strong>Portfolio outstanding amount ratios 👉 Safe rate: {safe_rate_:,.2f}% || Not Risky rate: {not_risky_rate_:,.2f}% || Risky rate: {risky_rate_:,.2f}% || Extremely risky rate: {ext_risky_rate_:,.2f}% </strong></h6>
            </div>
            """,
            unsafe_allow_html=True
        )

        # Select and arrange all repayment data columns
        DaysLeft = DaysLeft[[
            'date_disbursed', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'agent_full_name', 'agent_contact',
            'guarantor_phone_number', 'amount', 'principal_balance', 'repayment', 'expected_repayment', 'due_today_amount', 'open_portfolio', 'Pending Due Category',
            'repayment_rate', 'daily_repayment_amount', 'Pre-Maturity Collection', 'Safe Zone', 'missed_days', 'missed_repayment', 'days_left_to_maturity', 'end_date'
        ]]

        # Rename columns for clarity
        column_mappings_ = {
            "date_disbursed": "Date",
            "users_full_name": "Name",
            "users_phone_number": "Contact",
            "users_address": "Address",
            "users_trade": "Business",
            "agent_full_name": "Loan Officer",
            "agent_contact": "LO Contact",
            "guarantor_phone_number": "Guarantor Contact",
            "amount": "Principal",
            "expected_repayment": "Total Due",
            "due_today_amount": "Due Today",
            "repayment_by_date": "Paid Amount",
            "open_portfolio": "Pending Due",
            "repayment_rate": 'Repayment Rate',
            "missed_days": "Days Missed",
            "missed_repayment": "Missed Amount",
            "paid_date": "Payment Date",
            "repayment": "Paid",
            "end_date": "Maturity Date",
            "daily_repayment_amount": "Daily Collection"
        }

        DaysLeft.rename(columns=column_mappings_, inplace=True)

        # Display filtered data inside an expander
        with st.expander("Maturing Loans within 30 days"):
            st.dataframe(DaysLeft)
            # Provide feedback for skipped filters
            if DaysLeft.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                st.success(f"Displaying {len(DaysLeft)} records.")

        st.markdown('---')

    ######################################################################### Bar chart for repayments ####################################################################################

        if 'all_trend' in globals() or 'all_trend' in locals():
            try:
                # Fill na with 0
                all_trend = all_trend.fillna(0)

                # Calculate repayment rate (avoid division by zero)
                all_trend ["repayment_rate"] = (
                    all_trend ["repayment_by_date"] / all_trend ["exp_repay_today"]
                ).fillna(0)  # Fill NaN (in case exp_repay_today is 0)

                # Convert rate to percentage
                all_trend ["repayment_rate"] = (all_trend ["repayment_rate"] * 100).round(2)

                a1, a2 = st.columns(2)

                with a1:
                    # Create side-by-side bar chart
                    fig = px.bar(
                        all_trend ,
                        x="date_disbursed",
                        y=["repayment_by_date", "exp_repay_today"],
                        title="Repayment Rate Bar Chart",
                        barmode="group",  # Side-by-side bars
                        labels={"value": "Amount", "variable": "Repayment Type"},
                        hover_data={"repayment_rate": ":.2f"},  # Show repayment rate as percentage
                    )

                    # Customize layout
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"},
                    )

                    # Display the chart
                    st.plotly_chart(fig)

                with a2:
                    # Create side-by-side bar chart
                    fig = px.line(
                        all_trend ,
                        x="date_disbursed",
                        y=["repayment_by_date", "exp_repay_today"],
                        title="Repayment Rate Line Chart",
                        labels={"value": "Amount", "variable": "Repayment Type"},
                        hover_data={"repayment_rate": ":.2f"},  # Show repayment rate as percentage
                    )

                    # Customize layout
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"},
                    )

                    fig.update_traces(mode="lines+markers")

                    # Display the chart
                    st.plotly_chart(fig)

                st.markdown('---')

    ######################################################################### Checks agents, trades and branches for best repayemnt ####################################################################################

                # Filter loans from this month alone
                loans_per_month = new_filtered_data[new_filtered_data["date_disbursed"] >= start_of_month]

                # Filter loans from this week alone
                loans_per_week = new_filtered_data[new_filtered_data["date_disbursed"] >= start_of_week]

                # Drop 'agent_id' to mergfe successfully and avoid a clash with 'agent_id' in repayment table
                loan_repay_merge = new_filtered_data.drop(columns='agent_id')

                # merge repayments and loans by months
                merged_repay = loan_repay_merge.merge(repayment_df, how='left', left_on='id', right_on='ajo_loan_id')

                # Filter repayments from this month alone
                repayment_per_month = merged_repay[merged_repay["paid_date"] >= start_of_month]

                # Filter repayments from this week alone
                repayment_per_week = merged_repay[merged_repay["paid_date"] >= start_of_week]

    ######################################################################### Top Agents Disbursement ####################################################################################

                st.markdown(
                    """
                    <div style='text-align: center;'>
                        <h3> 💸 Top 10 Agents By Disbursement 💸 </h3>
                    </div>
                    """, unsafe_allow_html=True
                )

                col1, col2, col3 = st.columns(3)

                with col1:
                    # Top 10 Agents By Disbursement (This Week)
                    top_agents_by_disbursement_weekly = (
                        loans_per_week.groupby(['agent_id', 'agent_full_name'])['amount']
                        .sum()
                        .reset_index()
                        .sort_values(by='amount', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_disbursement_weekly,
                                x='amount',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Disbursement (This Week)",
                                color='amount',
                                color_continuous_scale='blugrn')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col2:
                    # Top 10 Agents By Disbursement (This Month)
                    top_agents_by_disbursement_monthly = (
                        loans_per_month.groupby(['agent_id', 'agent_full_name'])['amount']
                        .sum()
                        .reset_index()
                        .sort_values(by='amount', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_disbursement_monthly,
                                x='amount',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Disbursement (This Month)",
                                color='amount',
                                color_continuous_scale='blugrn')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col3:
                    # Top 10 Agents By Disbursement (All Time)
                    top_agents_by_disbursement_all_time = (
                        new_filtered_data.groupby(['agent_id', 'agent_full_name'])['amount']
                        .sum()
                        .reset_index()
                        .sort_values(by='amount', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_disbursement_all_time,
                                x='amount',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Disbursement (All Time)",
                                color='amount',
                                color_continuous_scale='blugrn')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                st.markdown("---")

    ######################################################################### Top Agents Repayment ####################################################################################

                st.markdown(
                    """
                    <div style='text-align: center;'>
                        <h3> 💵 Top 10 Agents By Repayments 💵 </h3>
                    </div>
                    """, unsafe_allow_html=True
                )

                col4, col5, col6 = st.columns(3)

                with col4:
                    # Top 10 Agents By Repayments (This Week)
                    top_agents_by_repayment_weekly = (
                        repayment_per_week.groupby(['agent_id', 'agent_full_name'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_repayment_weekly,
                                x='repayment_by_date',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Repayments (This Week)",
                                color='repayment_by_date',
                                color_continuous_scale='blues')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col5:
                    # Top 10 Agents By Repayments (This Month)
                    top_agents_by_repayment_monthly = (
                        repayment_per_month.groupby(['agent_id', 'agent_full_name'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_repayment_monthly,
                                x='repayment_by_date',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Repayments (This Month)",
                                color='repayment_by_date',
                                color_continuous_scale='blues')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col6:
                    # Top 10 Agents By Repayments (All Time)
                    top_agents_by_repayment_all_time = (
                        merged_repay.groupby(['agent_id', 'agent_full_name'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_agents_by_repayment_all_time,
                                x='repayment_by_date',
                                y='agent_full_name',
                                orientation='h',
                                title="Top 10 Agents By Repayments (All Time)",
                                color='repayment_by_date',
                                color_continuous_scale='blues')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                st.markdown("---")

    ######################################################################### Top Trade Repayment ####################################################################################

                st.markdown(
                    """
                    <div style='text-align: center;'>
                        <h3> 📈 Top 10 Trades By Repayments 📉 </h3>
                    </div>
                    """, unsafe_allow_html=True
                )

                col2, col3, col4 = st.columns(3)

                with col2:
                    # Top 10 Repaying Trades (This Week)
                    top_trades_by_repayment_weekly = (
                        repayment_per_week.groupby(['users_trade'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_trades_by_repayment_weekly,
                                x='users_trade',
                                y='repayment_by_date',
                                # orientation='h',
                                title="Top 10 Repaying Trades (This Week)",
                                color='repayment_by_date',
                                color_continuous_scale='peach')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col3:
                    # Top 10 Repaying Trades (This Month)
                    top_trades_by_repayment_monthly = (
                        repayment_per_month.groupby(['users_trade'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_trades_by_repayment_monthly,
                                x='users_trade',
                                y='repayment_by_date',
                                # orientation='h',
                                title="Top 10 Repaying Trades (This Month)",
                                color='repayment_by_date',
                                color_continuous_scale='peach')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col4:
                    # Top 10 Repaying Trades (All Time)
                    top_trades_by_repayment_all_time = (
                        merged_repay.groupby(['users_trade'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_trades_by_repayment_all_time,
                                x='users_trade',
                                y='repayment_by_date',
                                # orientation='h',
                                title="Top 10 Repaying Trades (All Time)",
                                color='repayment_by_date',
                                color_continuous_scale='peach')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                st.markdown("---")

    ######################################################################### Top Branch Repayment ####################################################################################

                st.markdown(
                    """
                    <div style='text-align: center;'>
                        <h3> 🏦 Top 10 Branches By Repayments 🏬 </h3>
                    </div>
                    """, unsafe_allow_html=True
                )

                col4, col5, col6 = st.columns(3)

                with col4:
                    # Top 10 Repaying Branches (This Week)
                    top_branches_by_repayment_weekly = (
                        repayment_per_week.groupby(['sub_location'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_branches_by_repayment_weekly,
                                x='repayment_by_date',
                                y='sub_location',
                                orientation='h',
                                title="Top 10 Repaying Branches (This Week)",
                                color='repayment_by_date',
                                color_continuous_scale='sunset')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col5:
                    # Top 10 Repaying Branches (This Month)
                    top_branches_by_repayment_monthly = (
                        repayment_per_month.groupby(['sub_location'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_branches_by_repayment_monthly,
                                x='repayment_by_date',
                                y='sub_location',
                                orientation='h',
                                title="Top 10 Repaying Branches (This Month)",
                                color='repayment_by_date',
                                color_continuous_scale='sunset')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                with col6:
                    # Top 10 Repaying Branches (All Time)
                    top_branches_by_repayment_all_time = (
                        merged_repay.groupby(['sub_location'])['repayment_by_date']
                        .sum()
                        .reset_index()
                        .sort_values(by='repayment_by_date', ascending=False)
                        .head(10)
                    )

                    fig = px.bar(top_branches_by_repayment_all_time,
                                x='repayment_by_date',
                                y='sub_location',
                                orientation='h',
                                title="Top 10 Repaying Branches (All Time)",
                                color='repayment_by_date',
                                color_continuous_scale='sunset')

                    # Update layout to reduce background opacity
                    fig.update_layout(
                        paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                        plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                        modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                    )

                    st.plotly_chart(fig, use_container_width=True)

                st.markdown("---")

    ########################################################################### Save Files ###################################################################

                # Gets daily repayments without holidays
                daily_repayment_by_agents_ = exp_till_today_.copy() # copy
                daily_repayment_by_agents__ = daily_repayment_by_agents_.merge(new_filtered_data, how='right', on='id')
                daily_repayment_by_agents__ = daily_repayment_by_agents__[(daily_repayment_by_agents__["status"] == "OPEN") & (daily_repayment_by_agents__["start_date"] <= cur_date) & (daily_repayment_by_agents__["end_date"] >= cur_date)]
                DailyRepayments = daily_repayment_by_agents__.groupby(['agent_id', 'agent_full_name', 'agent_email', 'agent_contact', 'users_full_name', 'users_phone_number'])['exp_repay_today'].sum().reset_index()

                # Gets daily repayments without holidays
                daily_repayment_by_agents = repayment_today.groupby(['agent_id', 'agent_full_name', 'agent_email', 'agent_contact', 'users_full_name', 'users_phone_number'])['daily_repayment_amount'].sum().reset_index()

                c1, c2 = st.columns(2)

                with c1:
                    # Display filtered data inside an expander
                    with st.expander("Daily Repayments by agents"):
                        st.dataframe(daily_repayment_by_agents)
                        # Provide feedback for skipped filters
                        if daily_repayment_by_agents.empty:
                            st.warning("No data found for the selected filters. Please adjust your selections.")
                        else:
                            st.success(f"Displaying {len(daily_repayment_by_agents)} records.")

                with c2:
                    # Display filtered data inside an expander
                    with st.expander("Expected Collection by agents without holidays"):
                        st.dataframe(DailyRepayments)
                        # Provide feedback for skipped filters
                        if DailyRepayments.empty:
                            st.warning("No data found for the selected filters. Please adjust your selections.")
                        else:
                            st.success(f"Displaying {len(DailyRepayments)} records.")

                c1, c2 = st.columns(2)

                # Select and arrange all repayment data columns
                past_due_date = past_due_date[[
                    'verticals', 'vertical_lead_name', 'vertical_lead_email', 'vertical_lead_contact',
                    'sup_name', 'sup_email', 'sup_contact', 'location', 'sub_location', 'agent_full_name', 'agent_contact',
                    'agent_email', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'guarantor_phone_number',
                    'status', 'loan_type', 'date_disbursed', 'start_date', 'end_date', 'amount', 'interest_amount', 'daily_repayment_amount', 'repayment',
                    'exp_repay_today', 'repayment_rate', 'expected_repayment', 'completion_rate', 'open_portfolio', 'missed_days',
                    'missed_repayment', 'loan_age(days)', 'loan_age(months)', 'num_of_month', 'past_maturity', 'Loss_Category'
                ]]

                # Rename columns for clarity
                data_column_mappings_ = {
                    "verticals": "Vertical",
                    "vertical_lead_name": "Vertical Lead",
                    "vertical_lead_email": "VL email",
                    "vertical_lead_contact": "VL contact",
                    "sup_name": "Supervisor",
                    "sup_email": "Supervisor email",
                    "sup_contact": "Supervisor contact",
                    "location": "Branch",
                    "sub_location": "Sub-Branch",
                    "agent_full_name": "Loan Officer",
                    "agent_email": "LO email",
                    "agent_contact": "LO contact",
                    "users_full_name": "Name",
                    "users_phone_number": "Contact",
                    "users_address": "Address",
                    "users_trade": "Business",
                    "guarantor_phone_number": "Guarantor Contact",
                    "status": "Status",
                    "loan_type": "Loan Type",
                    "date_disbursed": "Disbursed Date",
                    "start_date": "Start Date",
                    "end_date": "Maturity Date",
                    "amount": "Principal",
                    "interest_amount": "Interest",
                    "daily_repayment_amount": "Daily Repayment",
                    "repayment": "Collection",
                    "exp_repay_today": "Expected Collection",
                    "repayment_rate": "Collection rate (%)",
                    "expected_repayment": "Total Due",
                    "completion_rate": "Completion rate (%)",
                    "open_portfolio": "Pending Due",
                    "missed_days": "Days Missed",
                    "missed_repayment": "Missed Amount",
                    "past_maturity": "Days Past Maturity",
                    "Loss_Category": "P/M Category",
                    "loan_age(days)": "Loan Age (Days)",
                    "loan_age(months)": "Loan Age (Months)",
                    "num_of_month": "Duration (Months)"
                }

                past_due_date.rename(columns=data_column_mappings_, inplace=True)

                with c1:
                    # Display filtered data inside an expander
                    with st.expander("Loans past maturity"):
                        st.dataframe(past_due_date)
                        # Provide feedback for skipped filters
                        if past_due_date.empty:
                            st.warning("No data found for the selected filters. Please adjust your selections.")
                        else:
                            st.success(f"Displaying {len(past_due_date)} records.")

                with c2:
                    # Show duplicated rows based on 'id'
                    duplicate_ids = filtered_data[filtered_data.duplicated(subset='id', keep=False)]
                    # Display filtered data inside an expander
                    with st.expander("🔁 Duplicate loan record"):
                        st.dataframe(duplicate_ids)
                        # Provide feedback for skipped filters
                        if duplicate_ids.empty:
                            st.success("No loan duplicate record. Dashboard is clean 👍")
                        else:
                            st.warning(f"Displaying {len(duplicate_ids)} records.")

                pass
            except (ValueError, IndexError, KeyError, AttributeError):
                st.warning("Not enough dataset to load this, Kindly skip.")

        ########################################################################### Customer reports ###################################################################

            ## Copy dataset to get last date in repayment
            last_date_repayment = filtered_repayment_df.copy()
            ## Gets the last date by sorting and grouping
            last_date_repayment = last_date_repayment.sort_values(by=['ajo_loan_id', 'paid_date']).groupby('ajo_loan_id').last().reset_index()

            @st.cache_data(show_spinner=False)
            def count_valid_days(start_date, end_date, exceptions):
                count = 0
                current = start_date + timedelta(days=1)
                while current <= end_date:
                    if current.weekday() < 5 and current not in exceptions:
                        count += 1
                    current += timedelta(days=1)
                return count

            @st.cache_data(show_spinner=False)
            def generate_customer_report(filtered_data, last_date_repayment, holiday):
                # Merge loans with last payment date
                customer_report = filtered_data.merge(last_date_repayment, how='left', left_on='id', right_on='ajo_loan_id')

                # Ensure 'paid_date' is datetime
                customer_report['paid_date'] = pd.to_datetime(customer_report['paid_date'], errors='coerce')

                # Ensure holiday['date'] is datetime and create a set of exception dates
                holiday['date'] = pd.to_datetime(holiday['date'], errors='coerce')
                exception_dates = set(holiday['date'].dropna().dt.date)

                # Today's date
                today = pd.to_datetime("today").normalize().date()

                # Apply business day count
                customer_report['Days Past'] = customer_report['paid_date'].apply(
                    lambda x: count_valid_days(x.date(), today, exception_dates) if pd.notnull(x) else None
                )

                # Clean fields
                customer_report['repayment'] = customer_report['repayment'].fillna(0)
                customer_report['exp_repay_today'] = customer_report['exp_repay_today'].fillna(0)
                customer_report['status'] = customer_report['status'].astype(str).str.strip().str.upper()

                # Customer status classification
                conditions_status = [
                    (customer_report['status'] != 'COMPLETED') & customer_report['Days Past'].isna() & (customer_report['repayment'] > customer_report['exp_repay_today']),
                    (customer_report['status'] != 'COMPLETED') & customer_report['Days Past'].isna(),
                    (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] >= 5) & (customer_report['repayment'] > customer_report['exp_repay_today']),
                    (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] >= 5) & (customer_report['repayment'] == customer_report['exp_repay_today']),
                    (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] >= 5),
                    (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] < 5) & (customer_report['repayment'] < customer_report['exp_repay_today']),
                    (customer_report['status'] != 'COMPLETED') & (customer_report['Days Past'] < 5) & (customer_report['repayment'] == customer_report['exp_repay_today']),
                    (customer_report['status'] != 'COMPLETED')
                ]
                choices_status = [
                    'active_customer',
                    'inactive_customer',
                    'performing_customer',
                    'partially_active',
                    'inactive_customer',
                    'underperforming_customer',
                    'partially_active',
                    'active_customer'
                ]
                customer_report['customer_status'] = np.select(conditions_status, choices_status, default='no-activity')

                # Customer performance
                conditions_perf = [
                    (customer_report['repayment_rate'] == 100),
                    (customer_report['repayment_rate'] >= 85),
                    (customer_report['repayment_rate'] >= 50),
                    (customer_report['repayment_rate'] < 50)
                ]
                choices_perf = ['safe', 'inactive', 'risky', 'highly_risky']
                customer_report['customer_performance'] = np.select(conditions_perf, choices_perf, default='unknown')

                # Days log categorization
                bins = [-np.inf, 0, 15, 30, 45, 60, 90, np.inf]
                labels = ['0 days', '1-15 days', '16-30 days', '31-45 days', '46-60 days', '61-90 days', '90+ days']
                customer_report['days_log'] = pd.cut(customer_report['Days Past'], bins=bins, labels=labels)

                # Customer grade
                conditions_grade = [
                    (customer_report['customer_performance'] == 'highly_risky') & (customer_report['Days Past'] > 30),
                    (customer_report['customer_performance'] == 'highly_risky') & (customer_report['Days Past'] <= 30),
                    (customer_report['customer_performance'] == 'risky') & (customer_report['Days Past'] > 30),
                    (customer_report['customer_performance'] == 'risky') & (customer_report['Days Past'] <= 30),
                ]
                choices_grade = ['Grade 1', 'Grade 2', 'Grade 3', 'Grade 4']
                customer_report['customer_grade'] = np.select(conditions_grade, choices_grade, default=None)

                return customer_report

            # Generate customer report
            customer_report = generate_customer_report(filtered_data, last_date_repayment, holiday)

            # Select and arrange all repayment data columns
            customer_report = customer_report[[
                'verticals', 'vertical_lead_name', 'vertical_lead_email', 'vertical_lead_contact',
                'sup_name', 'sup_email', 'sup_contact', 'location', 'sub_location', 'agent_full_name', 'agent_contact',
                'agent_email', 'users_full_name', 'users_phone_number', 'users_address', 'users_trade', 'guarantor_phone_number',
                'status', 'loan_type', 'date_disbursed', 'start_date', 'end_date', 'amount', 'interest_amount', 'daily_repayment_amount', 'repayment',
                'exp_repay_today', 'repayment_rate', 'expected_repayment', 'completion_rate', 'open_portfolio', 'missed_days',
                'missed_repayment', 'Days Past', 'customer_status', 'customer_performance', 'customer_grade'
            ]]

            # Rename columns for clarity
            data_column_mappings__ = {
                "verticals": "Vertical",
                "vertical_lead_name": "Vertical Lead",
                "vertical_lead_email": "VL email",
                "vertical_lead_contact": "VL contact",
                "sup_name": "Supervisor",
                "sup_email": "Supervisor email",
                "sup_contact": "Supervisor contact",
                "location": "Branch",
                "sub_location": "Sub-Branch",
                "agent_full_name": "Loan Officer",
                "agent_email": "LO email",
                "agent_contact": "LO contact",
                "users_full_name": "Name",
                "users_phone_number": "Contact",
                "users_address": "Address",
                "users_trade": "Business",
                "guarantor_phone_number": "Guarantor Contact",
                "status": "Status",
                "loan_type": "Loan Type",
                "date_disbursed": "Disbursed Date",
                "start_date": "Start Date",
                "end_date": "Maturity Date",
                "amount": "Principal",
                "interest_amount": "Interest",
                "daily_repayment_amount": "Daily Repayment",
                "repayment": "Collection",
                "exp_repay_today": "Expected Collection",
                "repayment_rate": "Collection rate (%)",
                "expected_repayment": "Total Due",
                "completion_rate": "Completion rate (%)",
                "open_portfolio": "Pending Due",
                "missed_days": "Days Missed",
                "missed_repayment": "Missed Amount",
                "past_maturity": "Days Past Maturity",
                "Loss_Category": "P/M Category",
                "loan_age(days)": "Loan Age (Days)",
                "loan_age(months)": "Loan Age (Months)",
                "num_of_month": "Duration (Months)"
            }

            customer_report.rename(columns=data_column_mappings__, inplace=True)

            # Display filtered data inside an expander
            with st.expander("Customer Activity"):
                st.dataframe(customer_report)
                # Provide feedback for skipped filters
                if customer_report.empty:
                    st.warning("No data found for the customer report selected, kindly recheck loan metrics and re-run.")
                else:
                    st.success(f"Displaying {len(customer_report)} records.")

    st.markdown(
        """
        <style>
        .sidebar-footer {
            position: fixed;
            left: 0;
            bottom: 0;
            width: 100%;
            opacity: 90%;
            background: linear-gradient(to bottom,rgba(32, 178, 170, 0.7), rgba(60, 179, 113, 0.7));
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.3); /* Added shadow */
            color: white;
            text-align: left; /* Align text to the left for moving effect */
            padding: 10px 0;
            font-weight: bold;
            font-size: 1.2em;
            letter-spacing: 6px;
            z-index: 1000;
            overflow: hidden; /* Hide overflowing text */
            white-space: nowrap; /* Prevent text from wrapping */
        }

        .moving-text {
            display: inline-block;
            padding-left: 100%; /* Start offscreen to the right */
            animation: marquee 95s linear infinite; /* Adjust speed as needed */
        }

        @keyframes marquee {
            0% {
                transform: translateX(0%);
            }
            100% {
                transform: translateX(-100%); /* Move offscreen to the left */
            }
        }
        </style>
        <div class="sidebar-footer">
            <span class="moving-text"> Liberty Assured - be liberated
            || Seeds By Liberty : Ajo, BNPL, Prosper Loans, Boosta, Boosta_2x, Boosta_2x_mini
            || This Dashboard Provides A Comprehensive Overview Of Loan, Helping Users Identify Trends, Relationship Between Variables, Performance Metrics, And Other Areas
            || Time-Series Analysis for Loan Overview, Past Maturity & Open Portfolio, Cohort/Cumulative Based Trends
            || Check repayment progress as agents make collections on a daily, weekly, or monthly basis
            || Refine by loan type, get summarized/breakdown record by supervisor/agent/merchant/borrower
            || Insights Provided Into The Loan Data, Including Agent Performance, Bad Loans, Late Repayments, Trades Collections, And Branch Analysis For This Month And All Time Side By Side
            || Get Past Maturity, Customer Performance, Loans Expiring Within 30 days
            || Growing Futures, Enriching lives : Empowering The Underserved With Every Penny and Seed
            || Seeds and Pennies - Your Financial Partner </span>
        </div>
        """,
        unsafe_allow_html=True,
    )