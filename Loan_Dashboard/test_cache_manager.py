"""
Test script for cache manager functionality
Run this to verify the cache manager works correctly
"""

import streamlit as st
import time
from datetime import datetime, timedelta
from cache_manager import initialize_auto_cache_clearing, get_cache_status, display_cache_status, manual_cache_clear

def test_cache_manager():
    st.title("Cache Manager Test")
    
    # Initialize cache system
    cache_cleared = initialize_auto_cache_clearing()
    
    # Display current status
    st.subheader("Current Cache Status")
    display_cache_status(cache_cleared)
    
    # Show detailed status
    status = get_cache_status()
    st.json(status)
    
    # Manual controls
    st.subheader("Manual Controls")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("Manual Clear Cache"):
            if manual_cache_clear():
                st.success("Cache cleared successfully!")
                st.rerun()
            else:
                st.error("Failed to clear cache")
    
    with col2:
        if st.button("Simulate 5 Minutes Passed"):
            # Simulate 5 minutes have passed
            st.session_state.last_auto_cache_clear = datetime.now() - timedelta(minutes=6)
            st.success("Simulated 6 minutes passed - cache should clear on next interaction")
            st.rerun()
    
    # Auto refresh every 30 seconds to show live updates
    if st.button("Enable Auto Refresh (30s)"):
        time.sleep(30)
        st.rerun()

if __name__ == "__main__":
    test_cache_manager()
