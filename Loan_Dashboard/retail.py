import pandas as pd
import numpy as np
from datetime import timedelta, date, datetime
import warnings
import plotly.express as px
import plotly.graph_objects as go
import streamlit as st
import time
import importlib
import sys
import types
import os
import base64
import queries
from queries import fund_agent_df_, payout_df_, africa_lotto_df_, lotto_ticket_df_, lottery_model_df_, agent_df_ # remittance_df_
warnings.filterwarnings('ignore')

def show_retail_dashboard(lotto_df):
    # Gets dataframe
    wallet_trans_df = lotto_df
    fund_agent_df = fund_agent_df_
    payout_df = payout_df_
    # remittance_df = remittance_df_
    africa_lotto_df = africa_lotto_df_
    lotto_ticket_df = lotto_ticket_df_
    lottery_model_df = lottery_model_df_
    # agent_df = agent_df_

    # sets logo
    st.logo("winwise logo.png", size="large")
    
    # Sets background image and color
    @st.cache_data(show_spinner=False)
    def set_background(image_path):
        if not os.path.exists(image_path):
            st.error(
                f"Image '{image_path}' not found. Ensure the file is in the correct directory."
            )
            return

        with open(image_path, "rb") as f:
            encoded_image = base64.b64encode(f.read()).decode()

        background_style = f"""
        <style>
        /* Darker background with solid dark blue */
        .stApp {{
            background: linear-gradient(to bottom, #00001, #000015); /* Darker blue */
        }}

        /* Sidebar styling - dark grey */
        [data-testid="stSidebar"] {{
            background-color: #33333 !important; /* dark grey */
        }}

        /* Centered transparent text image */
        .background-container {{
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40%; /* Adjust width if needed */
            text-align: center;
            z-index: 0; /* Ensure image is behind the dashboard and infront of the background color */
        }}

        .background-container img {{
            width: 100%;
            height: auto;
            opacity: 0.1; /* Set to minmal visibility */
        }}
        </style>

        <div class="background-container">
            <img src="data:image/png;base64,{encoded_image}">
        </div>
        """

        st.markdown(background_style, unsafe_allow_html=True)

    # Call the function with your image
    set_background("winwise logo.png")

    st.sidebar.markdown('---')

    ########################################################################### Create date range #######################################################################################
    # Convert to datetime and extract time
    @st.cache_data(show_spinner=False)
    def process_datetime_columns(df, datetime_col, prefix=None):
        """
        Standardizes and extracts time-related features from a datetime column.

        Parameters:
            df (pd.DataFrame): DataFrame to process.
            datetime_col (str): Name of the datetime column.
            prefix (str, optional): Optional prefix for new columns. If None, base name is used.

        Returns:
            pd.DataFrame: The DataFrame with new 'date', 'time', and 'month' columns.
        """
        col_prefix = prefix if prefix else datetime_col
        df['time'] = pd.to_datetime(df[datetime_col]).dt.time
        df['date'] = pd.to_datetime(df[datetime_col]).dt.normalize().dt.tz_localize(None)
        df['month'] = df['date'].dt.strftime('%b %Y')
        return df

    # Apply standardized processing
    # agent_df = process_datetime_columns(agent_df, 'last_updated')
    wallet_trans_df = process_datetime_columns(wallet_trans_df, 'date_created')
    fund_agent_df = process_datetime_columns(fund_agent_df, 'created_at')
    payout_df = process_datetime_columns(payout_df, 'date_added')
    # remittance_df = process_datetime_columns(remittance_df, 'created_at')
    africa_lotto_df = process_datetime_columns(africa_lotto_df, 'created_at')
    lotto_ticket_df = process_datetime_columns(lotto_ticket_df, 'date')
    lottery_model_df = process_datetime_columns(lottery_model_df, 'date')
    # lottery_model_df['paid_date'] = pd.to_datetime(lottery_model_df['paid_date']).dt.normalize().dt.tz_localize(None)
    # lottery_model_df['paid_date_month'] = lottery_model_df['paid_date'].dt.strftime('%b %Y')

    # all_time button
    all_time = st.sidebar.checkbox("All Time", value=False)
    # start date
    start_date = st.sidebar.date_input("View Dashboard From ...", wallet_trans_df['date'].dropna().min())
    # end date
    end_date = st.sidebar.date_input("View Dasboard Till ...", date.today())

    # Checks for selected date range and if date range is valid
    if all_time:
        st.sidebar.success(f"All time from {start_date} to {end_date}")
    elif (start_date > end_date) and not all_time:
        st.sidebar.error("Error: Start Date must be before End Date.")
    else:
        st.sidebar.success(f"Date range: {start_date} to {end_date}")

    ############################################################################ Sets conditions for date filtering #######################################################################################

    # Override date filters if "All Time" is selected
    wallet_trans_df['date'] = pd.to_datetime(wallet_trans_df['date'])
    if all_time:
        start_date, end_date = pd.to_datetime(wallet_trans_df["date"].dropna().min()), pd.to_datetime(date.today())
    else:
        wallet_trans_df = wallet_trans_df[
            (wallet_trans_df["date"] >= pd.to_datetime(start_date)) &
            (wallet_trans_df["date"] <= pd.to_datetime(end_date))
        ]

    st.sidebar.markdown('---')

    ########################################################################### set lotto sidebar #####################################################################################

    # Sidebar: Wave selection based on wallet_trans_df
    selected_waves = st.sidebar.multiselect(
        'Select Wave',
        options=wallet_trans_df['wave'].dropna().unique().tolist(),
        default=None
    )

    # Filter by wave using agent_name as the join key
    if selected_waves:
        # Get agents in selected waves from wallet_trans_df
        agents_in_selected_waves = wallet_trans_df[wallet_trans_df['wave'].isin(selected_waves)]['agent_name'].unique()

        # Filter all related DataFrames using agent_name
        wallet_trans_df = wallet_trans_df[wallet_trans_df['agent_name'].isin(agents_in_selected_waves)]
        # agent_df = agent_df[agent_df['agent_name'].isin(agents_in_selected_waves)]
        fund_agent_df = fund_agent_df[fund_agent_df['agent_name'].isin(agents_in_selected_waves)]
        payout_df = payout_df[payout_df['agent_name'].isin(agents_in_selected_waves)]
        # remittance_df = remittance_df[remittance_df['agent_name'].isin(agents_in_selected_waves)]
        lottery_model_df = lottery_model_df[lottery_model_df['agent_name'].isin(agents_in_selected_waves)]
        africa_lotto_df = africa_lotto_df[africa_lotto_df['agent_name'].isin(agents_in_selected_waves)]
        lotto_ticket_df = lotto_ticket_df[lotto_ticket_df['agent_name'].isin(agents_in_selected_waves)]

    # Select Vertical Lead
    selected_vertical_lead = st.sidebar.multiselect(
        'Select Vertical Lead',
        options=sorted(wallet_trans_df['vertical_full_name'].dropna().unique())
    )
    # Filter supervisors strictly under selected vertical
    if selected_vertical_lead:
        filtered_supervisor_df = wallet_trans_df[
            wallet_trans_df['vertical_full_name'].isin(selected_vertical_lead)
        ]
        supervisor_options = sorted(filtered_supervisor_df['supervisor_name'].dropna().unique())
    else:
        filtered_supervisor_df = wallet_trans_df.copy()
        supervisor_options = sorted(filtered_supervisor_df['supervisor_name'].dropna().unique())

    # Select Supervisor (strictly from filtered options)
    selected_supervisors = st.sidebar.multiselect(
        'Select Supervisor',
        options=supervisor_options
    )
    # Filter agents strictly based on both selected vertical & supervisor
    if selected_supervisors:
        filtered_agents_df = filtered_supervisor_df[
            filtered_supervisor_df['supervisor_name'].isin(selected_supervisors)
        ]
        agent_options = sorted(filtered_agents_df['agent_name'].dropna().unique())
    else:
        filtered_agents_df = filtered_supervisor_df.copy()
        agent_options = sorted(filtered_agents_df['agent_name'].dropna().unique())

    # Select Agent (filtered strictly by vertical & supervisor)
    selected_agents = st.sidebar.multiselect(
        'Select Agent',
        options=agent_options
    )

    # Get lotto_agent_ids based on all 3 filters
    if selected_agents:
        lotto_selected_agent_ids = wallet_trans_df[
            wallet_trans_df['agent_name'].isin(selected_agents)
        ]['lotto_agent_id'].unique()
    elif selected_supervisors:
        lotto_selected_agent_ids = wallet_trans_df[
            wallet_trans_df['supervisor_name'].isin(selected_supervisors)
        ]['lotto_agent_id'].unique()
    elif selected_vertical_lead:
        lotto_selected_agent_ids = wallet_trans_df[
            wallet_trans_df['vertical_full_name'].isin(selected_vertical_lead)
        ]['lotto_agent_id'].unique()
    else:
        lotto_selected_agent_ids = wallet_trans_df['lotto_agent_id'].unique()

    # Apply filtering to all datasets
    # agent_df = agent_df[agent_df['id'].isin(lotto_selected_agent_ids)].copy()
    fund_agent_df = fund_agent_df[fund_agent_df['lotto_agent_id'].isin(lotto_selected_agent_ids)].copy()
    wallet_trans_df = wallet_trans_df[wallet_trans_df['lotto_agent_id'].isin(lotto_selected_agent_ids)].copy()
    payout_df = payout_df[payout_df['lotto_agent_id'].isin(lotto_selected_agent_ids)].copy()
    # remittance_df = remittance_df[remittance_df['lotto_agent_id'].isin(lotto_selected_agent_ids)].copy()
    lottery_model_df = lottery_model_df[lottery_model_df['lotto_agent_id'].isin(lotto_selected_agent_ids)].copy()
    africa_lotto_df = africa_lotto_df[africa_lotto_df['lotto_agent_id'].isin(lotto_selected_agent_ids)].copy()
    lotto_ticket_df = lotto_ticket_df[lotto_ticket_df['lotto_agent_id'].isin(lotto_selected_agent_ids)].copy()

    st.sidebar.markdown("---")

    # Set sidebar for laons and customer activity
    st.sidebar.write('Click to download files')
    with st.sidebar.popover('Download WinWise Records'):
        ## to downlaod the file for each and store in a sidebar using .popover() keeping it readable...
        ## ... and simple for users to access dataframes instead of viewing them and consuming space in the dashboard
        # File 1
        file1, mid = st.columns([1, 3])
        with mid:
            st.write("### Wallet Transaction")

        # File 2
        file2, mid = st.columns([1, 3])
        with mid:
            st.write("### Agent Funding")

        # File 3
        file3, mid = st.columns([1, 3])
        with mid:
            st.write("### Payouts")

        # File 4
        file4, mid = st.columns([1, 3])
        with mid:
            st.write("### Africa Lotto (RTP/RTO)")

        # File 5
        file5, mid = st.columns([1, 3])
        with mid:
            st.write("### Lotto Ticket (RTP/RTO)")

        # File 6
        file6, mid = st.columns([1, 3])
        with mid:
            st.write("### Lottery Model (RTP/RTO)")

        # File 7
        file7, mid = st.columns([1, 3])
        with mid:
            st.write("### Aggregated Monthly Data")

    st.sidebar.markdown("---")

    ########################################################################### set sidebar logout and reset buttons #####################################################################################

    # Define reload function to re-import from queries.py
    def reload_queries_functions():
        global wallet_trans_df_, fund_agent_df_, payout_df_, africa_lotto_df_, lotto_ticket_df_, lottery_model_df_, agent_df_ #remittance_df_

        from queries import (
            wallet_trans_df_, fund_agent_df_, payout_df_, africa_lotto_df_, lotto_ticket_df_, lottery_model_df_, agent_df_ #remittance_df_
        )

    # Clear all caches including queries.py
    def clear_all_caches():
        st.cache_data.clear()
        st.cache_resource.clear()

        try:
            queries.clear_cache() # Call function in queries.py
            importlib.reload(queries) # Reload the module
            reload_queries_functions() # Re-import all functions
        except Exception as e:
            st.warning(f"Failed to clear queries cache: {e}")

    # Sidebar Buttons
    c1, c2 = st.sidebar.columns([1, 1])

    with c1:
        st.markdown(
            """
            <style>
            div.stButton > button {
                width: 100%;
            }
            </style>
            """,
            unsafe_allow_html=True,
        )
        if st.button("Clear Memory"):
            clear_all_caches()
            st.success("All memory caches cleared successfully!")
            time.sleep(2)
            st.rerun()

    with c2:
        st.markdown(
            """
            <style>div.stButton > button {width: 100%;}</style>
            """,
            unsafe_allow_html=True,
        )
        if st.button("Logout"):
            st.session_state["logged_in"] = False
            st.session_state["role"] = None
            st.session_state["username"] = None
            st.session_state["page"] = "login"
            st.success("You have been logged out.")
            time.sleep(3)
            st.rerun()

    # Filter loans with status 'OPEN' and end_date greater than today
    cur_date = pd.to_datetime(end_date)

    ############################################################################ Dashboard graphs and plots #######################################################################################

    # splits dashboard into tabs
    tab1, tab2, tab3 = st.tabs(["Retail Overview", "Retail Analysis", "Product Analysis"])

    ############################################################################ Tab 1 #######################################################################################

    with tab1:
        # Get today's date
        today = pd.to_datetime(cur_date)
        # Calculate yesterday's date
        yesterday = today - pd.DateOffset(days=1)
        # Calculate the day before yesterday's date
        day_before_yesterday = today - pd.DateOffset(days=2)
        # Calculate the start of this week
        start_of_week = today - pd.DateOffset(days=today.weekday())  # Monday as start of week
        # Calculate the start of last week (Monday)
        start_of_last_week = start_of_week - pd.DateOffset(weeks=1)
        # Calculate the start of two weeks ago (Monday)
        start_of_last_2_weeks = start_of_week - pd.DateOffset(weeks=2)
        # Calculate the start of this month
        start_of_month = today.replace(day=1)
        # Calculate the start of last month
        start_of_last_month = (start_of_month - pd.DateOffset(days=1)).replace(day=1)
        # Calculate the start of two months ago
        start_of_last_2_months = (start_of_last_month - pd.DateOffset(days=1)).replace(day=1)
        # Calculate the start of this year
        start_of_year = today.replace(month=1, day=1)
        # Calculate the start of last year
        start_of_last_year = start_of_year - pd.DateOffset(years=1)
        # Calculate the start of two years ago
        start_of_last_2_years = start_of_last_year - pd.DateOffset(years=1)
        # For 14-day window
        start_of_14_days_ago = today - pd.DateOffset(days=13)
        end_of_14_day_window = start_of_14_days_ago + pd.DateOffset(days=13)
        upper_limit_14_days = min(today, end_of_14_day_window)
        # For 30-day window
        start_of_30_days_ago = today - pd.DateOffset(days=29)
        end_of_30_day_window = start_of_30_days_ago + pd.DateOffset(days=29)
        upper_limit_30_days = min(today, end_of_30_day_window)
        # For 2-day window
        start_of_last_2_days = today - pd.DateOffset(days=1)
        end_of_2_day_window = start_of_last_2_days + pd.DateOffset(days=1)
        upper_limit_2_days = min(today, end_of_2_day_window)
        # Last day of the current week (Sunday)
        end_of_week = today + pd.DateOffset(days=(6 - today.weekday()))
        upper_limit_week = min(today, end_of_week)
        # Last day (Sunday) of last week
        end_of_last_week = start_of_week - pd.DateOffset(days=1)
        upper_limit_last_week = min(today, end_of_last_week)
        # Last day (Sunday) of last 2 weeks
        end_of_last_2_weeks = end_of_last_week - pd.DateOffset(weeks=1)
        upper_limit_last_2_weeks = min(today, end_of_last_2_weeks)
        # Last day of the current month
        end_of_month = today + pd.offsets.MonthEnd(0)
        upper_limit_month = min(today, end_of_month)
        # End of last month
        end_of_last_month = today.replace(day=1) - pd.DateOffset(days=1)
        upper_limit_last_month = min(today, end_of_last_month)
        # End of 2 months ago
        end_of_last_2_months = (end_of_last_month.replace(day=1) - pd.DateOffset(days=1))
        upper_limit_last_2_months = min(today, end_of_last_2_months)
        # Last day of the current year
        end_of_year = today + pd.offsets.YearEnd(0)
        upper_limit_year = min(today, end_of_year)
        # End of last year
        end_of_last_year = today.replace(month=1, day=1) - pd.DateOffset(days=1)
        upper_limit_last_year = min(today, end_of_last_year)
        # End of 2 years ago
        end_of_last_2_years = (end_of_last_year.replace(month=1, day=1) - pd.DateOffset(days=1))
        upper_limit_last_2_years = min(today, end_of_last_2_years)

        # Filter datasets for winnings by date
        payout_df_today = payout_df[payout_df['date'] == today]
        payout_df_yesterday = payout_df[payout_df['date'] == yesterday]
        payout_df_day_before_yesterday = payout_df[payout_df['date'] == day_before_yesterday]
        payout_df_week = payout_df[
            (payout_df['date'] >= start_of_week) & 
            (payout_df['date'] <= upper_limit_week)
        ]
        payout_df_last_week = payout_df[
            (payout_df['date'] >= start_of_last_week) & 
            (payout_df['date'] <= upper_limit_last_week)
        ]
        payout_df_last_2_weeks = payout_df[
            (payout_df['date'] >= start_of_last_2_weeks) & 
            (payout_df['date'] <= upper_limit_last_2_weeks)
        ]
        payout_df_month = payout_df[payout_df['date'] >= start_of_month]
        payout_df_month = payout_df[
            (payout_df['date'] >= start_of_month) & 
            (payout_df['date'] <= upper_limit_month)
        ]
        payout_df_last_month = payout_df[
            (payout_df['date'] >= start_of_last_month) & 
            (payout_df['date'] <= upper_limit_last_month)
        ]
        payout_df_last_2_months = payout_df[
            (payout_df['date'] >= start_of_last_2_months) & 
            (payout_df['date'] <= upper_limit_last_2_months)
        ]
        payout_df_year = payout_df[payout_df['date'] >= start_of_year]
        payout_df_year = payout_df[
            (payout_df['date'] >= start_of_year) & 
            (payout_df['date'] <= upper_limit_year)
        ]
        payout_df_last_year = payout_df[
            (payout_df['date'] >= start_of_last_year) & 
            (payout_df['date'] <= upper_limit_last_year)
        ]
        payout_df_last_2_years = payout_df[
            (payout_df['date'] >= start_of_last_2_years) & 
            (payout_df['date'] <= upper_limit_last_2_years)
        ]
        payout_df_all_time = payout_df[payout_df['date'] <= today]

        # Aggregate data for winnings
        payout_df_today_total = payout_df_today['amount'].sum()
        payout_df_yesterday_total = payout_df_yesterday['amount'].sum()
        payout_df_day_before_yesterday_total = payout_df_day_before_yesterday['amount'].sum()
        payout_df_week_total = payout_df_week['amount'].sum()
        payout_df_last_week_total = payout_df_last_week['amount'].sum()
        payout_df_last_2_weeks_total = payout_df_last_2_weeks['amount'].sum()
        payout_df_month_total = payout_df_month['amount'].sum()
        payout_df_last_month_total = payout_df_last_month['amount'].sum()
        payout_df_last_2_months_total = payout_df_last_2_months['amount'].sum()
        payout_df_year_total = payout_df_year['amount'].sum()
        payout_df_last_year_total = payout_df_last_year['amount'].sum()
        payout_df_last_2_years_total = payout_df_last_2_years['amount'].sum()
        payout_df_all_time_total = payout_df_all_time['amount'].sum()

        # Filter datasets for active agents by date
        game_wallet_trans_df = wallet_trans_df[wallet_trans_df['transaction_from']=='GAME_PLAY']
        winning_wallet_trans_df = wallet_trans_df[wallet_trans_df['transaction_from']=='WINNINGS']

        game_wallet_trans_df_today = game_wallet_trans_df[game_wallet_trans_df['date'] == today]
        game_wallet_trans_df_yesterday = game_wallet_trans_df[game_wallet_trans_df['date'] == yesterday]
        game_wallet_trans_df_day_before_yesterday = game_wallet_trans_df[game_wallet_trans_df['date'] == day_before_yesterday]
        game_wallet_trans_df_week = game_wallet_trans_df[
            (game_wallet_trans_df['date'] >= start_of_week) & 
            (game_wallet_trans_df['date'] <= upper_limit_week)
        ]        
        game_wallet_trans_df_last_week = game_wallet_trans_df[
            (game_wallet_trans_df['date'] >= start_of_last_week) & 
            (game_wallet_trans_df['date'] <= upper_limit_last_week)
        ]
        game_wallet_trans_df_last_2_weeks = game_wallet_trans_df[
            (game_wallet_trans_df['date'] >= start_of_last_2_weeks) & 
            (game_wallet_trans_df['date'] <= upper_limit_last_2_weeks)
        ]
        game_wallet_trans_df_month = game_wallet_trans_df[
            (game_wallet_trans_df['date'] >= start_of_month) & 
            (game_wallet_trans_df['date'] <= upper_limit_month)
        ]
        game_wallet_trans_df_last_month = game_wallet_trans_df[
            (game_wallet_trans_df['date'] >= start_of_last_month) & 
            (game_wallet_trans_df['date'] <= upper_limit_last_month)
        ]
        game_wallet_trans_df_last_2_months = game_wallet_trans_df[
            (game_wallet_trans_df['date'] >= start_of_last_2_months) & 
            (game_wallet_trans_df['date'] <= upper_limit_last_2_months)
        ]
        game_wallet_trans_df_year = game_wallet_trans_df[
            (game_wallet_trans_df['date'] >= start_of_year) & 
            (game_wallet_trans_df['date'] <= upper_limit_year)
        ]
        game_wallet_trans_df_last_year = game_wallet_trans_df[
            (game_wallet_trans_df['date'] >= start_of_last_year) & 
            (game_wallet_trans_df['date'] <= upper_limit_last_year)
        ]
        game_wallet_trans_df_last_2_years = game_wallet_trans_df[
            (game_wallet_trans_df['date'] >= start_of_last_2_years) & 
            (game_wallet_trans_df['date'] <= upper_limit_last_2_years)
        ]
        game_wallet_trans_df_all_time = game_wallet_trans_df[game_wallet_trans_df['date'] <= today]

        # Aggregate data for sales
        sales_game_wallet_trans_df_today_total = game_wallet_trans_df_today['amount'].sum()
        sales_game_wallet_trans_df_yesterday_total = game_wallet_trans_df_yesterday['amount'].sum()
        sales_game_wallet_trans_df_day_before_yesterday_total = game_wallet_trans_df_day_before_yesterday['amount'].sum()
        sales_game_wallet_trans_df_week_total = game_wallet_trans_df_week['amount'].sum()
        sales_game_wallet_trans_df_last_week_total = game_wallet_trans_df_last_week['amount'].sum()
        sales_game_wallet_trans_df_last_2_weeks_total = game_wallet_trans_df_last_2_weeks['amount'].sum()
        sales_game_wallet_trans_df_month_total = game_wallet_trans_df_month['amount'].sum()
        sales_game_wallet_trans_df_last_month_total = game_wallet_trans_df_last_month['amount'].sum()
        sales_game_wallet_trans_df_last_2_months_total = game_wallet_trans_df_last_2_months['amount'].sum()
        sales_game_wallet_trans_df_year_total = game_wallet_trans_df_year['amount'].sum()
        sales_game_wallet_trans_df_last_year_total = game_wallet_trans_df_last_year['amount'].sum()
        sales_game_wallet_trans_df_last_2_years_total = game_wallet_trans_df_last_2_years['amount'].sum()
        sales_game_wallet_trans_df_all_time_total = game_wallet_trans_df_all_time['amount'].sum()

        # Filter datasets for winnings by date
        winning_wallet_trans_df_today = winning_wallet_trans_df[winning_wallet_trans_df['date'] == today]
        winning_wallet_trans_df_yesterday = winning_wallet_trans_df[winning_wallet_trans_df['date'] == yesterday]
        winning_wallet_trans_df_day_before_yesterday = winning_wallet_trans_df[winning_wallet_trans_df['date'] == day_before_yesterday]
        winning_wallet_trans_df_week = winning_wallet_trans_df[
            (winning_wallet_trans_df['date'] >= start_of_week) & 
            (winning_wallet_trans_df['date'] <= upper_limit_week)
        ]
        winning_wallet_trans_df_last_week = winning_wallet_trans_df[
            (winning_wallet_trans_df['date'] >= start_of_last_week) & 
            (winning_wallet_trans_df['date'] <= upper_limit_last_week)
        ]
        winning_wallet_trans_df_last_2_weeks = winning_wallet_trans_df[
            (winning_wallet_trans_df['date'] >= start_of_last_2_weeks) & 
            (winning_wallet_trans_df['date'] <= upper_limit_last_2_weeks)
        ]
        winning_wallet_trans_df_month = winning_wallet_trans_df[
            (winning_wallet_trans_df['date'] >= start_of_month) & 
            (winning_wallet_trans_df['date'] <= upper_limit_month)
        ]
        winning_wallet_trans_df_last_month = winning_wallet_trans_df[
            (winning_wallet_trans_df['date'] >= start_of_last_month) & 
            (winning_wallet_trans_df['date'] <= upper_limit_last_month)
        ]
        winning_wallet_trans_df_last_2_months = winning_wallet_trans_df[
            (winning_wallet_trans_df['date'] >= start_of_last_2_months) & 
            (winning_wallet_trans_df['date'] <= upper_limit_last_2_months)
        ]
        winning_wallet_trans_df_year = winning_wallet_trans_df[
            (winning_wallet_trans_df['date'] >= start_of_year) & 
            (winning_wallet_trans_df['date'] <= upper_limit_year)
        ]
        winning_wallet_trans_df_last_year = winning_wallet_trans_df[
            (winning_wallet_trans_df['date'] >= start_of_last_year) & 
            (winning_wallet_trans_df['date'] <= upper_limit_last_year)
        ]
        winning_wallet_trans_df_last_2_years = winning_wallet_trans_df[
            (winning_wallet_trans_df['date'] >= start_of_last_2_years) & 
            (winning_wallet_trans_df['date'] <= upper_limit_last_2_years)
        ]
        winning_wallet_trans_df_all_time = winning_wallet_trans_df[winning_wallet_trans_df['date'] <= today]

        # Aggregate data for winnings
        winning_wallet_trans_df_today_total = winning_wallet_trans_df_today['amount'].sum()
        winning_wallet_trans_df_yesterday_total = winning_wallet_trans_df_yesterday['amount'].sum()
        winning_wallet_trans_df_day_before_yesterday_total = winning_wallet_trans_df_day_before_yesterday['amount'].sum()
        winning_wallet_trans_df_week_total = winning_wallet_trans_df_week['amount'].sum()
        winning_wallet_trans_df_last_week_total = winning_wallet_trans_df_last_week['amount'].sum()
        winning_wallet_trans_df_last_2_weeks_total = winning_wallet_trans_df_last_2_weeks['amount'].sum()
        winning_wallet_trans_df_month_total = winning_wallet_trans_df_month['amount'].sum()
        winning_wallet_trans_df_last_month_total = winning_wallet_trans_df_last_month['amount'].sum()
        winning_wallet_trans_df_last_2_months_total = winning_wallet_trans_df_last_2_months['amount'].sum()
        winning_wallet_trans_df_year_total = winning_wallet_trans_df_year['amount'].sum()
        winning_wallet_trans_df_last_year_total = winning_wallet_trans_df_last_year['amount'].sum()
        winning_wallet_trans_df_last_2_years_total = winning_wallet_trans_df_last_2_years['amount'].sum()
        winning_wallet_trans_df_all_time_total = winning_wallet_trans_df_all_time['amount'].sum()

        # Gets all wallet fundings
        fund_agent_df_today = fund_agent_df[fund_agent_df['date'] == today]
        fund_agent_df_yesterday = fund_agent_df[fund_agent_df['date'] == yesterday]
        fund_agent_df_day_before_yesterday = fund_agent_df[fund_agent_df['date'] == day_before_yesterday]
        fund_agent_df_week = fund_agent_df[
            (fund_agent_df['date'] >= start_of_week) & 
            (fund_agent_df['date'] <= upper_limit_week)
        ]
        fund_agent_df_last_week = fund_agent_df[
            (fund_agent_df['date'] >= start_of_last_week) & 
            (fund_agent_df['date'] <= upper_limit_last_week)
        ]
        fund_agent_df_last_2_weeks = fund_agent_df[
            (fund_agent_df['date'] >= start_of_last_2_weeks) & 
            (fund_agent_df['date'] <= upper_limit_last_2_weeks)
        ]
        fund_agent_df_month = fund_agent_df[
            (fund_agent_df['date'] >= start_of_month) & 
            (fund_agent_df['date'] <= upper_limit_month)
        ]
        fund_agent_df_last_month = fund_agent_df[
            (fund_agent_df['date'] >= start_of_last_month) & 
            (fund_agent_df['date'] <= upper_limit_last_month)
        ]
        fund_agent_df_last_2_months = fund_agent_df[
            (fund_agent_df['date'] >= start_of_last_2_months) & 
            (fund_agent_df['date'] <= upper_limit_last_2_months)
        ]
        fund_agent_df_year = fund_agent_df[
            (fund_agent_df['date'] >= start_of_year) & 
            (fund_agent_df['date'] <= upper_limit_year)
        ]
        fund_agent_df_last_year = fund_agent_df[
            (fund_agent_df['date'] >= start_of_last_year) & 
            (fund_agent_df['date'] <= upper_limit_last_year)
        ]
        fund_agent_df_last_2_years = fund_agent_df[
            (fund_agent_df['date'] >= start_of_last_2_years) & 
            (fund_agent_df['date'] <= upper_limit_last_2_years)
        ]
        fund_agent_df_all_time = fund_agent_df[fund_agent_df['date'] <= today]

        # Aggregate data for terminal retrieval
        fund_agent_df_today_total = fund_agent_df_today['amount'].sum()
        fund_agent_df_yesterday_total = fund_agent_df_yesterday['amount'].sum()
        fund_agent_df_day_before_yesterday_total = fund_agent_df_day_before_yesterday['amount'].sum()
        fund_agent_df_week_total = fund_agent_df_week['amount'].sum()
        fund_agent_df_last_week_total = fund_agent_df_last_week['amount'].sum()
        fund_agent_df_last_2_weeks_total = fund_agent_df_last_2_weeks['amount'].sum()
        fund_agent_df_month_total = fund_agent_df_month['amount'].sum()
        fund_agent_df_last_month_total = fund_agent_df_last_month['amount'].sum()
        fund_agent_df_last_2_months_total = fund_agent_df_last_2_months['amount'].sum()
        fund_agent_df_year_total = fund_agent_df_year['amount'].sum()
        fund_agent_df_last_year_total = fund_agent_df_last_year['amount'].sum()
        fund_agent_df_last_2_years_total = fund_agent_df_last_2_years['amount'].sum()
        fund_agent_df_all_time_total = fund_agent_df_all_time['amount'].sum()

        # Gets all africa lotto
        africa_lotto_df_today = africa_lotto_df[africa_lotto_df['date'] == today]
        africa_lotto_df_yesterday = africa_lotto_df[africa_lotto_df['date'] == yesterday]
        africa_lotto_df_day_before_yesterday = africa_lotto_df[africa_lotto_df['date'] == day_before_yesterday]
        africa_lotto_df_week = africa_lotto_df[
            (africa_lotto_df['date'] >= start_of_week) & 
            (africa_lotto_df['date'] <= upper_limit_week)
        ]
        africa_lotto_df_last_week = africa_lotto_df[
            (africa_lotto_df['date'] >= start_of_last_week) & 
            (africa_lotto_df['date'] <= upper_limit_last_week)
        ]
        africa_lotto_df_last_2_weeks = africa_lotto_df[
            (africa_lotto_df['date'] >= start_of_last_2_weeks) & 
            (africa_lotto_df['date'] <= upper_limit_last_2_weeks)
        ]
        africa_lotto_df_month = africa_lotto_df[
            (africa_lotto_df['date'] >= start_of_month) & 
            (africa_lotto_df['date'] <= upper_limit_month)
        ]
        africa_lotto_df_last_month = africa_lotto_df[
            (africa_lotto_df['date'] >= start_of_last_month) & 
            (africa_lotto_df['date'] <= upper_limit_last_month)
        ]
        africa_lotto_df_last_2_months = africa_lotto_df[
            (africa_lotto_df['date'] >= start_of_last_2_months) & 
            (africa_lotto_df['date'] <= upper_limit_last_2_months)
        ]
        africa_lotto_df_year = africa_lotto_df[
            (africa_lotto_df['date'] >= start_of_year) & 
            (africa_lotto_df['date'] <= upper_limit_year)
        ]
        africa_lotto_df_last_year = africa_lotto_df[
            (africa_lotto_df['date'] >= start_of_last_year) & 
            (africa_lotto_df['date'] <= upper_limit_last_year)
        ]
        africa_lotto_df_last_2_years = africa_lotto_df[
            (africa_lotto_df['date'] >= start_of_last_2_years) & 
            (africa_lotto_df['date'] <= upper_limit_last_2_years)
        ]
        africa_lotto_df_all_time = africa_lotto_df[africa_lotto_df['date'] <= today]

        # Aggregate data for terminal retrieval
        rtp_africa_lotto_df_today_total = africa_lotto_df_today['rtp'].sum()
        rtp_africa_lotto_df_yesterday_total = africa_lotto_df_yesterday['rtp'].sum()
        rtp_africa_lotto_df_day_before_yesterday_total = africa_lotto_df_day_before_yesterday['rtp'].sum()
        rtp_africa_lotto_df_week_total = africa_lotto_df_week['rtp'].sum()
        rtp_africa_lotto_df_last_week_total = africa_lotto_df_last_week['rtp'].sum()
        rtp_africa_lotto_df_last_2_weeks_total = africa_lotto_df_last_2_weeks['rtp'].sum()
        rtp_africa_lotto_df_month_total = africa_lotto_df_month['rtp'].sum()
        rtp_africa_lotto_df_last_month_total = africa_lotto_df_last_month['rtp'].sum()
        rtp_africa_lotto_df_last_2_months_total = africa_lotto_df_last_2_months['rtp'].sum()
        rtp_africa_lotto_df_year_total = africa_lotto_df_year['rtp'].sum()
        rtp_africa_lotto_df_last_year_total = africa_lotto_df_last_year['rtp'].sum()
        rtp_africa_lotto_df_last_2_years_total = africa_lotto_df_last_2_years['rtp'].sum()
        rtp_africa_lotto_df_all_time_total = africa_lotto_df_all_time['rtp'].sum()

        # Aggregate data for terminal retrieval
        rto_africa_lotto_df_today_total = africa_lotto_df_today['rto'].sum()
        rto_africa_lotto_df_yesterday_total = africa_lotto_df_yesterday['rto'].sum()
        rto_africa_lotto_df_day_before_yesterday_total = africa_lotto_df_day_before_yesterday['rto'].sum()
        rto_africa_lotto_df_week_total = africa_lotto_df_week['rto'].sum()
        rto_africa_lotto_df_last_week_total = africa_lotto_df_last_week['rto'].sum()
        rto_africa_lotto_df_last_2_weeks_total = africa_lotto_df_last_2_weeks['rto'].sum()
        rto_africa_lotto_df_month_total = africa_lotto_df_month['rto'].sum()
        rto_africa_lotto_df_last_month_total = africa_lotto_df_last_month['rto'].sum()
        rto_africa_lotto_df_last_2_months_total = africa_lotto_df_last_2_months['rto'].sum()
        rto_africa_lotto_df_year_total = africa_lotto_df_year['rto'].sum()
        rto_africa_lotto_df_last_year_total = africa_lotto_df_last_year['rto'].sum()
        rto_africa_lotto_df_last_2_years_total = africa_lotto_df_last_2_years['rto'].sum()
        rto_africa_lotto_df_all_time_total = africa_lotto_df_all_time['rto'].sum()

        # Gets all lotto ticket
        lotto_ticket_df_today = lotto_ticket_df[lotto_ticket_df['date'] == today]
        lotto_ticket_df_yesterday = lotto_ticket_df[lotto_ticket_df['date'] == yesterday]
        lotto_ticket_df_day_before_yesterday = lotto_ticket_df[lotto_ticket_df['date'] == day_before_yesterday]
        lotto_ticket_df_week = lotto_ticket_df[
            (lotto_ticket_df['date'] >= start_of_week) & 
            (lotto_ticket_df['date'] <= upper_limit_week)
        ]
        lotto_ticket_df_last_week = lotto_ticket_df[
            (lotto_ticket_df['date'] >= start_of_last_week) & 
            (lotto_ticket_df['date'] <= upper_limit_last_week)
        ]
        lotto_ticket_df_last_2_weeks = lotto_ticket_df[
            (lotto_ticket_df['date'] >= start_of_last_2_weeks) & 
            (lotto_ticket_df['date'] <= upper_limit_last_2_weeks)
        ]
        lotto_ticket_df_month = lotto_ticket_df[
            (lotto_ticket_df['date'] >= start_of_month) & 
            (lotto_ticket_df['date'] <= upper_limit_month)
        ]
        lotto_ticket_df_last_month = lotto_ticket_df[
            (lotto_ticket_df['date'] >= start_of_last_month) & 
            (lotto_ticket_df['date'] <= upper_limit_last_month)
        ]
        lotto_ticket_df_last_2_months = lotto_ticket_df[
            (lotto_ticket_df['date'] >= start_of_last_2_months) & 
            (lotto_ticket_df['date'] <= upper_limit_last_2_months)
        ]
        lotto_ticket_df_year = lotto_ticket_df[
            (lotto_ticket_df['date'] >= start_of_year) & 
            (lotto_ticket_df['date'] <= upper_limit_year)
        ]
        lotto_ticket_df_last_year = lotto_ticket_df[
            (lotto_ticket_df['date'] >= start_of_last_year) & 
            (lotto_ticket_df['date'] <= upper_limit_last_year)
        ]
        lotto_ticket_df_last_2_years = lotto_ticket_df[
            (lotto_ticket_df['date'] >= start_of_last_2_years) & 
            (lotto_ticket_df['date'] <= upper_limit_last_2_years)
        ]
        lotto_ticket_df_all_time = lotto_ticket_df[lotto_ticket_df['date'] <= today]

        # Aggregate data for terminal retrieval
        rtp_lotto_ticket_df_today_total = lotto_ticket_df_today['rtp'].sum()
        rtp_lotto_ticket_df_yesterday_total = lotto_ticket_df_yesterday['rtp'].sum()
        rtp_lotto_ticket_df_day_before_yesterday_total = lotto_ticket_df_day_before_yesterday['rtp'].sum()
        rtp_lotto_ticket_df_week_total = lotto_ticket_df_week['rtp'].sum()
        rtp_lotto_ticket_df_last_week_total = lotto_ticket_df_last_week['rtp'].sum()
        rtp_lotto_ticket_df_last_2_weeks_total = lotto_ticket_df_last_2_weeks['rtp'].sum()
        rtp_lotto_ticket_df_month_total = lotto_ticket_df_month['rtp'].sum()
        rtp_lotto_ticket_df_last_month_total = lotto_ticket_df_last_month['rtp'].sum()
        rtp_lotto_ticket_df_last_2_months_total = lotto_ticket_df_last_2_months['rtp'].sum()
        rtp_lotto_ticket_df_year_total = lotto_ticket_df_year['rtp'].sum()
        rtp_lotto_ticket_df_last_year_total = lotto_ticket_df_last_year['rtp'].sum()
        rtp_lotto_ticket_df_last_2_years_total = lotto_ticket_df_last_2_years['rtp'].sum()
        rtp_lotto_ticket_df_all_time_total = lotto_ticket_df_all_time['rtp'].sum()

        # Aggregate data for terminal retrieval
        rto_lotto_ticket_df_today_total = lotto_ticket_df_today['rto'].sum()
        rto_lotto_ticket_df_yesterday_total = lotto_ticket_df_yesterday['rto'].sum()
        rto_lotto_ticket_df_day_before_yesterday_total = lotto_ticket_df_day_before_yesterday['rto'].sum()
        rto_lotto_ticket_df_week_total = lotto_ticket_df_week['rto'].sum()
        rto_lotto_ticket_df_last_week_total = lotto_ticket_df_last_week['rto'].sum()
        rto_lotto_ticket_df_last_2_weeks_total = lotto_ticket_df_last_2_weeks['rto'].sum()
        rto_lotto_ticket_df_month_total = lotto_ticket_df_month['rto'].sum()
        rto_lotto_ticket_df_last_month_total = lotto_ticket_df_last_month['rto'].sum()
        rto_lotto_ticket_df_last_2_months_total = lotto_ticket_df_last_2_months['rto'].sum()
        rto_lotto_ticket_df_year_total = lotto_ticket_df_year['rto'].sum()
        rto_lotto_ticket_df_last_year_total = lotto_ticket_df_last_year['rto'].sum()
        rto_lotto_ticket_df_last_2_years_total = lotto_ticket_df_last_2_years['rto'].sum()
        rto_lotto_ticket_df_all_time_total = lotto_ticket_df_all_time['rto'].sum()

        # Gets all lottery model
        lottery_model_df_today = lottery_model_df[lottery_model_df['date'] == today]
        lottery_model_df_yesterday = lottery_model_df[lottery_model_df['date'] == yesterday]
        lottery_model_df_day_before_yesterday = lottery_model_df[lottery_model_df['date'] == day_before_yesterday]
        lottery_model_df_week = lottery_model_df[
            (lottery_model_df['date'] >= start_of_week) & 
            (lottery_model_df['date'] <= upper_limit_week)
        ]
        lottery_model_df_last_week = lottery_model_df[
            (lottery_model_df['date'] >= start_of_last_week) & 
            (lottery_model_df['date'] <= upper_limit_last_week)
        ]
        lottery_model_df_last_2_weeks = lottery_model_df[
            (lottery_model_df['date'] >= start_of_last_2_weeks) & 
            (lottery_model_df['date'] <= upper_limit_last_2_weeks)
        ]
        lottery_model_df_month = lottery_model_df[
            (lottery_model_df['date'] >= start_of_month) & 
            (lottery_model_df['date'] <= upper_limit_month)
        ]
        lottery_model_df_last_month = lottery_model_df[
            (lottery_model_df['date'] >= start_of_last_month) & 
            (lottery_model_df['date'] <= upper_limit_last_month)
        ]
        lottery_model_df_last_2_months = lottery_model_df[
            (lottery_model_df['date'] >= start_of_last_2_months) & 
            (lottery_model_df['date'] <= upper_limit_last_2_months)
        ]
        lottery_model_df_year = lottery_model_df[
            (lottery_model_df['date'] >= start_of_year) & 
            (lottery_model_df['date'] <= upper_limit_year)
        ]
        lottery_model_df_last_year = lottery_model_df[
            (lottery_model_df['date'] >= start_of_last_year) & 
            (lottery_model_df['date'] <= upper_limit_last_year)
        ]
        lottery_model_df_last_2_years = lottery_model_df[
            (lottery_model_df['date'] >= start_of_last_2_years) & 
            (lottery_model_df['date'] <= upper_limit_last_2_years)
        ]
        lottery_model_df_all_time = lottery_model_df[lottery_model_df['date'] <= today]

        # Aggregate data for terminal retrieval
        rtp_lottery_model_df_today_total = lottery_model_df_today['rtp'].sum()
        rtp_lottery_model_df_yesterday_total = lottery_model_df_yesterday['rtp'].sum()
        rtp_lottery_model_df_day_before_yesterday_total = lottery_model_df_day_before_yesterday['rtp'].sum()
        rtp_lottery_model_df_week_total = lottery_model_df_week['rtp'].sum()
        rtp_lottery_model_df_last_week_total = lottery_model_df_last_week['rtp'].sum()
        rtp_lottery_model_df_last_2_weeks_total = lottery_model_df_last_2_weeks['rtp'].sum()
        rtp_lottery_model_df_month_total = lottery_model_df_month['rtp'].sum()
        rtp_lottery_model_df_last_month_total = lottery_model_df_last_month['rtp'].sum()
        rtp_lottery_model_df_last_2_months_total = lottery_model_df_last_2_months['rtp'].sum()
        rtp_lottery_model_df_year_total = lottery_model_df_year['rtp'].sum()
        rtp_lottery_model_df_last_year_total = lottery_model_df_last_year['rtp'].sum()
        rtp_lottery_model_df_last_2_years_total = lottery_model_df_last_2_years['rtp'].sum()
        rtp_lottery_model_df_all_time_total = lottery_model_df_all_time['rtp'].sum()

        # Aggregate data for terminal retrieval
        rto_lottery_model_df_today_total = lottery_model_df_today['rto'].sum()
        rto_lottery_model_df_yesterday_total = lottery_model_df_yesterday['rto'].sum()
        rto_lottery_model_df_day_before_yesterday_total = lottery_model_df_day_before_yesterday['rto'].sum()
        rto_lottery_model_df_week_total = lottery_model_df_week['rto'].sum()
        rto_lottery_model_df_last_week_total = lottery_model_df_last_week['rto'].sum()
        rto_lottery_model_df_last_2_weeks_total = lottery_model_df_last_2_weeks['rto'].sum()
        rto_lottery_model_df_month_total = lottery_model_df_month['rto'].sum()
        rto_lottery_model_df_last_month_total = lottery_model_df_last_month['rto'].sum()
        rto_lottery_model_df_last_2_months_total = lottery_model_df_last_2_months['rto'].sum()
        rto_lottery_model_df_year_total = lottery_model_df_year['rto'].sum()
        rto_lottery_model_df_last_year_total = lottery_model_df_last_year['rto'].sum()
        rto_lottery_model_df_last_2_years_total = lottery_model_df_last_2_years['rto'].sum()
        rto_lottery_model_df_all_time_total = lottery_model_df_all_time['rto'].sum()

        # Aggregate all rtp
        rtp_today_total = rtp_africa_lotto_df_today_total + rtp_lotto_ticket_df_today_total + rtp_lottery_model_df_today_total
        rtp_yesterday_total = rtp_africa_lotto_df_yesterday_total + rtp_lotto_ticket_df_yesterday_total + rtp_lottery_model_df_yesterday_total
        rtp_day_before_yesterday_total = rtp_africa_lotto_df_day_before_yesterday_total + rtp_lotto_ticket_df_day_before_yesterday_total + rtp_lottery_model_df_day_before_yesterday_total
        rtp_week_total = rtp_africa_lotto_df_week_total + rtp_lotto_ticket_df_week_total + rtp_lottery_model_df_week_total
        rtp_last_week_total = rtp_africa_lotto_df_last_week_total + rtp_lotto_ticket_df_last_week_total + rtp_lottery_model_df_last_week_total
        rtp_last_2_weeks_total = rtp_africa_lotto_df_last_2_weeks_total + rtp_lotto_ticket_df_last_2_weeks_total + rtp_lottery_model_df_last_2_weeks_total
        rtp_month_total = rtp_africa_lotto_df_month_total + rtp_lotto_ticket_df_month_total + rtp_lottery_model_df_month_total
        rtp_last_month_total = rtp_africa_lotto_df_last_month_total + rtp_lotto_ticket_df_last_month_total + rtp_lottery_model_df_last_month_total
        rtp_last_2_months_total = rtp_africa_lotto_df_last_2_months_total + rtp_lotto_ticket_df_last_2_months_total + rtp_lottery_model_df_last_2_months_total
        rtp_year_total = rtp_africa_lotto_df_year_total + rtp_lotto_ticket_df_year_total + rtp_lottery_model_df_year_total
        rtp_last_year_total = rtp_africa_lotto_df_last_year_total + rtp_lotto_ticket_df_last_year_total + rtp_lottery_model_df_last_year_total
        rtp_last_2_years_total = rtp_africa_lotto_df_last_2_years_total + rtp_lotto_ticket_df_last_2_years_total + rtp_lottery_model_df_last_2_years_total
        rtp_all_time_total = rtp_africa_lotto_df_all_time_total + rtp_lotto_ticket_df_all_time_total + rtp_lottery_model_df_all_time_total

        # Aggregate all rto
        rto_today_total = rto_africa_lotto_df_today_total + rto_lotto_ticket_df_today_total + rto_lottery_model_df_today_total
        rto_yesterday_total = rto_africa_lotto_df_yesterday_total + rto_lotto_ticket_df_yesterday_total + rto_lottery_model_df_yesterday_total
        rto_day_before_yesterday_total = rto_africa_lotto_df_day_before_yesterday_total + rto_lotto_ticket_df_day_before_yesterday_total + rto_lottery_model_df_day_before_yesterday_total
        rto_week_total = rto_africa_lotto_df_week_total + rto_lotto_ticket_df_week_total + rto_lottery_model_df_week_total
        rto_last_week_total = rto_africa_lotto_df_last_week_total + rto_lotto_ticket_df_last_week_total + rto_lottery_model_df_last_week_total
        rto_last_2_weeks_total = rto_africa_lotto_df_last_2_weeks_total + rto_lotto_ticket_df_last_2_weeks_total + rto_lottery_model_df_last_2_weeks_total
        rto_month_total = rto_africa_lotto_df_month_total + rto_lotto_ticket_df_month_total + rto_lottery_model_df_month_total
        rto_last_month_total = rto_africa_lotto_df_last_month_total + rto_lotto_ticket_df_last_month_total + rto_lottery_model_df_last_month_total
        rto_last_2_months_total = rto_africa_lotto_df_last_2_months_total + rto_lotto_ticket_df_last_2_months_total + rto_lottery_model_df_last_2_months_total
        rto_year_total = rto_africa_lotto_df_year_total + rto_lotto_ticket_df_year_total + rto_lottery_model_df_year_total
        rto_last_year_total = rto_africa_lotto_df_last_year_total + rto_lotto_ticket_df_last_year_total + rto_lottery_model_df_last_year_total
        rto_last_2_years_total = rto_africa_lotto_df_last_2_years_total + rto_lotto_ticket_df_last_2_years_total + rto_lottery_model_df_last_2_years_total
        rto_all_time_total = rto_africa_lotto_df_all_time_total + rto_lotto_ticket_df_all_time_total + rto_lottery_model_df_all_time_total

        # Aggregate for net_margin
        net_margin_today_total = max(0,(sales_game_wallet_trans_df_today_total - winning_wallet_trans_df_today_total))
        net_margin_yesterday_total = max(0,(sales_game_wallet_trans_df_yesterday_total - winning_wallet_trans_df_yesterday_total))
        net_margin_day_before_yesterday_total = max(0,(sales_game_wallet_trans_df_day_before_yesterday_total - winning_wallet_trans_df_day_before_yesterday_total))
        net_margin_week_total = max(0,(sales_game_wallet_trans_df_week_total - winning_wallet_trans_df_week_total))
        net_margin_last_week_total = max(0,(sales_game_wallet_trans_df_last_week_total - winning_wallet_trans_df_last_week_total))
        net_margin_last_2_weeks_total = max(0,(sales_game_wallet_trans_df_last_2_weeks_total - winning_wallet_trans_df_last_2_weeks_total))
        net_margin_month_total = max(0,(sales_game_wallet_trans_df_month_total - winning_wallet_trans_df_month_total))
        net_margin_last_month_total = max(0,(sales_game_wallet_trans_df_last_month_total - winning_wallet_trans_df_last_month_total))
        net_margin_last_2_months_total = max(0,(sales_game_wallet_trans_df_last_2_months_total - winning_wallet_trans_df_last_2_months_total))
        net_margin_year_total = max(0,(sales_game_wallet_trans_df_year_total - winning_wallet_trans_df_year_total))
        net_margin_last_year_total = max(0,(sales_game_wallet_trans_df_last_year_total - winning_wallet_trans_df_last_year_total))
        net_margin_last_2_years_total = max(0,(sales_game_wallet_trans_df_last_2_years_total - winning_wallet_trans_df_last_2_years_total))
        net_margin_all_time_total = max(0,(sales_game_wallet_trans_df_all_time_total - winning_wallet_trans_df_all_time_total))

        # Custom CSS for bordered metrics
        st.markdown(
            """
            <style>
            .metric-container {
                border: 4px solid #7851A9;
                border-radius: 20%; /* Rounded corners */
                padding: 10px;
                text-align: center;
                margin: 15px;
                width: 100%;
                box-shadow: 0 4px 12px rgba(120, 81, 169, 0.4);
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
            .metric-value {
                font-size: 14px;
                font-weight: bold;
                white-space: normal;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
            }
            .metric-delta {
                font-size: 12px;
                font-weight: bold;
                margin-top: 5px;
                margin-bottom: 5px;
            }
            .metric-delta-positive {
                color: #4CAF50; /* Green for positive deltas */
            }
            .metric-delta-negative {
                color: #FF5252; /* Red for negative deltas */
            }
            .metric-title {
                font-size: 17px;
                color: #777;
                font-weight: bold;
                margin-bottom: 10px;
                white-space: normal;
                text-align: center;
            }
            .metric-label {
                font-size: 15px;
                color: #555;
                font-weight: lighter;
                white-space: normal;
                text-align: center;
            }
            </style>
            """, unsafe_allow_html=True
        )

        # Custom metric display function for entire category
        def bordered_category(title, metrics):
            st.markdown(f'<div class="metric-container"><div class="metric-title">{title}</div>', unsafe_allow_html=True)
            for label1, value1, delta1, label2, value2, delta2 in metrics:
                st.markdown(
                    f"""
                    <div style="display: flex; justify-content: space-between; width: 100%;">
                        <div style="text-align: center; flex: 1; margin-left:7%; padding: 10px;">
                            <div class="metric-label">{label1}</div> 
                            <div class="metric-value">{value1}</div>
                            <div class='metric-delta {"metric-delta-positive" if delta1 >= 0 else "metric-delta-negative"}'>{delta1:+.2f}%</div>
                        </div>
                        <div style="text-align: center; flex: 1; padding: 10px;">
                            <div class="metric-label">{label2}</div> 
                            <div class="metric-value">{value2}</div>
                            <div class='metric-delta {"metric-delta-positive" if delta2 >= 0 else "metric-delta-negative"}'>{delta2:+.2f}%</div>
                        </div>
                    </div>
                    <hr style="margin:0; width:90%; display:block; margin-left:10%; margin-right:0%;">
                    """, unsafe_allow_html=True
                )
            st.markdown("</div>", unsafe_allow_html=True)  # Close container

        # Create columns for each metric category
        col1, col2, col3, col4 = st.columns(4)
        col5, col6, col7, col8 = st.columns(4)
        col9, col10, col11, col12 = st.columns(4)

        # # GAMES SOLD
        # with col1:
        #     bordered_category("Game Sold", [
        #         ("Today", f"{game_wallet_trans_df_today_total:,}", 0, "Yesterday", f"{game_wallet_trans_df_yesterday_total:,}", 0),
        #         ("This Week", f"{game_wallet_trans_df_week_total:,}", 0, "This Month", f"{game_wallet_trans_df_month_total:,}", 0),
        #         ("This Year", f"{game_wallet_trans_df_year_total:,}", 0, "All-Time", f"{game_wallet_trans_df_all_time_total:,}", 0),
        #     ])

        # # ACTIVE AGENTS
        # with col2:
        #     bordered_category("Active Agents", [
        #         ("Today", f"{user_today_total:,}", user_growth_today, "Yesterday", f"{user_yesterday_total:,}", user_growth_yesterday),
        #         ("This Week", f"{user_week_total:,}", user_growth_last_week, "This Month", f"{user_month_total:,}", user_growth_last_month),
        #         ("This Year", f"{user_year_total:,}", user_growth_last_year, "All-Time", f"{user_all_time_total:,}", 0),
        #     ])

        # # INACTIVE AGENTS
        # with col3:
        #     bordered_category("Inactive Agents", [
        #         ("Today", f"{user_today_total:,}", user_growth_today, "Yesterday", f"{user_yesterday_total:,}", user_growth_yesterday),
        #         ("This Week", f"{user_week_total:,}", user_growth_last_week, "This Month", f"{user_month_total:,}", user_growth_last_month),
        #         ("This Year", f"{user_year_total:,}", user_growth_last_year, "All-Time", f"{user_all_time_total:,}", 0),
        #     ])

        # # ABSENT AGENTS
        # with col4:
        #     bordered_category("Absent Agents", [
        #         ("Today", f"{user_today_total:,}", user_growth_today, "Yesterday", f"{user_yesterday_total:,}", user_growth_yesterday),
        #         ("This Week", f"{user_week_total:,}", user_growth_last_week, "This Month", f"{user_month_total:,}", user_growth_last_month),
        #         ("This Year", f"{user_year_total:,}", user_growth_last_year, "All-Time", f"{user_all_time_total:,}", 0),
        #     ])

        # SALES
        with col5:
            bordered_category("Sales", [
                ("Today", f"N{sales_game_wallet_trans_df_today_total:,.2f}", 0, "Yesterday", f"N{sales_game_wallet_trans_df_yesterday_total:,.2f}", 0),
                ("This Week", f"N{sales_game_wallet_trans_df_week_total:,.2f}", 0, "This Month", f"N{sales_game_wallet_trans_df_month_total:,.2f}", 0),
                ("This Year", f"N{sales_game_wallet_trans_df_year_total:,.2f}", 0, "All-Time", f"N{sales_game_wallet_trans_df_all_time_total:,.2f}", 0),
            ])

        # WINNINGS
        with col6:
            bordered_category("Winnings", [
                ("Today", f"N{winning_wallet_trans_df_today_total:,.2f}", (winning_wallet_trans_df_today_total/sales_game_wallet_trans_df_today_total)*100, "Yesterday", f"N{winning_wallet_trans_df_yesterday_total:,.2f}", (winning_wallet_trans_df_yesterday_total/sales_game_wallet_trans_df_yesterday_total)*100),
                ("This Week", f"N{winning_wallet_trans_df_week_total:,.2f}", (winning_wallet_trans_df_week_total/sales_game_wallet_trans_df_week_total)*100, "This Month", f"N{winning_wallet_trans_df_month_total:,.2f}", (winning_wallet_trans_df_month_total/sales_game_wallet_trans_df_month_total)*100),
                ("This Year", f"N{winning_wallet_trans_df_year_total:,.2f}", (winning_wallet_trans_df_year_total/sales_game_wallet_trans_df_year_total)*100, "All-Time", f"N{winning_wallet_trans_df_all_time_total:,.2f}", (winning_wallet_trans_df_all_time_total/sales_game_wallet_trans_df_all_time_total)*100),
            ])

        # COMMISSIONS
        with col7:
            bordered_category("Commisions", [
                ("Today", f"{0:,}", 0, "Yesterday", f"{0:,}", 0),
                ("This Week", f"{0:,}", 0, "This Month", f"{0:,}", 0),
                ("This Year", f"{0:,}", 0, "All-Time", f"{0:,}", 0),
            ])

        # NET MARGIN
        with col8:
            bordered_category("Net Margin", [
                ("Today", f"N{net_margin_today_total:,.2f}", 0, "Yesterday", f"N{net_margin_yesterday_total:,.2f}", 0),
                ("This Week", f"N{net_margin_week_total:,.2f}", 0, "This Month", f"N{net_margin_month_total:,.2f}", 0),
                ("This Year", f"N{net_margin_year_total:,.2f}", 0, "All-Time", f"N{net_margin_all_time_total:,.2f}", 0),
            ])

        # PAYOUTS
        with col9:
            bordered_category("Payouts", [
                ("Today", f"N{payout_df_today_total:,.2f}", 0, "Yesterday", f"N{payout_df_yesterday_total:,.2f}", 0),
                ("This Week", f"N{payout_df_week_total:,.2f}", 0, "This Month", f"N{payout_df_month_total:,.2f}", 0),
                ("This Year", f"N{payout_df_year_total:,.2f}", 0, "All-Time", f"N{payout_df_all_time_total:,.2f}", 0),
            ])

        # TICKET COUNT
        with col10:
            bordered_category("Return To Player", [
                ("Today", f"N{rtp_today_total:,.2f}", (rtp_today_total/winning_wallet_trans_df_today_total)*100, "Yesterday", f"N{rtp_yesterday_total:,.2f}", (rtp_yesterday_total/winning_wallet_trans_df_yesterday_total)*100),
                ("This Week", f"N{rtp_week_total:,.2f}", (rtp_week_total/winning_wallet_trans_df_week_total)*100, "This Month", f"N{rtp_month_total:,.2f}", (rtp_month_total/winning_wallet_trans_df_month_total)*100),
                ("This Year", f"N{rtp_year_total:,.2f}", (rtp_year_total/winning_wallet_trans_df_year_total)*100, "All-Time", f"N{rtp_all_time_total:,.2f}", (rtp_all_time_total/winning_wallet_trans_df_all_time_total)*100),
            ])

        # REMITTANCE
        with col11:
            bordered_category("Return To Owner", [
                ("Today", f"N{rto_today_total:,.2f}", (rto_today_total/net_margin_today_total)*100, "Yesterday", f"N{rto_yesterday_total:,.2f}", (rto_yesterday_total/net_margin_yesterday_total)*100),
                ("This Week", f"N{rto_week_total:,.2f}", (rto_week_total/net_margin_week_total)*100, "This Month", f"N{rto_month_total:,.2f}", (rto_month_total/net_margin_month_total)*100),
                ("This Year", f"N{rto_year_total:,.2f}", (rto_year_total/net_margin_year_total)*100, "All-Time", f"N{rto_all_time_total:,.2f}", (rto_all_time_total/net_margin_all_time_total)*100),
            ])

        # WALLET FUNDING
        with col12:
            bordered_category("Wallet Funding", [
                ("Today", f"N{fund_agent_df_today_total:,.2f}", 0, "Yesterday", f"N{fund_agent_df_yesterday_total:,.2f}", 0),
                ("This Week", f"N{fund_agent_df_week_total:,.2f}", 0, "This Month", f"N{fund_agent_df_month_total:,.2f}", 0),
                ("This Year", f"N{fund_agent_df_year_total:,.2f}", 0, "All-Time", f"N{fund_agent_df_all_time_total:,.2f}", 0),
            ])

        st.markdown("---")

    ################################################################################ Agent Metrics ##########################################################################################

        # # Group and aggregate by agent for past 14 days
        # agent_14_day_avg = game_wallet_trans_df[
        #     (game_wallet_trans_df['date'] >= start_of_14_days_ago) & 
        #     (game_wallet_trans_df['date'] <= upper_limit_14_days)
        # ].groupby('lotto_agent_id')['amount'].mean().round(2).rename('avg_14_days')
        
        # # Group and aggregate by agent for 30 days ago
        # agent_30_day_avg = game_wallet_trans_df[
        #     (game_wallet_trans_df['date'] >= start_of_30_days_ago) & 
        #     (game_wallet_trans_df['date'] <= upper_limit_30_days)
        # ].groupby('lotto_agent_id')['amount'].mean().round(2).rename('avg_30_days')

        # # Check for presence in last 2 days
        # agent_last_2_days = game_wallet_trans_df[
        #     (game_wallet_trans_df['date'] >= start_of_last_2_days) & 
        #     (game_wallet_trans_df['date'] <= upper_limit_2_days)
        # ].groupby('lotto_agent_id')['amount'].sum().round(2).rename('last_2_days_total')

        # # Combine all metrics
        # agent_status_df = pd.concat([agent_14_day_avg, agent_30_day_avg, agent_last_2_days], axis=1).fillna(0)

        # # Define logic for classification
        # def classify_agent(row):
        #     if row['avg_14_days'] >= 8000:
        #         return 'ACTIVE_AGENT'
        #     elif row['avg_14_days'] < 8000 and row['avg_30_days'] > 8000:
        #         return 'DECLINING_AGENT'
        #     elif 4000 <= row['avg_14_days'] < 8000:
        #         return 'AT_RISK_AGENT'
        #     elif 1000 <= row['avg_14_days'] < 4000:
        #         return 'INACTIVE_AGENT'
        #     elif row['avg_14_days'] < 1000 or row['last_2_days_total'] == 0:
        #         return 'ABSENT_AGENT'
        #     else:
        #         return 'INVALID_RECORD'

        # # Apply logic
        # agent_status_df['agent_metrics'] = agent_status_df.apply(classify_agent, axis=1)

        # # Merge back to original dataset
        # lotto_agent_metrics = game_wallet_trans_df.merge(agent_status_df['agent_metrics'], on='lotto_agent_id', how='left')

        # st.write(agent_status_df)
        # st.write(lotto_agent_metrics)

    ################################################################################ Charts and Plots ##########################################################################################

        # Cached Chart Generators
        @st.cache_data(show_spinner=False)
        def generate_game_play_chart(game_wallet_trans_df):
            game_type_count = game_wallet_trans_df['game_type'].value_counts()
            fig = px.pie(
                names=game_type_count.index,
                values=game_type_count.values,
                title="Game Play Distribution",
                color_discrete_sequence=px.colors.sequential.Viridis
            )
            fig.update_traces(hoverinfo="label+percent+value")
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",
                plot_bgcolor="rgba(0, 0, 0, 0)",
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}
            )
            return fig

        @st.cache_data(show_spinner=False)
        def generate_winning_chart(winning_wallet_trans_df):
            game_type_count = winning_wallet_trans_df['game_type'].value_counts()
            fig = px.pie(
                names=game_type_count.index,
                values=game_type_count.values,
                title="Winning Distribution",
                color_discrete_sequence=px.colors.sequential.Cividis,
                hole=0.5
            )
            fig.update_traces(hoverinfo="label+percent+value")
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",
                plot_bgcolor="rgba(0, 0, 0, 0)",
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}
            )
            return fig

        # Loan Status and Performance Distribution
        col1, col2 = st.columns(2)

        with col1:
            st.plotly_chart(generate_game_play_chart(game_wallet_trans_df), use_container_width=True)

        with col2:
            st.plotly_chart(generate_winning_chart(winning_wallet_trans_df), use_container_width=True)

    ########################################################################### Tab 2 #####################################################################################

    with tab2: 

    ######################################################################## Time-Series Analysis ####################################################################################

        # Make year filter
        all_years = sorted(wallet_trans_df['date'].dt.year.unique().tolist())
        current_year = datetime.now().year

        # Create an option for all time view
        year_options = ['All'] + all_years
        default_index = year_options.index(current_year) if current_year in all_years else 0

        # Make current year default
        col1, col2, col3 = st.columns([1,3,1])
        with col2:
            selected_year = st.selectbox("Select Year:", year_options, index=default_index)

        # Filter the dataframe by selected year
        if selected_year != 'All':
            filtered_wallet_trans_df = wallet_trans_df[wallet_trans_df['date'].dt.year == selected_year]

        # Function to filter other datasets by year
        def filter_by_year(df, year):
            if df is not None and not df.empty:
                df = df.copy()
                df['date'] = pd.to_datetime(df['date'])
                if year != 'All':
                    df = df[df['date'].dt.year == year]
            return df

        # Filter by selected year
        filtered_game_wallet_trans_df = filter_by_year(game_wallet_trans_df, selected_year)
        filtered_winning_wallet_trans_df = filter_by_year(winning_wallet_trans_df, selected_year)
        filtered_fund_agent_df = filter_by_year(fund_agent_df, selected_year)
        filtered_payout_df = filter_by_year(payout_df, selected_year)
        filtered_africa_lotto_df = filter_by_year(africa_lotto_df, selected_year)
        filtered_lotto_ticket_df = filter_by_year(lotto_ticket_df, selected_year)
        filtered_lottery_model_df = filter_by_year(lottery_model_df, selected_year)

        # Select period
        col1, col2, col3 = st.columns([1,2,1])
        with col2:
            period_filter = st.selectbox("Select Period:", ["Week", "Month", "Quarter", "Year"], index=1)

        # Grouping format helper
        def format_period_column(df, period_col="date"):
            df = df.copy()
            df[period_col] = pd.to_datetime(df[period_col])  # Ensure datetime
            df["period"] = (
                df[period_col].dt.to_period("W").apply(lambda r: f"{r.start_time.date()} to {r.end_time.date()}")
                if period_filter == "Week"
                else df[period_col].dt.to_period("M").astype(str)
                if period_filter == "Month"
                else df[period_col].dt.to_period("Q").astype(str)
                if period_filter == "Quarter"
                else df[period_col].dt.to_period("Y").astype(str)
            )
            return df

        # Ensure all DataFrames have 'date', 'rtp', and 'rto' columns
        # Fill missing 'rtp' or 'rto' with 0 if they don't exist in a specific DataFrame
        for df in [filtered_africa_lotto_df, filtered_lotto_ticket_df, filtered_lottery_model_df]:
            if 'rtp' not in df.columns:
                df['rtp'] = 0
            if 'rto' not in df.columns:
                df['rto'] = 0

        # Combine all DataFrames into one
        rtp_rto_combined = pd.concat([filtered_africa_lotto_df, filtered_lotto_ticket_df, filtered_lottery_model_df], ignore_index=True)

        # Group by date and sum rtp and rto
        grouped_df = rtp_rto_combined.groupby('date', as_index=False)[['rtp', 'rto']].sum()

        # Separate into rtp_df and rto_df if needed
        rtp_df = grouped_df[['date', 'rtp']]
        rto_df = grouped_df[['date', 'rto']]

        # Apply period formatting to all datasets
        sales_df = format_period_column(filtered_game_wallet_trans_df)
        winnings_df = format_period_column(filtered_winning_wallet_trans_df)
        funds_df = format_period_column(filtered_fund_agent_df)
        payouts_df = format_period_column(filtered_payout_df)
        rtp_df = format_period_column(rtp_df)
        rto_df = format_period_column(rto_df)

        # Aggregate by period
        sales_trend = sales_df.groupby(["period", "game_type"])["amount"].sum().reset_index(name="Sales").fillna(0)
        winnings_trend = winnings_df.groupby(["period", "game_type"])["amount"].sum().reset_index(name="Winnings").fillna(0)
        sales_summary = sales_df.groupby("period")["amount"].sum().reset_index(name="Sales")
        winnings_summary = winnings_df.groupby("period")["amount"].sum().reset_index(name="Winnings")
        funds_summary = funds_df.groupby("period")["amount"].sum().reset_index(name="Funds")
        payouts_summary = payouts_df.groupby("period")["amount"].sum().reset_index(name="Payouts")
        rtp_summary = rtp_df.groupby("period")["rtp"].sum().reset_index(name="RTP")
        rto_summary = rto_df.groupby("period")["rto"].sum().reset_index(name="RTO")

        # Merge all summaries
        all_data = sales_summary.merge(winnings_summary, on="period", how="outer") \
                                .merge(funds_summary, on="period", how="outer") \
                                .merge(payouts_summary, on="period", how="outer") \
                                .merge(rtp_summary, on="period", how="outer") \
                                .merge(rto_summary, on="period", how="outer") \
                                .fillna(0)

        # Sort by period
        all_data = all_data.sort_values(by="period")

        #### # Create a new DataFrame commission based on all_data periods, with zero default values
        commission_df = pd.DataFrame({
            "period": all_data["period"],
            "Commission": 0.0
        })      ## To be removed when commission is gotten

        #### # Merge commission into all_data
        all_data = all_data.merge(commission_df, on="period", how="left")      ## To be removed when commission is gotten

        # Calculate Net Margin
        all_data["Net Margin"] = all_data["Sales"] - all_data["Winnings"] - all_data["Commission"]

        # Cumulative columns
        metrics = ["Sales", "Winnings", "Funds", "Payouts", "RTP", "RTO", "Commission", "Net Margin"]
        for metric in metrics:
            cum_col = f"Cumulative {metric}"
            all_data[cum_col] = all_data[metric].cumsum()

    ######################################################################### Plots ####################################################################################

        plot1, plot2 = st.columns(2)

        with plot1:
            with st.container():
                # Sales
                fig1 = px.line(
                    sales_trend,
                    x="period",
                    y="Sales",
                    color="game_type",
                    title="Sales Trends",
                    markers=True,
                    color_discrete_sequence=px.colors.qualitative.Plotly
                )

                # Update layout to reduce background opacity
                fig1.update_layout(
                    paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                    plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                    modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                )

                st.plotly_chart(fig1)

        with plot2:
            with st.container():
                # Winnings
                fig2 = px.line(
                    winnings_trend,
                    x="period",
                    y="Winnings",
                    color="game_type",
                    title="Winnings Trend",
                    markers=True,
                    color_discrete_sequence=px.colors.qualitative.Plotly
                )

                # Update layout to reduce background opacity
                fig2.update_layout(
                    paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                    plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                    modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                )

                st.plotly_chart(fig2)

        plot3, plot4 = st.columns(2)

        with plot3:
            with st.container():
                # Sales vs winnings
                fig3 = px.line(
                    all_data,
                    x="period",
                    y=["Sales", "Winnings", "Payouts", "Net Margin"],
                    color_discrete_sequence=["#4B0082", "#800080", "#9370DB", "#DA70D6"],  # Purple shades
                    title="Sales/Winnings Trend (Cohort)",
                    markers=True
                )
                    
                # Update layout to reduce background opacity
                fig3.update_layout(
                    paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                    plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                    modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                )

                st.plotly_chart(fig3)
                
        with plot4:
            with st.container():
                # Sales vs winnings
                fig4 = px.line(
                    all_data,
                    x="period",
                    y=["Cumulative Sales", "Cumulative Winnings", "Cumulative Payouts", "Cumulative Net Margin"],
                    color_discrete_sequence=["#4B0082", "#800080", "#9370DB", "#DA70D6"],  # Purple shades
                    title="Sales/Winnings Trend (Cumulative)",
                    markers=True
                )
                    
                # Update layout to reduce background opacity
                fig4.update_layout(
                    paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                    plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                    modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                )

                st.plotly_chart(fig4)

        plot5, plot6 = st.columns(2)

        with plot5:
            with st.container():
                # RTP vs RTO
                fig5 = px.line(
                    all_data,
                    x="period",
                    y=["RTP", "RTO"],
                    color_discrete_sequence=["#4B0082", "#DA70D6"],  # Purple shades
                    title="Return To Player/Owner (Cohort)",
                    markers=True
                )

                # Update layout to reduce background opacity
                fig5.update_layout(
                    paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                    plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                    modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                )

                st.plotly_chart(fig5)

        with plot6:
            with st.container():
                # RTO
                fig6 = px.line(
                    all_data,
                    x="period",
                    y=["Cumulative RTP", "Cumulative RTO"],
                    color_discrete_sequence=["#4B0082", "#DA70D6"],  # Purple shades
                    title="Return To Player/Owner (Cumulative)",
                    markers=True
                )
  
                # Update layout to reduce background opacity
                fig6.update_layout(
                    paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                    plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                    modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                )

                st.plotly_chart(fig6)



        plot7, plot8 = st.columns(2)

        with plot7:
            with st.container():
                # Payouts vs winnings
                fig7 = px.line(
                    all_data,
                    x="period",
                    y=["Payouts", "Winnings"],
                    color_discrete_sequence=["#4B0082", "#DA70D6"],  # Purple shades
                    title="Payouts/Winnings Trend",
                    markers=True
                )

                # Update layout to reduce background opacity
                fig7.update_layout(
                    paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                    plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                    modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
                )

                st.plotly_chart(fig7)

    ################################################################################### Tab 3 #################################################################################

    with tab3:

    ######################################################################## Product Analysis ####################################################################################

        st.markdown(
            """
            <div style='text-align:center; font-size:15px;'>
                <h1>Agent Ranking By Sales (Amount)</h1>
            </div>
            """,
            unsafe_allow_html=True
        )

        col1, col2, col3 = st.columns(3)
        with col1:
            # Top 10 Agents (This Week)
            agent_ranking = (
                game_wallet_trans_df_week.groupby(['agent_name'])['amount']
                .sum()
                .reset_index()
                .sort_values(by='amount', ascending=False)
                .head(10)
            )

            fig = px.bar(agent_ranking, 
                        x='amount', 
                        y='agent_name', 
                        orientation='h',
                        title="Agent Ranking (This Week)", 
                        color='amount', 
                        color_continuous_scale='sunset')
            
            # Update layout to reduce background opacity
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
            ) 

            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Top 10 Agents (This Month)
            agent_ranking = (
                game_wallet_trans_df_month.groupby(['agent_name'])['amount']
                .sum()
                .reset_index()
                .sort_values(by='amount', ascending=False)
                .head(10)
            )

            fig = px.bar(agent_ranking, 
                        x='amount', 
                        y='agent_name', 
                        orientation='h',
                        title="Agent Ranking (This Month)", 
                        color='amount', 
                        color_continuous_scale='sunset')
            
            # Update layout to reduce background opacity
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
            ) 

            st.plotly_chart(fig, use_container_width=True)

        with col3:
            # Top 10 Agents (All Time)
            agent_ranking = (
                game_wallet_trans_df.groupby(['agent_name'])['amount']
                .sum()
                .reset_index()
                .sort_values(by='amount', ascending=False)
                .head(10)
            )

            fig = px.bar(agent_ranking, 
                        x='amount', 
                        y='agent_name', 
                        orientation='h',
                        title="Agent Ranking (All Time)", 
                        color='amount', 
                        color_continuous_scale='sunset')
            
            # Update layout to reduce background opacity
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
            ) 

            st.plotly_chart(fig, use_container_width=True)

        st.markdown("---")

        st.markdown(
            """
            <div style='text-align:center; font-size:15px;'>
                <h1>Game Type Ranking By Sales (Amount)</h1>
            </div>
            """,
            unsafe_allow_html=True
        )

        col1, col2, col3 = st.columns(3)
        with col1:
            # Top 10 Agents (This Week)
            agent_ranking = (
                game_wallet_trans_df_week.groupby(['game_type'])['amount']
                .sum()
                .reset_index()
                .sort_values(by='amount', ascending=False)
                .head(10)
            )

            fig = px.bar(agent_ranking, 
                        x='amount', 
                        y='game_type', 
                        orientation='h',
                        title="Game Type Ranking (This Week)", 
                        color='amount', 
                        color_continuous_scale='peach')
            
            # Update layout to reduce background opacity
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
            ) 

            st.plotly_chart(fig, use_container_width=True)

        with col2:
            # Top 10 Agents (This Month)
            agent_ranking = (
                game_wallet_trans_df_month.groupby(['game_type'])['amount']
                .sum()
                .reset_index()
                .sort_values(by='amount', ascending=False)
                .head(10)
            )

            fig = px.bar(agent_ranking, 
                        x='amount', 
                        y='game_type', 
                        orientation='h',
                        title="Game Type Ranking (This Month)", 
                        color='amount', 
                        color_continuous_scale='peach')
            
            # Update layout to reduce background opacity
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
            ) 

            st.plotly_chart(fig, use_container_width=True)

        with col3:
            # Top 10 Agents (All Time)
            agent_ranking = (
                game_wallet_trans_df.groupby(['game_type'])['amount']
                .sum()
                .reset_index()
                .sort_values(by='amount', ascending=False)
                .head(10)
            )

            fig = px.bar(agent_ranking, 
                        x='amount', 
                        y='game_type', 
                        orientation='h',
                        title="Game Type Ranking (All Time)", 
                        color='amount', 
                        color_continuous_scale='peach')
            
            # Update layout to reduce background opacity
            fig.update_layout(
                paper_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent background
                plot_bgcolor="rgba(0, 0, 0, 0)",  # Fully transparent plot background (adjust opacity)
                modebar={"bgcolor": "rgba(0, 0, 0, 0)"}  # Make modebar background transparent
            ) 

            st.plotly_chart(fig, use_container_width=True)

        st.markdown("---")

        # Allows users to download files
        with file1:
            if wallet_trans_df.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                # Format: "15_Jan_2025"
                today_str = datetime.now().strftime("%d_%b_%Y")
                file_name = f"Wallet_transactions_till_{today_str}.csv"

                csv = wallet_trans_df.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="⬇️ Download",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

        with file2:
            if fund_agent_df.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                # Format: "15_Jan_2025"
                today_str = datetime.now().strftime("%d_%b_%Y")
                file_name = f"Agent_fundings_till_{today_str}.csv"

                csv = fund_agent_df.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="⬇️ Download",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

        with file3:
            if payout_df.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                # Format: "15_Jan_2025"
                today_str = datetime.now().strftime("%d_%b_%Y")
                file_name = f"Payouts_till_{today_str}.csv"

                csv = payout_df.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="⬇️ Download",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

        with file4:
            if africa_lotto_df.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                # Format: "15_Jan_2025"
                today_str = datetime.now().strftime("%d_%b_%Y")
                file_name = f"Africa_lotto_till_{today_str}.csv"

                csv = africa_lotto_df.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="⬇️ Download",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

        with file5:
            if lotto_ticket_df.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                # Format: "15_Jan_2025"
                today_str = datetime.now().strftime("%d_%b_%Y")
                file_name = f"Lotto_ticket_till_{today_str}.csv"

                csv = lotto_ticket_df.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="⬇️ Download",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

        with file6:
            if lottery_model_df.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                # Format: "15_Jan_2025"
                today_str = datetime.now().strftime("%d_%b_%Y")
                file_name = f"Lottery_model_till_{today_str}.csv"

                csv = lottery_model_df.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="⬇️ Download",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

        with file7:
            if all_data.empty:
                st.warning("No data found for the selected filters. Please adjust your selections.")
            else:
                # Format: "15_Jan_2025"
                today_str = datetime.now().strftime("%d_%b_%Y")
                file_name = f"Aggregated_monthly_data_till_{today_str}.csv"

                csv = all_data.to_csv(index=False).encode("utf-8")
                st.download_button(
                    label="⬇️ Download",
                    data=csv,
                    file_name=file_name,
                    mime="text/csv"
                )

    st.markdown(
        """
        <style>
        .sidebar-footer {
            position: fixed;
            left: 0;
            bottom: 0;
            width: 100%;
            opacity: 90%;
            background: linear-gradient(to bottom, rgba(120, 81, 169, 0.8), rgba(75, 0, 130, 0.8));
            box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.3); /* Added shadow */
            color: white;
            text-align: left; /* Align text to the left for moving effect */
            padding: 10px 0;
            font-weight: bold;
            font-size: 1.2em;
            letter-spacing: 6px;
            z-index: 1000;
            overflow: hidden; /* Hide overflowing text */
            white-space: nowrap; /* Prevent text from wrapping */
        }

        .moving-text {
            display: inline-block;
            padding-left: 100%; /* Start offscreen to the right */
            animation: marquee 45s linear infinite; /* Adjust speed as needed */
        }

        @keyframes marquee {
            0% {
                transform: translateX(0%);
            }
            100% {
                transform: translateX(-100%); /* Move offscreen to the left */
            }
        }
        </style>
        <div class="sidebar-footer">
            <span class="moving-text"> This dashboard provides a real-time overview of total lotto sales and winnings across all agents
            || Track daily, weekly, and monthly performance, including top-performing agents and payout trends
            || Visual insights highlight sales volumes, customer win rates, and agent-level activity breakdowns. </span>
        </div>
        """,
        unsafe_allow_html=True,
    )