import pandas as pd
import psycopg2
import warnings
import streamlit as st

warnings.filterwarnings('ignore')

@st.cache_data(show_spinner=False)
def sql_connection(script):
    try:
        conn = psycopg2.connect(
            host=st.secrets.database.host,
            database=st.secrets.database.database,
            user=st.secrets.database.user,
            password=st.secrets.database.password,
            port=st.secrets.database.port
        )
        query = pd.read_sql_query(script, conn)
        conn.close()  # Close connection after use
        return query
    except psycopg2.Error as e:
        st.error(f"Database connection failed: {e}")
        st.stop()  # Stop execution immediately
    except Exception as e:
        st.error(f"An unexpected error occurred: {e}")
        st.stop()  # Stop execution immediately

@st.cache_data(show_spinner=False)
def agency_db_conn(script):
    try:
        conn = psycopg2.connect(
            host=st.secrets.agencydb.host,
            database=st.secrets.agencydb.database,
            user=st.secrets.agencydb.user,
            password=st.secrets.agencydb.password,
            port=st.secrets.agencydb.port
        )
        query = pd.read_sql_query(script, conn)
        conn.close()  # Close connection after use
        return query
    except psycopg2.Error as e:
        st.error(f"Agency DB connection failed: {e}")
        st.stop()  # Stop execution immediately
    except Exception as e:
        st.error(f"An unexpected error occurred: {e}")
        st.stop()  # Stop execution immediately

@st.cache_data(show_spinner=False)
def lotto_db_conn(script):
    try:
        conn = psycopg2.connect(
            host=st.secrets.lottodb.host,
            database=st.secrets.lottodb.database,
            user=st.secrets.lottodb.user,
            password=st.secrets.lottodb.password,
            port=st.secrets.lottodb.port
        )
        query = pd.read_sql_query(script, conn)
        conn.close()  # Close connection after use
        return query
    except psycopg2.Error as e:
        st.error(f"Lotto DB connection failed: {e}")
        st.stop()  # Stop execution immediately
    except Exception as e:
        st.error(f"An unexpected error occurred: {e}")
        st.stop()  # Stop execution immediately

# Query dataset
savings_transaction_query = '''
SELECT 
	trans.date_created,
	SAVINGS.user_id, 
    SAVINGS.ajo_user_id,
    SAVINGS.quotation_id,
    trans.amount
from ajo_ajosaving AS SAVINGS
left join payment_transaction as trans
on 
    SAVINGS.quotation_id = trans.quotation_id
    AND trans.wallet_type IN ('AJO_USER')
    AND trans.status LIKE '%SUCCESS%'
    AND trans.transaction_type LIKE '%CREDIT%'
    AND trans.transaction_form_type LIKE '%WALLET_DEPOSIT%'
    AND trans.amount > 0
    AND trans.plan_type = 'AJO'
where SAVINGS.is_loan_repayment = 'false'
and SAVINGS.loan = 'false'
'''
sav_transaction_df_ = sql_connection(savings_transaction_query)

# Query database
loan_saving = '''
SELECT 
    savings.ajo_user_id, 
    savings.user_id, 
    savings.created_at as ajoSavings_created_at,
    wallet.date_created as wallet_date_created,
    TO_CHAR(savings.created_at::timestamp with time zone, 'Month') AS ajosavings_month_created,
    EXTRACT(YEAR FROM savings.created_at::timestamp with time zone) AS ajosavings_year_created,
    TO_CHAR(wallet.date_created::timestamp with time zone, 'Month') AS wallet_month_created,
    EXTRACT(YEAR FROM wallet.date_created::timestamp with time zone) AS wallet_year_created,						
    savings.amount_saved,
    savings.plan_balance_after,
    savings.is_active,
    wallet.available_balance, 
    (savings.amount_saved + wallet.available_balance) as ajo_savings_plus_escrow
FROM 
    ajo_ajosaving as savings 
LEFT JOIN 
    payment_walletsystem as wallet
ON 
    wallet.onboarded_user_id = savings.ajo_user_id
WHERE 
    wallet.wallet_type = 'AJO_LOAN_ESCROW'
AND 
    savings.is_loan_repayment is false
AND 
    savings.loan = true
    '''
loan_saving_df_ = sql_connection(loan_saving)

# Query database
escrow_query = '''
WITH escrow_ledger AS (
    SELECT 
        SUM(ledger_amount) as ledger_amount,
        borrower_id
    FROM loans_ajoloanrepayment
    GROUP BY borrower_id
)
SELECT 
    date_created,
    TO_CHAR(date_created::timestamp with time zone, 'Month') AS month_created,
    EXTRACT(YEAR FROM date_created::timestamp with time zone) AS year_created,
    user_id, 
    onboarded_user_id, (available_balance + ledger_amount) as available_balance
FROM 
    payment_walletsystem 
LEFT JOIN escrow_ledger
ON 	onboarded_user_id = borrower_id
WHERE 
    wallet_type = 'AJO_LOAN_ESCROW'
    '''
escrow_df_ = sql_connection(escrow_query)

# Query database
staff_savings_trans = '''
SELECT
    agent.customer_user_id, agent.id as user_id,agent.email,
    loans.disbursed,
    DATE_PART('day', CURRENT_DATE - agent.date_joined) AS agent_age_days,
    COALESCE(SUM(trans.amount), 0) AS savings,
    COUNT(trans.id) AS trans_count
FROM
    accounts_customuser AS agent
LEFT JOIN
    payment_transaction AS trans
ON
    agent.id = trans.user_id
    AND trans.date_created >= date_trunc('week', date_trunc('month', CURRENT_DATE) - INTERVAL '1 day')
    AND trans.transaction_type = 'CREDIT'
    AND trans.wallet_type = 'AJO_USER'
    AND trans.transaction_form_type = 'WALLET_DEPOSIT'
LEFT JOIN (
    SELECT
        agent_id,
        sum(amount) as disbursed
    FROM
        loans_ajoloan
    WHERE
        date_disbursed >= date_trunc('week', date_trunc('month', CURRENT_DATE) - INTERVAL '1 day')
        AND status = 'OPEN'
    GROUP BY agent_id
) AS loans
ON
    loans.agent_id = agent.id
GROUP BY
    agent.customer_user_id,agent.id,agent.email,agent.date_joined,loans.disbursed
ORDER BY
    savings DESC
'''
staff_savings_trans_ = sql_connection(staff_savings_trans)

# Query database
monthly_repayments = '''
SELECT
    agent_id, 
    SUM(repayment_amount) AS total_repayment_amount,
    TO_CHAR(paid_date, 'Month') AS month
FROM 
    loans_ajoloanrepayment
GROUP BY 
    agent_id,
    TO_CHAR(paid_date, 'Month')
'''
monthly_repayments_ = sql_connection(monthly_repayments)

# Query database
disbursement_4_last_month= '''
SELECT 
    SUM(amount) AS last_month,
    agent_id
FROM 
    loans_ajoloan
WHERE 
    status = 'OPEN' 
    AND created_at >= DATE_TRUNC('month', CURRENT_DATE) - INTERVAL '1 month'
    AND created_at < DATE_TRUNC('month', CURRENT_DATE)
GROUP BY 
    agent_id
'''
disbursement_4_last_month_ = sql_connection(disbursement_4_last_month)

# Query database
earnings = '''
WITH individualLoans AS (
SELECT 
    agent_id AS user_id,
    ((interest_rate / 100) * amount) AS interest,
    amount AS disbursed
FROM 
    loans_ajoloan
WHERE 
    date_disbursed >= date_trunc('week', date_trunc('month', CURRENT_DATE) - INTERVAL '1 day')
    AND status = 'OPEN'
),
aggregatedLoans AS (
    SELECT
        user_id,
        SUM(disbursed) AS total_disbursed,
        SUM(interest) AS total_interest
    FROM
        individualLoans
    GROUP BY
        user_id
)
SELECT
    user_id,
    -- total_disbursed AS loans,
    total_interest AS interest,
    CASE 
        WHEN total_disbursed BETWEEN 1200000 AND 2100000 THEN 0.05 * total_interest
        ELSE 0
    END AS "Range 1",
    CASE 
        WHEN total_disbursed BETWEEN 2100001 AND 3000000 THEN 0.09 * total_interest
        ELSE 0
    END AS "Range 2",
    CASE 
        WHEN total_disbursed > 3000000 THEN 0.08 * total_interest
        ELSE 0
    END AS "Range 3",
    -- New column to sum up Range 1, Range 2, and Range 3
    CASE 
        WHEN total_disbursed BETWEEN 1200000 AND 2100000 THEN 0.05 * total_interest
        ELSE 0
    END +
    CASE 
        WHEN total_disbursed BETWEEN 2100001 AND 3000000 THEN 0.09 * total_interest
        ELSE 0
    END +
    CASE 
        WHEN total_disbursed > 3000000 THEN 0.08 * total_interest
        ELSE 0
    END AS earnings
FROM
    aggregatedLoans;
'''
earnings_ = sql_connection(earnings)

# Query database
loginactivity = '''
select 
    count(email) as login_count,
    email
from 
    singlelogin_blacklistedjwt
WHERE 
    date_created >= date_trunc('week', date_trunc('month', CURRENT_DATE) - INTERVAL '1 day')
    AND login_type = 'Logged in'
group by 
    email
'''
loginactivity_ = agency_db_conn(loginactivity)

# Query database
supervisor = '''
SELECT 
    team.user_id AS team_user_id,
    supervisor_dets.supervisor_id,
    branchlocation.region_id,
    region.regional_head_id,
    region.name AS verticals,
	CONCAT(regional_head.first_name, ' ', regional_head.last_name) AS vertical_lead_name,
    regional_head.email AS vertical_lead_email,
    regional_head.phone_number AS vertical_lead_contact,
    supervisor_dets.sup_name,
    supervisor_dets.sup_email,
    supervisor_dets.sup_contact,
    supervisor_dets.type_of_user,
    supervisor_dets.sup_referral_code,
    CONCAT(details.first_name, ' ', details.last_name) AS agent_name,
    branchlocation.state,
    branchlocation.location,
    branchlocation.sub_location,
    branchlocation.sub_location_num
FROM main_userteam_users AS team 
LEFT JOIN main_branchteam AS branch
    ON branch.id = team.userteam_id
LEFT JOIN main_newlocationlist AS branchlocation
    ON branch.branch_id = branchlocation.id
LEFT JOIN main_region AS region
    ON branchlocation.region_id = region.id
LEFT JOIN main_user AS details
    ON details.id = team.user_id
LEFT JOIN main_user AS regional_head
    ON regional_head.id = region.regional_head_id
LEFT JOIN (
    SELECT 
        branch.supervisor_id, 
        CONCAT(details.first_name, ' ', details.last_name) AS sup_name,
        details.email AS sup_email,
        details.phone_number AS sup_contact,
        details.referral_code AS sup_referral_code,
        details.type_of_user
    FROM main_branchteam AS branch
    LEFT JOIN main_user AS details
        ON details.id = branch.supervisor_id
) AS supervisor_dets
    ON supervisor_dets.supervisor_id = branch.supervisor_id
'''
sup_df_ = agency_db_conn(supervisor)

# Query database
merchants = '''
SELECT
    officer.user_id,
    officer.id AS merchant_agent_id,
	users.user_id AS merchant_user_id,
    CONCAT(a.first_name, ' ', a.last_name) AS merchant_agents,
    a.email AS merchant_agent_email,
	merchant_users,
	merchant_users_email	
FROM main_merchantacquisitionofficer AS officer
LEFT JOIN main_user AS a 
    ON a.id = officer.user_id
LEFT JOIN (
    SELECT
        mu.merchantacquisitionofficer_id AS merchant_agent_id,
        mu.user_id,
        CONCAT(u.first_name, ' ', u.last_name) AS merchant_users,
        u.email AS merchant_users_email
    FROM public.main_merchantacquisitionofficer_merchants AS mu
    LEFT JOIN main_user AS u 
        ON u.id = mu.user_id	
) AS users
    ON users.merchant_agent_id = officer.id
WHERE a.type_of_user = 'MERCHANT_AGENT'
'''
merchants_df_ = agency_db_conn(merchants)

# Query database
agent_wallet_transaction = '''
SELECT
	count(wallet.id) AS ticket_count,
	wallet.agent_wallet_id,
	agent.lotto_agent_id,
    agent.user_id,
    agent.terminal_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    agent.agent_name,
    agent.agent_num,
    agent.agent_mail,
    wallet.date_created::date AS date_created,
    agent.agent_type,
    SUM(wallet.amount) AS amount,
    agent.performance_status,
    wallet.transaction_from,
    wallet.game_type,
	wallet.status,
	wallet.wave,
	agent.terminal_retrieved
FROM
    pos_app_agentwallettransaction AS wallet
LEFT JOIN (
    SELECT
        id AS lotto_agent_id,
        supervisor_id,
        user_id,
        CONCAT(first_name, ' ', last_name) AS agent_name,
        phone AS agent_num,
        email AS agent_mail,
        agent_type,
        performance_status,
        terminal_id,
        terminal_retrieved
    FROM 
        public.pos_app_agent
) AS agent
ON 
    wallet.agent_phone_number = agent.agent_num
LEFT JOIN (
    SELECT
        id,
        vertical_lead_id,
        full_name AS supervisor_name,
        phone AS supervisor_phone,
        email AS supervisor_email
    FROM 
        public.pos_app_supervisor
) AS sup
ON
    sup.id = agent.supervisor_id
LEFT JOIN (
	SELECT
		id,
		CONCAT(first_name, ' ', last_name) AS vertical_full_name,
		phone AS vertical_contact,
		email AS vertical_email,
		address AS vertical_address
	FROM
		public.pos_app_lottoverticallead
) AS vert
ON
	sup.vertical_lead_id = vert.id
WHERE
    wallet.transaction_from IN ('GAME_PLAY', 'WINNINGS')
    AND agent.agent_type = 'LOTTO_AGENT'
GROUP BY
	wallet.agent_wallet_id,
	agent.lotto_agent_id,
    agent.user_id,
    agent.terminal_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    agent.agent_name,
    agent.agent_num,
    agent.agent_mail,
    wallet.date_created::date,
    agent.agent_type,
    agent.performance_status,
    wallet.transaction_from,
    wallet.game_type,
	wallet.status,
	wallet.wave,
	agent.terminal_retrieved
ORDER BY
    date_created ASC
'''
wallet_trans_df_ = lotto_db_conn(agent_wallet_transaction)

# Query database
agent_funding = '''
SELECT
	count(funding.id) AS id_count,
	funding.created_at::date AS created_at,
	agent.lotto_agent_id,
	agent.user_id,
	agent.supervisor_id,
	sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
	sup.supervisor_name,
	sup.supervisor_phone,
	sup.supervisor_email,
	agent.agent_name,
	agent_num,
	agent_mail,
	agent.agent_type,
	agent.performance_status,
    SUM(funding.amount) AS amount,
	funding.source,
	funding.is_verified,
	funding.verification_status
FROM
	pos_app_agentfundingtable AS funding
LEFT JOIN (
	SELECT
		id AS lotto_agent_id,
		supervisor_id,
		user_id,
		CONCAT(first_name, ' ', last_name) AS agent_name,
		phone AS agent_num,
		email AS agent_mail,
		agent_type,
		performance_status,
		terminal_id,
		terminal_retrieved
	FROM 
		public.pos_app_agent
) AS agent
ON 
	funding.agent_id = agent.lotto_agent_id
LEFT JOIN (
	SELECT
		id,
		vertical_lead_id,
		full_name AS supervisor_name,
		phone AS supervisor_phone,
		email AS supervisor_email
	FROM 
		public.pos_app_supervisor
) AS sup
ON
	sup.id = agent.supervisor_id
LEFT JOIN (
	SELECT
		id,
		CONCAT(first_name, ' ', last_name) AS vertical_full_name,
		phone AS vertical_contact,
		email AS vertical_email,
		address AS vertical_address
	FROM
		public.pos_app_lottoverticallead
) AS vert
ON
	sup.vertical_lead_id = vert.id
WHERE
	agent.agent_type = 'LOTTO_AGENT'
GROUP BY
	funding.created_at::date,
	agent.lotto_agent_id,
	agent.user_id,
	agent.supervisor_id,
	sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
	sup.supervisor_name,
	sup.supervisor_phone,
	sup.supervisor_email,
	agent.agent_name,
	agent_num,
	agent_mail,
	agent.agent_type,
	agent.performance_status,
	funding.source,
	funding.is_verified,
	funding.verification_status
ORDER BY 
    created_at ASC
'''
fund_agent_df_ = lotto_db_conn(agent_funding)

# Query database
payout_transactions = '''
SELECT
	count(payout.id) AS id_count,
    agent.lotto_agent_id,
    agent.user_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    agent.agent_name,
    agent_num,
    agent_mail,
    agent.agent_type,
    agent.performance_status,
    payout.name,
    payout.date_added::date AS date_added,
    payout.status,
    payout.channel,
    payout.source,
    -- payout.game_play_id,
    -- payout.date_won::date AS date_won,
    SUM(payout.amount) AS amount
FROM
    main_payouttransactiontable AS payout
LEFT JOIN (
    SELECT
        id AS lotto_agent_id,
        supervisor_id,
        user_id,
        CONCAT(first_name, ' ', last_name) AS agent_name,
        phone AS agent_num,
        email AS agent_mail,
        agent_type,
        performance_status,
        terminal_id,
        terminal_retrieved
    FROM 
        public.pos_app_agent
) AS agent
    ON payout.phone = agent.agent_num
LEFT JOIN (
    SELECT
        id,
        vertical_lead_id,
        full_name AS supervisor_name,
        phone AS supervisor_phone,
        email AS supervisor_email
    FROM 
        public.pos_app_supervisor
) AS sup
    ON sup.id = agent.supervisor_id
LEFT JOIN (
	SELECT
		id,
		CONCAT(first_name, ' ', last_name) AS vertical_full_name,
		phone AS vertical_contact,
		email AS vertical_email,
		address AS vertical_address
	FROM
		public.pos_app_lottoverticallead
) AS vert
ON
	sup.vertical_lead_id = vert.id
WHERE
    agent.agent_type = 'LOTTO_AGENT'
    AND payout.channel = 'POS'
    AND payout.source = 'BUDDY'
    AND payout.game_play_id IS NOT NULL
GROUP BY
    agent.lotto_agent_id,
    agent.user_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    agent.agent_name,
    agent_num,
    agent_mail,
    agent.agent_type,
    agent.performance_status,
    payout.name,
    payout.date_added::date,
    payout.status,
    payout.channel,
    payout.source
    -- payout.game_play_id,
	-- payout.date_won::date
ORDER BY
    date_added ASC
'''
payout_df_ = lotto_db_conn(payout_transactions)

# # Query database
# remittance = '''
# SELECT
#   remit.id,
# 	agent.lotto_agent_id,
# 	agent.user_id,
# 	agent.supervisor_id,
# 	sup.vertical_lead_id,
	# vert.vertical_full_name,
	# vert.vertical_contact,
	# vert.vertical_email,
	# vert.vertical_address,
# 	sup.supervisor_name,
# 	sup.supervisor_phone,
# 	sup.supervisor_email,
# 	agent.agent_name,
# 	agent_num,
# 	agent_mail,
# 	agent.agent_type,
# 	agent.performance_status,
# 	remit.created_at,
# 	remit.amount,
# 	remit.amount_paid,
# 	remit.remitted,
# 	remit.days_due,
# 	remit.due
# FROM
#     pos_app_lottoagentremittancetable as remit
# LEFT JOIN (
# 	SELECT
# 		id AS lotto_agent_id,
# 		supervisor_id,
# 		user_id,
# 		CONCAT(first_name, ' ', last_name) AS agent_name,
# 		phone AS agent_num,
# 		email AS agent_mail,
# 		agent_type,
# 		performance_status,
# 		terminal_id,
# 		terminal_retrieved
# 	FROM 
# 		public.pos_app_agent
# ) AS agent
# ON 
# 	remit.agent_id = agent.lotto_agent_id
# LEFT JOIN (
# 	SELECT
# 		id,
# 		vertical_lead_id,
# 		full_name AS supervisor_name,
# 		phone AS supervisor_phone,
# 		email AS supervisor_email
# 	FROM 
# 		public.pos_app_supervisor
# ) AS sup
# ON
# 	sup.id = agent.supervisor_id
# LEFT JOIN (
# 	SELECT
# 		id,
# 		CONCAT(first_name, ' ', last_name) AS vertical_full_name,
# 		phone AS vertical_contact,
# 		email AS vertical_email,
# 		address AS vertical_address
# 	FROM
# 		public.pos_app_lottoverticallead
# ) AS vert
# ON
# 	sup.vertical_lead_id = vert.id
# WHERE
# 	agent.agent_type = 'LOTTO_AGENT'
# ORDER BY
# 	remit.created_at ASC
# '''
# remittance_df_ = lotto_db_conn(remittance)

# Query database
africa_lotto = '''
SELECT
	count(africa.id) AS id_count,
    agent.lotto_agent_id,
    agent.user_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    africa.agent_name,
    agent_num,
    agent_mail,
    agent.agent_type,
    agent.performance_status,
    -- africa.game_play_id,
    africa.created_at::date AS created_at,
    africa.channel,
    -- africa.lottery_type,
    africa.game_type,
    africa.paid,
    africa.number_of_ticket,
    africa.seeder_status,
    SUM(africa.rtp) AS rtp,
    SUM(africa.rto) AS rto
FROM
    africa_lotto_africalotto AS africa
LEFT JOIN (
    SELECT
        id AS lotto_agent_id,
        supervisor_id,
        user_id,
        phone AS agent_num,
        email AS agent_mail,
        agent_type,
        performance_status,
        terminal_id,
        terminal_retrieved
    FROM 
        public.pos_app_agent
) AS agent
    ON africa.agent_phone_number = agent.agent_num
LEFT JOIN (
    SELECT
        id,
        vertical_lead_id,
        full_name AS supervisor_name,
        phone AS supervisor_phone,
        email AS supervisor_email
    FROM 
        public.pos_app_supervisor
) AS sup
    ON sup.id = agent.supervisor_id
LEFT JOIN (
	SELECT
		id,
		CONCAT(first_name, ' ', last_name) AS vertical_full_name,
		phone AS vertical_contact,
		email AS vertical_email,
		address AS vertical_address
	FROM
		public.pos_app_lottoverticallead
) AS vert
ON
	sup.vertical_lead_id = vert.id    
WHERE
    agent.agent_type = 'LOTTO_AGENT'
GROUP BY
    agent.lotto_agent_id,
    agent.user_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    africa.agent_name,
    agent_num,
    agent_mail,
    agent.agent_type,
    agent.performance_status,
    -- africa.game_play_id,
    africa.created_at::date,
    africa.channel,
    -- africa.lottery_type,
    africa.game_type,
    africa.paid,
    africa.number_of_ticket,
    africa.seeder_status
ORDER BY 
    created_at ASC
'''
africa_lotto_df_ = lotto_db_conn(africa_lotto)

# Query database
lotto_ticket = '''
SELECT
	count(tick.id) AS id_count,
    agent.lotto_agent_id,
    agent.user_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    agent.agent_name,
    agent_num,
    agent_mail,
    agent.agent_type,
    agent.performance_status,
    tick.date::date AS date,
    SUM(tick.stake_amount) AS stake_amount,
    SUM(tick.potential_winning) AS potential_winning,
    SUM(tick.expected_amount) AS expected_amount,
    SUM(tick.amount_paid) AS amount_paid,
    tick.paid,
    SUM(tick.rtp) AS rtp,
    SUM(tick.rto) AS rto,
    tick.number_of_ticket,
    tick.channel,
    -- tick.game_play_id,
    tick.lottery_type,
    tick.service_type,
    tick.telco_network
FROM
    main_lottoticket AS tick
LEFT JOIN (
    SELECT
        id AS lotto_agent_id,
        supervisor_id,
        user_id,
        CONCAT(first_name, ' ', last_name) AS agent_name,
        phone AS agent_num,
        email AS agent_mail,
        agent_type,
        performance_status,
        terminal_id,
        terminal_retrieved
    FROM 
        public.pos_app_agent
) AS agent
    ON tick.agent_profile_id = agent.lotto_agent_id
LEFT JOIN (
    SELECT
        id,
        vertical_lead_id,
        full_name AS supervisor_name,
        phone AS supervisor_phone,
        email AS supervisor_email
    FROM 
        public.pos_app_supervisor
) AS sup
    ON sup.id = agent.supervisor_id
LEFT JOIN (
	SELECT
		id,
		CONCAT(first_name, ' ', last_name) AS vertical_full_name,
		phone AS vertical_contact,
		email AS vertical_email,
		address AS vertical_address
	FROM
		public.pos_app_lottoverticallead
) AS vert
ON
	sup.vertical_lead_id = vert.id
WHERE
    agent.agent_type = 'LOTTO_AGENT'
GROUP BY
    agent.lotto_agent_id,
    agent.user_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    agent.agent_name,
    agent_num,
    agent_mail,
    agent.agent_type,
    agent.performance_status,
    tick.date::date,
    tick.paid,
    tick.number_of_ticket,
    tick.channel,
    -- tick.game_play_id,
    tick.lottery_type,
    tick.service_type,
    tick.telco_network
ORDER BY 
    date ASC
'''
lotto_ticket_df_ = lotto_db_conn(lotto_ticket)

# Query database
lottery_model = '''
SELECT
	count(model.id) AS id_count,
    agent.lotto_agent_id,
    agent.user_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    agent.agent_name,
    agent_num,
    agent_mail,
    agent.agent_type,
    agent.performance_status,
    model.date::date AS date,
    SUM(model.band) AS band,
    SUM(model.stake_amount) AS stake_amount,
    model.paid,
    -- model.paid_date,
    SUM(model.expected_amount) AS expected_amount,
    SUM(model.amount_paid) AS amount_paid,
    SUM(model.rto) AS rto,
    SUM(model.rtp) AS rtp,
    model.lottery_type,
    model.channel,
    -- model.game_play_id,
    model.telco_network
FROM
    main_lotterymodel AS model
LEFT JOIN (
    SELECT
        id AS lotto_agent_id,
        supervisor_id,
        user_id,
        CONCAT(first_name, ' ', last_name) AS agent_name,
        phone AS agent_num,
        email AS agent_mail,
        agent_type,
        performance_status,
        terminal_id,
        terminal_retrieved
    FROM 
        public.pos_app_agent
) AS agent
    ON model.agent_profile_id = agent.lotto_agent_id
LEFT JOIN (
    SELECT
        id,
        vertical_lead_id,
        full_name AS supervisor_name,
        phone AS supervisor_phone,
        email AS supervisor_email
    FROM 
        public.pos_app_supervisor
) AS sup
    ON sup.id = agent.supervisor_id
LEFT JOIN (
	SELECT
		id,
		CONCAT(first_name, ' ', last_name) AS vertical_full_name,
		phone AS vertical_contact,
		email AS vertical_email,
		address AS vertical_address
	FROM
		public.pos_app_lottoverticallead
) AS vert
ON
	sup.vertical_lead_id = vert.id
WHERE
    agent.agent_type = 'LOTTO_AGENT'
GROUP BY
    agent.lotto_agent_id,
    agent.user_id,
    agent.supervisor_id,
    sup.vertical_lead_id,
	vert.vertical_full_name,
	vert.vertical_contact,
	vert.vertical_email,
	vert.vertical_address,
    sup.supervisor_name,
    sup.supervisor_phone,
    sup.supervisor_email,
    agent.agent_name,
    agent_num,
    agent_mail,
    agent.agent_type,
    agent.performance_status,
    model.date::date,
    model.paid,
    -- model.paid_date,
    model.lottery_type,
    model.channel,
    -- model.game_play_id,
    model.telco_network
ORDER BY 
    date ASC
'''
lottery_model_df_ = lotto_db_conn(lottery_model)

# Query dataset
agents = '''
SELECT 
    * 
FROM 
	public.pos_app_agent
WHERE
    agent_type = 'LOTTO_AGENT'
'''
agent_df_ = lotto_db_conn(agents)

# Query dataset
holiday_df = '''
SELECT 
 
    * 
FROM 
    loans_holiday 
'''
holiday_ = sql_connection(holiday_df)

# Query database
repayment_query = '''
SELECT
    agent_id,
    ajo_loan_id,
    paid_date,
    repayment_amount as repayment_by_date,
    repayment_type
FROM loans_ajoloanrepayment
'''
repayment_df_ = sql_connection(repayment_query)

# Query database
loans_repayment_query = '''
SELECT 
    loans.id,
    loans.borrower_id,
	loans.agent_id,
	agents.customer_user_id,
	agents.agent_full_name,
	agents.agent_email,
	agents.agent_contact,
    loans.guarantor_phone_number,
	CONCAT(first_name, ' ', last_name) AS users_full_name,
    users.phone_number AS users_phone_number,
    users.address AS users_address,
    users.lga AS users_lga,
    users.state AS users_state,
    users.trade AS users_trade,
    loans.date_disbursed,
	loans.start_date,
    loans.end_date,
    loans.tenor,
    loans.tenor_in_days,
    loans.status,
    loans.loan_type,
    loans.amount,
    loans.amount_disbursed,
    loans.amount_saved,
	loans.interest_rate,
    loans.interest_amount,
    loans.daily_repayment_amount,
    repayment.repayment,
    loans.repayment_amount AS expected_repayment,
    loans.due_today_amount
FROM public.loans_ajoloan AS loans
LEFT JOIN (
    SELECT
        ajo_loan_id,
        SUM(repayment_amount) AS repayment 
    FROM 
        public.loans_ajoloanrepayment
    GROUP BY
        ajo_loan_id
) AS repayment
ON
    loans.id = repayment.ajo_loan_id
LEFT JOIN (
	SELECT
		id,
		phone_number,
		first_name,
		last_name,
		address,
        state,
		lga,
		trade
	FROM 
		public.ajo_ajouser
) AS users
ON 
	loans.borrower_id = users.id
LEFT JOIN (
	SELECT 
		id,
		customer_user_id,
		CONCAT(first_name, ' ', last_name) AS agent_full_name,
		email AS agent_email,
		user_phone AS agent_contact
FROM 
	public.accounts_customuser
) AS agents
ON
	loans.agent_id = agents.id
WHERE 
    loans.status IN ('COMPLETED', 'OPEN', 'PAST_MATURITY')
ORDER BY loans.id ASC
'''
loans_df_ = sql_connection(loans_repayment_query)

def clear_cache():
    st.cache_data.clear()
    st.cache_resource.clear()
